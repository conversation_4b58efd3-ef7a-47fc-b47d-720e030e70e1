"""
🎯 高级开仓引擎提示词系统 - 智能猎手 2.0
专注于高概率交易机会识别和智能风险管理
"""

class AdvancedOpeningEnginePrompt:
    """高级开仓引擎提示词生成器"""
    
    @staticmethod
    def create_dynamic_opening_prompt() -> str:
        """创建动态自适应的开仓决策提示词"""
        return """
🎯 **智能量化猎手 - 专业开仓决策系统**

你是一位拥有15年实战经验的顶级量化交易专家，专精于在复杂市场环境中识别高概率交易机会。

## 🧠 **核心使命**
在严格风控框架下，通过多维度分析识别最优入场时机，确保每笔交易都有明确的概率优势。

## 📊 **智能分析框架**

### **1. 市场环境识别**
**趋势市场 (Trending)**：主要周期方向一致，ADX>25，明确方向性
- 策略：顺势而为，寻找回调入场点
- 风险：假突破，趋势反转

**震荡市场 (Ranging)**：价格在区间内运行，ADX<20，支撑阻力明显
- 策略：区间边界交易，高抛低吸
- 风险：区间突破，方向性变化

**突破市场 (Breakout)**：关键位突破，成交量放大，动能强劲
- 策略：突破确认后跟进，严格止损
- 风险：假突破回落，流动性陷阱

**不确定市场 (Uncertain)**：信号混乱，数据不完整，波动异常
- 策略：保守观望，降低仓位
- 风险：盲目交易，信息不足

### **2. 多维度机会评估**

**技术面分析 (Technical)**：
- 多周期趋势一致性 (1H→30m→15m)
- 关键技术指标共振 (RSI+MACD+BB)
- 支撑阻力位有效性
- 价格形态完整度

**资金面分析 (Volume)**：
- 成交量变化趋势 (放量/缩量)
- 量价关系验证 (量价齐升/背离)
- 资金流向强度 (大额交易监控)
- 流动性风险评估

**情绪面分析 (Sentiment)**：
- 市场恐慌/贪婪指数
- 短期波动率异常
- 多空力量对比
- 市场参与度评估

### **3. 智能置信度模型**

**数据完整性评分 (Data Quality Score)**：
- 完整数据: +0.2 置信度
- 部分缺失: -0.1 置信度  
- 严重缺失: -0.3 置信度

**信号强度评分 (Signal Strength Score)**：
- 强烈信号: +0.3 置信度
- 中等信号: +0.1 置信度
- 弱信号: -0.1 置信度
- 混合信号: -0.2 置信度

**市场环境调整 (Market Environment Adjustment)**：
- 趋势清晰: +0.1 置信度
- 震荡明确: +0.05 置信度
- 突破确认: +0.15 置信度
- 环境不明: -0.2 置信度

### **4. 动态风险控制**

**基础风险参数**：
- 最大单笔亏损: 2%
- 最大日内回撤: 5%
- 最大杠杆倍数: 20x
- 最小置信度: 0.6

**置信度与风险映射**：
- 0.9+ 高置信: 杠杆15-20x, 止损1.5%, 止盈3-5%
- 0.8-0.9 较高: 杠杆10-15x, 止损2%, 止盈4-6%
- 0.7-0.8 中等: 杠杆5-10x, 止损2.5%, 止盈5-8%
- 0.6-0.7 较低: 杠杆3-5x, 止损3%, 止盈6-10%
- <0.6 不足: 观望，不开仓

## 🎯 **决策流程**

### **步骤1：环境识别**
分析当前市场属于哪种环境，确定适用策略

### **步骤2：机会评估**  
从技术、资金、情绪三个维度评估交易机会质量

### **步骤3：置信度计算**
基于数据质量、信号强度、环境因子计算综合置信度

### **步骤4：风险配置**
根据置信度动态设置杠杆、止损、止盈参数

### **步骤5：决策输出**
给出明确的BUY/SELL/HOLD决策和详细推理

## 📋 **严格输出格式**

```json
{{
    "market_environment": "trending|ranging|breakout|uncertain",
    "decision": "BUY|SELL|HOLD",
    "confidence": 0.0-1.0,
    "reasoning": "基于三维分析的详细推理，包含关键判断依据",
    "technical_score": 0.0-1.0,
    "volume_score": 0.0-1.0,
    "sentiment_score": 0.0-1.0,
    "data_quality": 0.0-1.0,
    "risk_factors": ["factor1", "factor2"],
    "opportunity_factors": ["factor1", "factor2"]
}}
```

## **实时市场数据**
交易对: {symbol}
当前价格: {current_price}
24h变化: {change_24h}%
市场波动率: {volatility}%

## **技术分析数据**
{indicators}

## **交易约束**
- 仅基于提供的实际数据进行分析
- 数据不完整时必须降低置信度
- 低于0.6置信度时必须选择HOLD
- 必须提供清晰的风险因子识别

**开始专业分析...**
"""

    @staticmethod
    def create_market_specific_prompt(market_environment: str) -> str:
        """根据市场环境创建特定的提示词补充"""
        
        market_specific_guidance = {
            "trending": """
### **趋势市场专项策略**
- 优先寻找主趋势方向的回调机会
- 关注趋势强度指标ADX和动量指标
- 避免逆势交易，即使技术指标超买超卖
- 设置移动止损，跟随趋势发展
            """,
            
            "ranging": """
### **震荡市场专项策略** 
- 重点关注支撑阻力位的有效性
- 利用RSI极值区域寻找反转机会
- 严格控制止损，防止区间突破亏损
- 考虑双向交易机会，灵活调整方向
            """,
            
            "breakout": """
### **突破市场专项策略**
- 等待突破确认，避免假突破陷阱
- 重点验证成交量配合情况
- 设置较紧止损，确认突破失败时快速止损
- 关注突破后的持续性和回踩确认
            """,
            
            "uncertain": """
### **不确定市场专项策略**
- 提高风险警惕，降低仓位配置
- 等待更明确的信号出现
- 缩短持仓时间，快进快出
- 重点保护资金安全，机会次之
            """
        }
        
        return market_specific_guidance.get(market_environment, "")

    @staticmethod
    def format_market_data_for_analysis(indicators: dict, market_data: dict) -> str:
        """格式化市场数据用于AI分析"""
        
        try:
            lines = []
            timeframe_signals = indicators.get('timeframe_signals', {})
            
            if not timeframe_signals:
                return "⚠️ 技术指标数据不完整，建议降低置信度或观望"
            
            # 多周期技术分析表
            lines.append("**多周期技术分析表**")
            lines.append("```")
            lines.append("周期  收盘价    RSI    MACD   ADX   BB位置  成交量比  趋势信号")
            lines.append("----  -------   ---    ----   ---   -----   -------  -------")
            
            trend_consistency = 0
            volume_anomaly = []
            
            for tf in ['1H', '30m', '15m', '5m', '1m']:
                if tf in timeframe_signals:
                    data = timeframe_signals[tf]
                    
                    # 基础数据
                    close = data.get('close', 0)
                    rsi = data.get('rsi', 50)
                    macd = data.get('macd', 0)
                    macd_signal = data.get('macd_signal', 0)
                    adx = data.get('adx', 20)
                    bb_pos = data.get('bb_position', 0.5)
                    volume = data.get('volume', 0)
                    volume_ma20 = data.get('volume_ma_20', 0)
                    
                    # 成交量比率
                    if volume_ma20 > 0:
                        vol_ratio = volume / volume_ma20
                        vol_ratio_str = f"{vol_ratio:.1f}x"
                        
                        # 检测成交量异常
                        if vol_ratio > 2.0:
                            volume_anomaly.append(f"{tf}:放量{vol_ratio:.1f}x")
                        elif vol_ratio < 0.5:
                            volume_anomaly.append(f"{tf}:缩量{vol_ratio:.1f}x")
                    else:
                        vol_ratio_str = "N/A"
                    
                    # 趋势信号
                    if macd > macd_signal and rsi > 50:
                        trend_signal = "看涨"
                        trend_consistency += 1
                    elif macd < macd_signal and rsi < 50:
                        trend_signal = "看跌" 
                        trend_consistency -= 1
                    else:
                        trend_signal = "中性"
                    
                    lines.append(f"{tf:4s}  {close:8.1f}  {rsi:5.1f}  {macd:6.2f} {adx:5.1f}  {bb_pos:5.2f}  {vol_ratio_str:>7}  {trend_signal}")
            
            lines.append("```")
            
            # 市场环境评估
            lines.append("\n**市场环境评估**")
            
            # 趋势一致性
            if trend_consistency >= 3:
                lines.append(f"🔼 **趋势一致性**: 多周期看涨 ({trend_consistency}/5)")
            elif trend_consistency <= -3:
                lines.append(f"🔽 **趋势一致性**: 多周期看跌 ({trend_consistency}/5)")
            else:
                lines.append(f"➡️ **趋势一致性**: 信号分歧 ({trend_consistency}/5)")
            
            # 成交量异常
            if volume_anomaly:
                lines.append(f"📊 **成交量异常**: {' | '.join(volume_anomaly)}")
            
            # 关键价位分析
            current_price = market_data.get('last_price', 0)
            lines.append(f"\n**关键价位分析**")
            lines.append(f"当前价格: {current_price}")
            
            # 24小时变化分析
            change_24h = market_data.get('change_24h', 0)
            if abs(change_24h) > 5:
                lines.append(f"⚠️ **24h大幅波动**: {change_24h:+.2f}% (需要谨慎评估)")
            
            return "\n".join(lines)
            
        except Exception as e:
            return f"数据格式化失败: {e}，建议使用保守策略"