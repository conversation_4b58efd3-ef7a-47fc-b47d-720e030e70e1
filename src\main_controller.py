"""
主控制器模块 - Main Controller
系统的核心控制器，协调所有模块的运行
使用schedule库进行任务调度
"""

import schedule
import time
import signal
import sys
import os
import threading
from typing import Dict, List, Optional, Any
import logging
from datetime import datetime
# OKX API由各模块自行管理，不在主控制器中初始化
from .config_manager import ConfigManager
from .indicator_calculator import IndicatorCalculator
from .multi_contract_manager import MultiContractManager
from .logger_monitor import LoggerMonitor
from .web_dashboard import WebDashboard

# 临时使用deprecated模块，直到unified_trading_service恢复
from .deprecated.data_fetcher import DataFetcher
from .deprecated.trade_executor import TradeExecutor
from .risk_manager import RiskManager

class MainController:
    """主控制器 - 系统核心控制器"""
    
    def __init__(self):
        """初始化主控制器"""
        # 系统状态
        self.is_running = False
        self.is_initialized = False
        
        # 模块实例
        self.config_manager = None
        self.data_fetcher = None
        self.indicator_calculator = None
        # decision_engine已移除，使用专门引擎
        self.trade_executor = None
        self.risk_manager = None
        self.multi_contract_manager = None
        self.logger_monitor = None
        self.web_dashboard = None
        
        # OKX客户端
        self.okx_client = None
        
        # 调度器
        self.scheduler = schedule
        
        # 主线程
        self.main_thread = None
        
        # 信号处理
        self._setup_signal_handlers()
        
        # 初始化临时日志（在logger_monitor创建之前）
        import logging
        logging.basicConfig(level=logging.INFO, format='[临时日志] %(levelname)s - %(message)s')
        temp_logger = logging.getLogger(__name__)
        temp_logger.info("🤖 DeepSeek加密货币量化交易系统 - 主控制器初始化")
    
    def initialize(self) -> bool:
        """初始化所有模块"""
        try:
            temp_logger = logging.getLogger(__name__)
            temp_logger.info("📋 开始初始化系统模块...")
            
            # 1. 初始化配置管理器
            temp_logger.info("1️⃣ 初始化配置管理器...")
            self.config_manager = ConfigManager()
            
            # 2. 初始化日志监控器
            temp_logger.info("2️⃣ 初始化日志监控器...")
            self.logger_monitor = LoggerMonitor(self.config_manager)
            self.logger_monitor.start_monitoring()
            
            # 设置统一日志系统
            from .logger_monitor import setup_unified_logging, get_unified_logger
            setup_unified_logging()
            
            # 获取统一logger
            self.logger = get_unified_logger(__name__)
            self.logger.info("主控制器开始初始化")
            
            # 3. 跳过OKX客户端初始化（各模块自行初始化）
            self.logger.info("3️⃣ 跳过OKX客户端初始化（各模块自行管理API连接）")
            
            # 4. 初始化数据获取器
            self.logger.info("4️⃣ 初始化数据获取器...")
            self.data_fetcher = DataFetcher(self.config_manager)

            # 5. 初始化指标计算器
            self.logger.info("5️⃣ 初始化指标计算器...")
            self.indicator_calculator = IndicatorCalculator(self.config_manager)

            # 6. 决策引擎已移除，使用专门的开仓和持仓监控引擎
            self.logger.info("6️⃣ 将使用专门的开仓和持仓监控引擎...")

            # 7. 延迟初始化交易执行器（需要API密钥）
            self.logger.info("7️⃣ 交易执行器将在API配置完成后初始化...")
            self.trade_executor = None

            # 8. 初始化风险管理器
            self.logger.info("8️⃣ 初始化风险管理器...")
            self.risk_manager = RiskManager(self.config_manager)
            
            # 9. 延迟初始化多合约管理器（需要决策引擎和交易执行器）
            self.logger.info("9️⃣ 多合约管理器将在API配置完成后初始化...")
            self.multi_contract_manager = None
            
            # 10. 初始化Web仪表盘（支持延迟加载的模块）
            self.logger.info("🔟 初始化Web仪表盘...")
            self.web_dashboard = WebDashboard(
                self.config_manager,
                self.data_fetcher,
                None,  # decision_engine已移除
                self.trade_executor,   # 可能为None
                self.risk_manager,
                self.multi_contract_manager,
                self.logger_monitor
            )
            
            # 跳过模板创建（使用手动创建的模板）
            # self.web_dashboard.create_templates()
            
            # 设置定时任务
            self._setup_scheduled_tasks()
            
            self.is_initialized = True
            self.logger.info("✅ 系统初始化完成！")
            
            return True
            
        except Exception as e:
            error_msg = f"系统初始化失败: {e}"
            if hasattr(self, 'logger'):
                self.logger.error(f"❌ {error_msg}")
            else:
                temp_logger.error(f"❌ {error_msg}")
            return False

    def initialize_api_modules(self) -> bool:
        """在API配置完成后初始化需要API的模块"""
        try:
            self.logger.info("开始初始化API相关模块...")

            # 决策引擎已移除，使用专门引擎
            self.logger.info("🧠 使用专门的开仓和持仓监控引擎...")

            # 初始化交易执行器
            if self.trade_executor is None:
                self.logger.info("⚡ 初始化交易执行器...")
                self.trade_executor = TradeExecutor(self.config_manager)
                self.logger.info("交易执行器初始化完成")

            # 初始化多合约管理器
            if self.multi_contract_manager is None:
                self.logger.info("🔄 初始化多合约管理器...")
                self.multi_contract_manager = MultiContractManager(
                    self.config_manager,
                    self.data_fetcher,
                    self.indicator_calculator,
                    self.trade_executor,
                    self.risk_manager
                )
                self.logger.info("多合约管理器初始化完成")

            # 更新Web仪表盘的模块引用
            if self.web_dashboard:
                # decision_engine已移除
                self.web_dashboard.trade_executor = self.trade_executor
                self.web_dashboard.multi_contract_manager = self.multi_contract_manager
                self.logger.info("Web仪表盘模块引用已更新")

            self.logger.info("API相关模块初始化完成")
            return True

        except Exception as e:
            error_msg = f"API模块初始化失败: {e}"
            self.logger.error(f"❌ {error_msg}")
            return False

    # OKX客户端初始化已移除，各模块自行管理API连接

    def _setup_scheduled_tasks(self) -> None:
        """设置定时任务"""
        try:
            # 每分钟检查系统状态
            self.scheduler.every(1).minutes.do(self._check_system_health)
            
            # 每5分钟清理缓存
            self.scheduler.every(5).minutes.do(self._cleanup_cache)
            
            # 每小时生成报告
            self.scheduler.every().hour.do(self._generate_hourly_report)
            
            # 每天凌晨2点重置统计
            self.scheduler.every().day.at("02:00").do(self._daily_reset)
            
            self.logger.info("定时任务设置完成")
            
        except Exception as e:
            self.logger.error(f"设置定时任务失败: {e}")
    
    def _setup_signal_handlers(self) -> None:
        """设置信号处理器"""
        def signal_handler(signum, frame):
            # 如果logger已初始化，使用logger，否则使用临时logger
            if hasattr(self, 'logger') and self.logger:
                self.logger.warning(f"🛑 接收到信号 {signum}，正在优雅关闭系统...")
            else:
                temp_logger = logging.getLogger(__name__)
                temp_logger.warning(f"🛑 接收到信号 {signum}，正在优雅关闭系统...")
            self.stop()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def start(self, web_port: int = 5000, debug_mode: bool = True) -> None:
        """启动系统"""
        try:
            if not self.is_initialized:
                self.logger.error("❌ 系统未初始化，请先调用 initialize() 方法")
                return

            if self.is_running:
                self.logger.warning("⚠️ 系统已在运行")
                return

            self.logger.info("🚀 启动DeepSeek加密货币量化交易系统...")
            if debug_mode:
                self.logger.info("🔧 开发模式：启用热重载功能")

            self.is_running = True

            # 启动Web仪表盘
            self.logger.info(f"🌐 启动Web仪表盘: http://127.0.0.1:{web_port}")
            self.web_dashboard.start(port=web_port, debug=debug_mode)
            
            # 启动多合约管理器
            self.logger.info("📊 启动多合约管理器...")
            self.multi_contract_manager.start()
            
            # 启动主循环
            self.logger.info("⚡ 启动主控制循环...")
            self._start_main_loop()
            
            self.logger.info("✅ 系统启动完成！")
            self.logger.info(f"📱 Web界面: http://127.0.0.1:{web_port}")
            self.logger.info("🔄 系统正在运行，按 Ctrl+C 停止...")
            
        except Exception as e:
            error_msg = f"系统启动失败: {e}"
            self.logger.error(f"❌ {error_msg}")
            self.is_running = False
    
    def _start_main_loop(self) -> None:
        """启动主循环"""
        def main_loop():
            try:
                while self.is_running:
                    try:
                        # 运行定时任务
                        self.scheduler.run_pending()
                        
                        # 休眠
                        time.sleep(1)
                        
                    except Exception as e:
                        self.logger.error(f"主循环异常: {e}")
                        time.sleep(5)
                        
            except Exception as e:
                self.logger.error(f"主循环失败: {e}")
        
        self.main_thread = threading.Thread(target=main_loop, daemon=True)
        self.main_thread.start()
    
    def stop(self) -> None:
        """停止系统"""
        try:
            if not self.is_running:
                self.logger.warning("⚠️ 系统未在运行")
                return
            
            self.logger.info("🛑 正在停止系统...")
            
            self.is_running = False
            
            # 停止多合约管理器
            if self.multi_contract_manager:
                self.logger.info("📊 停止多合约管理器...")
                self.multi_contract_manager.stop()
            
            # 停止Web仪表盘
            if self.web_dashboard:
                self.logger.info("🌐 停止Web仪表盘...")
                self.web_dashboard.stop()
            
            # 停止日志监控
            if self.logger_monitor:
                self.logger.info("📝 停止日志监控...")
                self.logger_monitor.stop_monitoring()
            
            # 清理资源
            self._cleanup_resources()
            
            self.logger.info("✅ 系统已停止")
                
        except Exception as e:
            error_msg = f"停止系统失败: {e}"
            if hasattr(self, 'logger'):
                self.logger.error(f"❌ {error_msg}")
            else:
                temp_logger = logging.getLogger(__name__)
                temp_logger.error(f"❌ {error_msg}")
    
    def _check_system_health(self) -> None:
        """检查系统健康状态"""
        try:
            health_status = {
                'timestamp': datetime.now().isoformat(),
                'modules': {}
            }
            
            # 检查各模块状态
            modules = [
                ('config_manager', self.config_manager),
                ('data_fetcher', self.data_fetcher),
                # ('decision_engine', 已移除),
                ('trade_executor', self.trade_executor),
                ('risk_manager', self.risk_manager),
                ('multi_contract_manager', self.multi_contract_manager),
                ('logger_monitor', self.logger_monitor),
                ('web_dashboard', self.web_dashboard)
            ]
            
            for module_name, module_instance in modules:
                if module_instance:
                    health_status['modules'][module_name] = 'HEALTHY'
                else:
                    health_status['modules'][module_name] = 'MISSING'
            
            # 检查多合约管理器运行状态
            if self.multi_contract_manager:
                health_status['trading_active'] = self.multi_contract_manager.is_running
            
            # 记录健康检查日志
            self.logger.debug(f"系统健康检查: {health_status}")
            
        except Exception as e:
            self.logger.warning(f"系统健康检查失败: {e}")
    
    def _cleanup_cache(self) -> None:
        """清理缓存"""
        try:
            if self.data_fetcher:
                self.data_fetcher.clear_cache()
            
            self.logger.debug("缓存清理完成")
            
        except Exception as e:
            self.logger.warning(f"清理缓存失败: {e}")
    
    def _generate_hourly_report(self) -> None:
        """生成小时报告"""
        try:
            report = {
                'timestamp': datetime.now().isoformat(),
                'system_status': 'RUNNING' if self.is_running else 'STOPPED',
                'performance_metrics': {},
                'risk_summary': {},
                'trade_summary': {}
            }
            
            # 获取性能指标
            if self.multi_contract_manager:
                report['performance_metrics'] = self.multi_contract_manager.get_performance_metrics()
            
            # 获取风险摘要
            if self.risk_manager:
                report['risk_summary'] = self.risk_manager.get_risk_summary()
            
            # 获取交易摘要
            if self.trade_executor:
                report['trade_summary'] = self.trade_executor.get_trade_statistics()
            
            self.logger.info(f"小时报告: {report}")
            
        except Exception as e:
            self.logger.warning(f"生成小时报告失败: {e}")
    
    def _daily_reset(self) -> None:
        """每日重置"""
        try:
            self.logger.info("执行每日重置任务")
            
            # 这里可以添加每日重置的逻辑
            # 例如：重置统计计数器、清理旧数据等
            
        except Exception as e:
            self.logger.warning(f"每日重置失败: {e}")
    
    def _cleanup_resources(self) -> None:
        """清理资源"""
        try:
            # 清理数据缓存
            if self.data_fetcher:
                self.data_fetcher.clear_cache()
            
            # 清理调度任务
            self.scheduler.clear()
            
            self.logger.debug("资源清理完成")
            
        except Exception as e:
            self.logger.warning(f"清理资源失败: {e}")
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        try:
            status = {
                'is_running': self.is_running,
                'is_initialized': self.is_initialized,
                'timestamp': datetime.now().isoformat(),
                'modules': {}
            }
            
            # 检查模块状态
            modules = [
                ('config_manager', self.config_manager),
                ('data_fetcher', self.data_fetcher),
                # ('decision_engine', 已移除),
                ('trade_executor', self.trade_executor),
                ('risk_manager', self.risk_manager),
                ('multi_contract_manager', self.multi_contract_manager),
                ('logger_monitor', self.logger_monitor),
                ('web_dashboard', self.web_dashboard)
            ]
            
            for module_name, module_instance in modules:
                status['modules'][module_name] = module_instance is not None
            
            return status
            
        except Exception as e:
            self.logger.error(f"获取系统状态失败: {e}")
            return {'error': str(e)}
    
    def run_interactive(self, debug_mode: bool = True) -> None:
        """交互式运行"""
        try:
            if not self.initialize():
                self.logger.error("❌ 初始化失败，退出程序")
                return

            # 启动Web界面
            self.logger.info("🌐 启动Web界面...")
            if self.web_dashboard:
                # 在后台线程启动Web服务器（关闭调试模式避免信号问题）
                web_thread = threading.Thread(
                    target=self.web_dashboard.run,
                    kwargs={'debug': False, 'host': '0.0.0.0', 'port': 5000},
                    daemon=True
                )
                web_thread.start()
                self.logger.info("✅ Web界面已启动: http://localhost:5000")
                self.logger.info("📝 请在Web界面中配置API密钥后，系统将自动开始交易")

            # 等待API配置并启动交易系统
            self._wait_for_api_config_and_start()

            # 保持主线程运行
            try:
                while True:
                    time.sleep(1)
                    # 检查是否需要初始化API模块
                    if not self.is_running and self._check_api_configured():
                        if self.initialize_api_modules():
                            self.start(debug_mode=debug_mode)
            except KeyboardInterrupt:
                self.logger.info("\n🛑 接收到中断信号，正在停止系统...")
                self.stop()
                
        except Exception as e:
            error_msg = f"运行失败: {e}"
            if hasattr(self, 'logger'):
                self.logger.error(f"❌ {error_msg}")
            else:
                temp_logger = logging.getLogger(__name__)
                temp_logger.error(f"❌ {error_msg}")

    def _check_api_configured(self) -> bool:
        """检查API是否已配置"""
        try:
            # 检查OKX API配置（正确的路径）
            okx_config = self.config_manager.get('credentials.okx', {})
            if not all([okx_config.get('api_key'), okx_config.get('api_secret'), okx_config.get('passphrase')]):
                return False

            # 检查DeepSeek API配置（正确的路径）
            deepseek_config = self.config_manager.get('credentials.deepseek', {})
            if not deepseek_config.get('api_key'):
                return False

            return True
        except Exception as e:
            self.logger.debug(f"API配置检查失败: {e}")
            return False

    def _wait_for_api_config_and_start(self):
        """等待API配置完成并启动系统"""
        self.logger.info("⏳ 等待API配置...")
        while True:
            if self._check_api_configured():
                self.logger.info("✅ API配置检测完成，正在启动交易系统...")
                if self.initialize_api_modules():
                    self.start(debug_mode=True)
                    break
                else:
                    self.logger.warning("❌ API模块初始化失败，继续等待...")
            time.sleep(5)  # 每5秒检查一次


def main():
    """主函数"""
    # 设置临时日志用于主函数
    import logging
    logging.basicConfig(level=logging.INFO, format='[临时日志] %(levelname)s - %(message)s')
    temp_logger = logging.getLogger(__name__)
    
    temp_logger.info("🤖 DeepSeek加密货币量化交易系统")
    temp_logger.info("=" * 50)
    
    # 创建主控制器
    controller = MainController()
    
    # 交互式运行
    controller.run_interactive()


if __name__ == "__main__":
    main()
