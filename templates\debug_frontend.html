<!DOCTYPE html>
<html>
<head>
    <title>前端调试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
    </style>
</head>
<body>
    <h1>🔍 前端调试页面</h1>
    
    <div class="debug-section">
        <h3>💰 账户概览测试</h3>
        <div>总资产: <span id="total-balance">--</span> USDT</div>
        <div>可用余额: <span id="available-balance">--</span> USDT</div>
        <div>今日盈亏: <span id="daily-pnl">--</span> USDT</div>
        <div>总盈亏: <span id="total-pnl">--</span> USDT</div>
        
        <button onclick="testAccountAPI()" style="margin-top: 10px;">测试账户API</button>
        <button onclick="updateAccountData()" style="margin-top: 10px;">手动更新数据</button>
    </div>
    
    <div class="debug-section">
        <h3>📋 调试日志</h3>
        <div id="debug-log" style="height: 300px; overflow-y: scroll; background: #f5f5f5; padding: 10px;"></div>
        <button onclick="clearLog()">清除日志</button>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            logDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('debug-log').innerHTML = '';
        }

        async function testAccountAPI() {
            log('🔍 开始测试账户API...', 'info');
            
            try {
                const response = await fetch('/api/account/balance');
                log(`📡 API响应状态: ${response.status}`, response.ok ? 'success' : 'error');
                
                if (response.ok) {
                    const data = await response.json();
                    log(`📊 API响应数据: ${JSON.stringify(data, null, 2)}`, 'success');
                    
                    // 检查数据字段
                    const fields = ['total_balance', 'available_balance', 'daily_pnl', 'total_pnl'];
                    fields.forEach(field => {
                        if (field in data) {
                            log(`✅ 字段 ${field}: ${data[field]}`, 'success');
                        } else {
                            log(`❌ 缺少字段: ${field}`, 'error');
                        }
                    });
                    
                    // 自动更新页面数据
                    updateAccountOverview(data);
                    log('🔄 页面数据已更新', 'success');
                    
                } else {
                    const errorText = await response.text();
                    log(`❌ API错误: ${errorText}`, 'error');
                }
                
            } catch (error) {
                log(`❌ 请求失败: ${error.message}`, 'error');
            }
        }

        function updateAccountOverview(data) {
            log('🔄 开始更新账户概览...', 'info');
            
            const fields = {
                'total-balance': data.total_balance,
                'available-balance': data.available_balance,
                'daily-pnl': data.daily_pnl,
                'total-pnl': data.total_pnl
            };
            
            for (const [elementId, value] of Object.entries(fields)) {
                const element = document.getElementById(elementId);
                if (element) {
                    const oldValue = element.textContent;
                    element.textContent = value || '--';
                    log(`📝 更新 ${elementId}: "${oldValue}" → "${element.textContent}"`, 'info');
                } else {
                    log(`❌ 找不到元素: ${elementId}`, 'error');
                }
            }
        }

        function updateAccountData() {
            log('🔄 手动更新测试数据...', 'info');
            
            const testData = {
                total_balance: '130857.56',
                available_balance: '130800.01',
                daily_pnl: '--',
                total_pnl: '--'
            };
            
            updateAccountOverview(testData);
        }

        // 页面加载完成后自动测试
        window.addEventListener('load', function() {
            log('🚀 页面加载完成，开始自动测试...', 'info');
            setTimeout(testAccountAPI, 1000);
        });

        // 定期刷新数据
        setInterval(function() {
            log('⏰ 定期刷新数据...', 'info');
            testAccountAPI();
        }, 10000); // 每10秒刷新一次
    </script>
</body>
</html>
