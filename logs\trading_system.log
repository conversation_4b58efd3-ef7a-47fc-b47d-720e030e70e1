2025-07-27 08:55:46,633 - src.logger_monitor - INFO - 日志监控器初始化完成
2025-07-27 08:55:46,635 - src.logger_monitor - INFO - 日志监控启动成功
2025-07-27 08:55:46,636 - src.main_controller - INFO - 主控制器开始初始化
2025-07-27 08:55:46,637 - src.deprecated.data_fetcher - INFO - 🔄 尝试连接OKX域名: https://www.okx.com
2025-07-27 08:55:48,085 - httpx - INFO - HTTP Request: GET https://www.okx.com/api/v5/public/time "HTTP/2 200 OK"
2025-07-27 08:55:48,086 - src.deprecated.data_fetcher - INFO - 🟢 成功连接OKX域名: https://www.okx.com
2025-07-27 08:55:48,087 - src.deprecated.data_fetcher - INFO - 🔧 API超时和连接池配置完成
2025-07-27 08:55:48,499 - httpx - INFO - HTTP Request: GET https://www.okx.com/api/v5/public/time "HTTP/2 200 OK"
2025-07-27 08:55:48,501 - src.deprecated.data_fetcher - INFO - 🟢 OKX连接健康检查通过
2025-07-27 08:55:48,502 - src.deprecated.data_fetcher - INFO - 🟢 OKX客户端初始化成功 - 使用域名: https://www.okx.com
2025-07-27 08:55:48,502 - src.indicator_calculator - INFO - 指标参数加载完成
2025-07-27 08:55:48,503 - src.risk_manager - INFO - 🔧 最终风险参数: {'max_leverage': 5.0, 'stop_loss_pct': 0.02, 'take_profit_pct': 0.04, 'max_position_ratio': 0.05, 'max_daily_loss_usdt': 0.05, 'min_balance_usdt': 10.0, 'safety_margin_ratio': 0.1, 'max_concurrent_positions': 3, 'emergency_stop_loss_pct': 0.15}
2025-07-27 08:55:48,504 - src.risk_manager - INFO - 🛡️ 现代化风控系统初始化完成
2025-07-27 08:55:48,505 - src.risk_manager - INFO - 📋 风控参数: {'max_leverage': 5.0, 'stop_loss_pct': 0.02, 'take_profit_pct': 0.04, 'max_position_ratio': 0.05, 'max_daily_loss_usdt': 0.05, 'min_balance_usdt': 10.0, 'safety_margin_ratio': 0.1, 'max_concurrent_positions': 3, 'emergency_stop_loss_pct': 0.15}
2025-07-27 08:55:48,514 - src.web_dashboard - INFO - Web仪表盘初始化完成
2025-07-27 08:55:48,515 - src.main_controller - INFO - 定时任务设置完成
2025-07-27 08:55:48,516 - src.main_controller - INFO - 所有模块初始化完成
2025-07-27 08:55:48,517 - src.web_dashboard - INFO - 启动Web服务器: http://0.0.0.0:5000
2025-07-27 08:55:48,518 - src.main_controller - INFO - 开始初始化API相关模块...
2025-07-27 08:55:48,519 - src.deprecated.trade_executor - INFO - 🔗 初始化交易API - 使用域名: https://www.okx.com
2025-07-27 08:55:48,548 - src.deprecated.trade_executor - INFO - 🔧 交易API增强超时配置完成: 连接15s, 读取45s, 连接池30个
2025-07-27 08:55:53,220 - httpx - INFO - HTTP Request: GET https://www.okx.com/api/v5/public/time "HTTP/2 200 OK"
2025-07-27 08:55:53,221 - src.deprecated.trade_executor - INFO - ✅ 交易API初始化完成，连接健康检查通过
2025-07-27 08:55:53,241 - src.deprecated.trade_executor - INFO - 交易执行器初始化完成
2025-07-27 08:55:53,242 - src.main_controller - INFO - 交易执行器初始化完成
2025-07-27 08:55:53,404 - src.optimized_prompts.smart_hunter_engine - INFO - 智能猎手引擎 - API密钥加载成功
2025-07-27 08:55:53,405 - src.optimized_prompts.smart_hunter_engine - INFO - 智能猎手引擎初始化完成
2025-07-27 08:55:53,405 - src.optimized_prompts.risk_guardian_engine - INFO - 风险守护者引擎 - API密钥加载成功
2025-07-27 08:55:53,405 - src.optimized_prompts.risk_guardian_engine - INFO - 风险守护者引擎初始化完成
2025-07-27 08:55:53,406 - src.multi_contract_manager - INFO - 专门决策引擎初始化成功
2025-07-27 08:55:53,406 - src.multi_contract_manager - INFO - 获取到3个活跃交易对: ['BTC-USDT-SWAP', 'ETH-USDT-SWAP', 'DOGE-USDT-SWAP']
2025-07-27 08:55:53,407 - src.multi_contract_manager - INFO - 成功初始化了3个交易对
2025-07-27 08:55:53,407 - src.multi_contract_manager - INFO - 多合约管理器初始化完成
2025-07-27 08:55:53,408 - src.main_controller - INFO - 多合约管理器初始化完成
2025-07-27 08:55:53,408 - src.main_controller - INFO - Web仪表盘模块引用已更新
2025-07-27 08:55:53,409 - src.main_controller - INFO - API相关模块初始化完成
2025-07-27 08:55:53,409 - src.web_dashboard - INFO - Web仪表盘启动成功: http://127.0.0.1:5000
2025-07-27 08:55:54,223 - src.logger_monitor - INFO - 日志监控器初始化完成
2025-07-27 08:55:54,225 - src.logger_monitor - INFO - 日志监控启动成功
2025-07-27 08:55:54,225 - src.main_controller - INFO - 主控制器开始初始化
2025-07-27 08:55:54,226 - src.deprecated.data_fetcher - INFO - 🔄 尝试连接OKX域名: https://www.okx.com
2025-07-27 08:55:56,289 - httpx - INFO - HTTP Request: GET https://www.okx.com/api/v5/public/time "HTTP/2 200 OK"
2025-07-27 08:55:56,290 - src.deprecated.data_fetcher - INFO - 🟢 成功连接OKX域名: https://www.okx.com
2025-07-27 08:55:56,291 - src.deprecated.data_fetcher - INFO - 🔧 API超时和连接池配置完成
2025-07-27 08:55:56,831 - httpx - INFO - HTTP Request: GET https://www.okx.com/api/v5/public/time "HTTP/2 200 OK"
2025-07-27 08:55:56,832 - src.deprecated.data_fetcher - INFO - 🟢 OKX连接健康检查通过
2025-07-27 08:55:56,832 - src.deprecated.data_fetcher - INFO - 🟢 OKX客户端初始化成功 - 使用域名: https://www.okx.com
2025-07-27 08:55:56,833 - src.indicator_calculator - INFO - 指标参数加载完成
2025-07-27 08:55:56,834 - src.risk_manager - INFO - 🔧 最终风险参数: {'max_leverage': 5.0, 'stop_loss_pct': 0.02, 'take_profit_pct': 0.04, 'max_position_ratio': 0.05, 'max_daily_loss_usdt': 0.05, 'min_balance_usdt': 10.0, 'safety_margin_ratio': 0.1, 'max_concurrent_positions': 3, 'emergency_stop_loss_pct': 0.15}
2025-07-27 08:55:56,835 - src.risk_manager - INFO - 🛡️ 现代化风控系统初始化完成
2025-07-27 08:55:56,835 - src.risk_manager - INFO - 📋 风控参数: {'max_leverage': 5.0, 'stop_loss_pct': 0.02, 'take_profit_pct': 0.04, 'max_position_ratio': 0.05, 'max_daily_loss_usdt': 0.05, 'min_balance_usdt': 10.0, 'safety_margin_ratio': 0.1, 'max_concurrent_positions': 3, 'emergency_stop_loss_pct': 0.15}
2025-07-27 08:55:56,845 - src.web_dashboard - INFO - Web仪表盘初始化完成
2025-07-27 08:55:56,847 - src.main_controller - INFO - 定时任务设置完成
2025-07-27 08:55:56,847 - src.main_controller - INFO - 所有模块初始化完成
2025-07-27 08:55:56,849 - src.web_dashboard - INFO - 启动Web服务器: http://0.0.0.0:5000
2025-07-27 08:55:56,849 - src.main_controller - INFO - 开始初始化API相关模块...
2025-07-27 08:55:56,850 - src.deprecated.trade_executor - INFO - 🔗 初始化交易API - 使用域名: https://www.okx.com
2025-07-27 08:55:56,880 - src.deprecated.trade_executor - INFO - 🔧 交易API增强超时配置完成: 连接15s, 读取45s, 连接池30个
2025-07-27 08:55:59,166 - httpx - INFO - HTTP Request: GET https://www.okx.com/api/v5/public/time "HTTP/2 200 OK"
2025-07-27 08:55:59,167 - src.deprecated.trade_executor - INFO - ✅ 交易API初始化完成，连接健康检查通过
2025-07-27 08:55:59,186 - src.deprecated.trade_executor - INFO - 交易执行器初始化完成
2025-07-27 08:55:59,187 - src.main_controller - INFO - 交易执行器初始化完成
2025-07-27 08:55:59,258 - src.optimized_prompts.smart_hunter_engine - INFO - 智能猎手引擎 - API密钥加载成功
2025-07-27 08:55:59,260 - src.optimized_prompts.smart_hunter_engine - INFO - 智能猎手引擎初始化完成
2025-07-27 08:55:59,260 - src.optimized_prompts.risk_guardian_engine - INFO - 风险守护者引擎 - API密钥加载成功
2025-07-27 08:55:59,261 - src.optimized_prompts.risk_guardian_engine - INFO - 风险守护者引擎初始化完成
2025-07-27 08:55:59,261 - src.multi_contract_manager - INFO - 专门决策引擎初始化成功
2025-07-27 08:55:59,261 - src.multi_contract_manager - INFO - 获取到3个活跃交易对: ['BTC-USDT-SWAP', 'ETH-USDT-SWAP', 'DOGE-USDT-SWAP']
2025-07-27 08:55:59,262 - src.multi_contract_manager - INFO - 成功初始化了3个交易对
2025-07-27 08:55:59,262 - src.multi_contract_manager - INFO - 多合约管理器初始化完成
2025-07-27 08:55:59,263 - src.main_controller - INFO - 多合约管理器初始化完成
2025-07-27 08:55:59,263 - src.main_controller - INFO - Web仪表盘模块引用已更新
2025-07-27 08:55:59,264 - src.main_controller - INFO - API相关模块初始化完成
2025-07-27 08:55:59,264 - src.web_dashboard - INFO - Web仪表盘启动成功: http://127.0.0.1:5000
2025-07-27 08:56:18,305 - httpx - INFO - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
2025-07-27 08:56:18,339 - httpx - INFO - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
2025-07-27 08:56:18,340 - src.deprecated.data_fetcher - INFO - 🔍 原始持仓数据 DOGE-USDT-SWAP: {'adl': '1', 'availPos': '1.52', 'avgPx': '0.23585', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '0.**************', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.179246', 'fundingFee': '0.********', 'gammaBS': '', 'gammaPA': '', 'idxPx': '0.235581', 'imr': '49.742', 'instId': 'DOGE-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '0.2356', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '86.**************', 'margin': '', 'markPx': '0.23562', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '52381.************', 'mmr': '2.3279256', 'nonSettleAvgPx': '', 'notionalUsd': '358.*********', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '1.52', 'posCcy': '', 'posId': '2720240594715480064', 'posSide': 'short', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.14345912', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '*********', 'uTime': '*************', 'upl': '0.****************', 'uplLastPx': '0.****************', 'uplRatio': '0.****************', 'uplRatioLastPx': '0.****************', 'usdPx': '1.00031', 'vegaBS': '', 'vegaPA': ''}
2025-07-27 08:56:18,341 - src.deprecated.data_fetcher - INFO - 🔍 使用OKX保证金: DOGE-USDT-SWAP = 49.74 USDT (来源: imr)
2025-07-27 08:56:18,761 - httpx - INFO - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
2025-07-27 08:56:19,191 - httpx - INFO - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
2025-07-27 08:56:19,193 - src.deprecated.trade_executor - INFO - 设置初始余额基准: 131320.43 USDT
2025-07-27 08:56:19,194 - src.deprecated.trade_executor - INFO - 设置今日余额基准: 131320.43 USDT
2025-07-27 08:56:59,888.888 - INFO     - [logger_monitor.py:83] - 日志监控器初始化完成
2025-07-27 08:56:59,889.889 - INFO     - [logger_monitor.py:203] - 日志监控启动成功
2025-07-27 08:56:59,890.890 - INFO     - [main_controller.py:78] - 主控制器开始初始化
2025-07-27 08:56:59,891.891 - INFO     - [data_fetcher.py:86] - 🔄 尝试连接OKX域名: https://www.okx.com
2025-07-27 08:57:02,088.088 - INFO     - [_client.py:1025] - HTTP Request: GET https://www.okx.com/api/v5/public/time "HTTP/2 200 OK"
2025-07-27 08:57:02,090.090 - INFO     - [data_fetcher.py:111] - 🟢 成功连接OKX域名: https://www.okx.com
2025-07-27 08:57:02,091.091 - INFO     - [data_fetcher.py:203] - 🔧 API超时和连接池配置完成
2025-07-27 08:57:02,647.647 - INFO     - [_client.py:1025] - HTTP Request: GET https://www.okx.com/api/v5/public/time "HTTP/2 200 OK"
2025-07-27 08:57:02,649.649 - INFO     - [data_fetcher.py:214] - 🟢 OKX连接健康检查通过
2025-07-27 08:57:02,649.649 - INFO     - [data_fetcher.py:134] - 🟢 OKX客户端初始化成功 - 使用域名: https://www.okx.com
2025-07-27 08:57:02,650.650 - INFO     - [indicator_calculator.py:59] - 指标参数加载完成
2025-07-27 08:57:02,651.651 - INFO     - [risk_manager.py:77] - 🔧 最终风险参数: {'max_leverage': 5.0, 'stop_loss_pct': 0.02, 'take_profit_pct': 0.04, 'max_position_ratio': 0.05, 'max_daily_loss_usdt': 0.05, 'min_balance_usdt': 10.0, 'safety_margin_ratio': 0.1, 'max_concurrent_positions': 3, 'emergency_stop_loss_pct': 0.15}
2025-07-27 08:57:02,652.652 - INFO     - [risk_manager.py:43] - 🛡️ 现代化风控系统初始化完成
2025-07-27 08:57:02,653.653 - INFO     - [risk_manager.py:44] - 📋 风控参数: {'max_leverage': 5.0, 'stop_loss_pct': 0.02, 'take_profit_pct': 0.04, 'max_position_ratio': 0.05, 'max_daily_loss_usdt': 0.05, 'min_balance_usdt': 10.0, 'safety_margin_ratio': 0.1, 'max_concurrent_positions': 3, 'emergency_stop_loss_pct': 0.15}
2025-07-27 08:57:02,662.662 - INFO     - [web_dashboard.py:65] - Web仪表盘初始化完成
2025-07-27 08:57:02,663.663 - INFO     - [main_controller.py:196] - 定时任务设置完成
2025-07-27 08:57:02,664.664 - INFO     - [main_controller.py:125] - 所有模块初始化完成
2025-07-27 08:57:02,665.665 - INFO     - [web_dashboard.py:1508] - 启动Web服务器: http://0.0.0.0:5000
2025-07-27 08:57:02,666.666 - INFO     - [main_controller.py:140] - 开始初始化API相关模块...
2025-07-27 08:57:02,667.667 - INFO     - [trade_executor.py:327] - 🔗 初始化交易API - 使用域名: https://www.okx.com
2025-07-27 08:57:02,697.697 - INFO     - [trade_executor.py:404] - 🔧 交易API增强超时配置完成: 连接15s, 读取45s, 连接池30个
2025-07-27 08:57:05,181.181 - INFO     - [_client.py:1025] - HTTP Request: GET https://www.okx.com/api/v5/public/time "HTTP/2 200 OK"
2025-07-27 08:57:05,183.183 - INFO     - [trade_executor.py:361] - ✅ 交易API初始化完成，连接健康检查通过
2025-07-27 08:57:05,203.203 - INFO     - [trade_executor.py:91] - 交易执行器初始化完成
2025-07-27 08:57:05,204.204 - INFO     - [main_controller.py:149] - 交易执行器初始化完成
2025-07-27 08:57:05,279.279 - INFO     - [smart_hunter_engine.py:65] - 智能猎手引擎 - API密钥加载成功
2025-07-27 08:57:05,279.279 - INFO     - [smart_hunter_engine.py:41] - 智能猎手引擎初始化完成
2025-07-27 08:57:05,280.280 - INFO     - [risk_guardian_engine.py:65] - 风险守护者引擎 - API密钥加载成功
2025-07-27 08:57:05,280.280 - INFO     - [risk_guardian_engine.py:41] - 风险守护者引擎初始化完成
2025-07-27 08:57:05,281.281 - INFO     - [multi_contract_manager.py:50] - 专门决策引擎初始化成功
2025-07-27 08:57:05,281.281 - INFO     - [multi_contract_manager.py:99] - 获取到3个活跃交易对: ['BTC-USDT-SWAP', 'ETH-USDT-SWAP', 'DOGE-USDT-SWAP']
2025-07-27 08:57:05,282.282 - INFO     - [multi_contract_manager.py:122] - 成功初始化了3个交易对
2025-07-27 08:57:05,282.282 - INFO     - [multi_contract_manager.py:83] - 多合约管理器初始化完成
2025-07-27 08:57:05,282.282 - INFO     - [main_controller.py:161] - 多合约管理器初始化完成
2025-07-27 08:57:05,283.283 - INFO     - [main_controller.py:168] - Web仪表盘模块引用已更新
2025-07-27 08:57:05,284.284 - INFO     - [main_controller.py:170] - API相关模块初始化完成
2025-07-27 08:57:05,284.284 - INFO     - [web_dashboard.py:1240] - Web仪表盘启动成功: http://127.0.0.1:5000
2025-07-27 08:57:18,505.505 - INFO     - [_client.py:1025] - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
2025-07-27 08:57:18,978.978 - INFO     - [_client.py:1025] - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
2025-07-27 08:57:19,528.528 - INFO     - [_client.py:1025] - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
2025-07-27 08:57:19,531.531 - INFO     - [trade_executor.py:2539] - 设置初始余额基准: 131319.82 USDT
2025-07-27 08:57:19,531.531 - INFO     - [trade_executor.py:2546] - 设置今日余额基准: 131319.82 USDT
2025-07-27 08:57:20,230.230 - INFO     - [_client.py:1025] - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
2025-07-27 08:57:20,233.233 - INFO     - [data_fetcher.py:521] - 🔍 原始持仓数据 DOGE-USDT-SWAP: {'adl': '1', 'availPos': '1.52', 'avgPx': '0.23585', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '0.**************', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.179246', 'fundingFee': '0.********', 'gammaBS': '', 'gammaPA': '', 'idxPx': '0.235967', 'imr': '49.**************', 'instId': 'DOGE-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '0.236', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '86.**************', 'margin': '', 'markPx': '0.23603', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '52290.************', 'mmr': '2.3319764', 'nonSettleAvgPx': '', 'notionalUsd': '358.********', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '1.52', 'posCcy': '', 'posId': '2720240594715480064', 'posSide': 'short', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.14345912', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '*********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.****************', 'uplRatio': '-0.****************', 'uplRatioLastPx': '-0.****************', 'usdPx': '1.0003', 'vegaBS': '', 'vegaPA': ''}
2025-07-27 08:57:20,233.233 - INFO     - [data_fetcher.py:530] - 🔍 使用OKX保证金: DOGE-USDT-SWAP = 49.83 USDT (来源: imr)
2025-07-27 08:58:17,651.651 - INFO     - [_client.py:1025] - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
2025-07-27 08:58:18,014.014 - INFO     - [_client.py:1025] - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
2025-07-27 08:58:18,351.351 - INFO     - [_client.py:1025] - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
2025-07-27 08:58:18,352.352 - INFO     - [data_fetcher.py:521] - 🔍 原始持仓数据 DOGE-USDT-SWAP: {'adl': '1', 'availPos': '1.52', 'avgPx': '0.23585', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '0.**************', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.179246', 'fundingFee': '0.********', 'gammaBS': '', 'gammaPA': '', 'idxPx': '0.236173', 'imr': '49.**************', 'instId': 'DOGE-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '0.23635', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '86.**************', 'margin': '', 'markPx': '0.23632', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '52225.************', 'mmr': '2.3348416', 'nonSettleAvgPx': '', 'notionalUsd': '359.**************', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '1.52', 'posCcy': '', 'posId': '2720240594715480064', 'posSide': 'short', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.14345912', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '*********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.****************', 'uplRatio': '-0.****************', 'uplRatioLastPx': '-0.****************', 'usdPx': '1.00031', 'vegaBS': '', 'vegaPA': ''}
2025-07-27 08:58:18,353.353 - INFO     - [data_fetcher.py:530] - 🔍 使用OKX保证金: DOGE-USDT-SWAP = 49.89 USDT (来源: imr)
2025-07-27 08:58:18,385.385 - INFO     - [_client.py:1025] - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 08:58:49,152.152 - INFO     - src.logger_monitor - 日志监控器初始化完成
[CryptoQuant] 2025-07-27 08:58:49,154.154 - INFO     - src.logger_monitor - 日志监控启动成功
[CryptoQuant] 2025-07-27 08:58:49,154.154 - INFO     - src.main_controller - 主控制器开始初始化
[CryptoQuant] 2025-07-27 08:58:49,156.156 - INFO     - src.deprecated.data_fetcher - 🔄 尝试连接OKX域名: https://www.okx.com
[CryptoQuant] 2025-07-27 08:58:51,826.826 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/time "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 08:58:51,827.827 - INFO     - src.deprecated.data_fetcher - 🟢 成功连接OKX域名: https://www.okx.com
[CryptoQuant] 2025-07-27 08:58:51,828.828 - INFO     - src.deprecated.data_fetcher - 🔧 API超时和连接池配置完成
[CryptoQuant] 2025-07-27 08:58:52,469.469 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/time "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 08:58:52,471.471 - INFO     - src.deprecated.data_fetcher - 🟢 OKX连接健康检查通过
[CryptoQuant] 2025-07-27 08:58:52,472.472 - INFO     - src.deprecated.data_fetcher - 🟢 OKX客户端初始化成功 - 使用域名: https://www.okx.com
[CryptoQuant] 2025-07-27 08:58:52,473.473 - INFO     - src.indicator_calculator - 指标参数加载完成
[CryptoQuant] 2025-07-27 08:58:52,473.473 - INFO     - src.risk_manager - 🔧 最终风险参数: {'max_leverage': 5.0, 'stop_loss_pct': 0.02, 'take_profit_pct': 0.04, 'max_position_ratio': 0.05, 'max_daily_loss_usdt': 0.05, 'min_balance_usdt': 10.0, 'safety_margin_ratio': 0.1, 'max_concurrent_positions': 3, 'emergency_stop_loss_pct': 0.15}
[CryptoQuant] 2025-07-27 08:58:52,474.474 - INFO     - src.risk_manager - 🛡️ 现代化风控系统初始化完成
[CryptoQuant] 2025-07-27 08:58:52,475.475 - INFO     - src.risk_manager - 📋 风控参数: {'max_leverage': 5.0, 'stop_loss_pct': 0.02, 'take_profit_pct': 0.04, 'max_position_ratio': 0.05, 'max_daily_loss_usdt': 0.05, 'min_balance_usdt': 10.0, 'safety_margin_ratio': 0.1, 'max_concurrent_positions': 3, 'emergency_stop_loss_pct': 0.15}
[CryptoQuant] 2025-07-27 08:58:52,485.485 - INFO     - src.web_dashboard - Web仪表盘初始化完成
[CryptoQuant] 2025-07-27 08:58:52,485.485 - INFO     - src.main_controller - 定时任务设置完成
[CryptoQuant] 2025-07-27 08:58:52,486.486 - INFO     - src.main_controller - 所有模块初始化完成
[CryptoQuant] 2025-07-27 08:58:52,487.487 - INFO     - src.web_dashboard - 启动Web服务器: http://0.0.0.0:5000
[CryptoQuant] 2025-07-27 08:58:52,488.488 - INFO     - src.main_controller - 开始初始化API相关模块...
[CryptoQuant] 2025-07-27 08:58:52,489.489 - INFO     - src.deprecated.trade_executor - 🔗 初始化交易API - 使用域名: https://www.okx.com
[CryptoQuant] 2025-07-27 08:58:52,518.518 - INFO     - src.deprecated.trade_executor - 🔧 交易API增强超时配置完成: 连接15s, 读取45s, 连接池30个
[CryptoQuant] 2025-07-27 08:58:54,157.157 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/time "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 08:58:54,159.159 - INFO     - src.deprecated.trade_executor - ✅ 交易API初始化完成，连接健康检查通过
[CryptoQuant] 2025-07-27 08:58:54,194.194 - INFO     - src.deprecated.trade_executor - 交易执行器初始化完成
[CryptoQuant] 2025-07-27 08:58:54,200.200 - INFO     - src.main_controller - 交易执行器初始化完成
[CryptoQuant] 2025-07-27 08:58:54,327.327 - INFO     - src.optimized_prompts.smart_hunter_engine - 智能猎手引擎 - API密钥加载成功
[CryptoQuant] 2025-07-27 08:58:54,328.328 - INFO     - src.optimized_prompts.smart_hunter_engine - 智能猎手引擎初始化完成
[CryptoQuant] 2025-07-27 08:58:54,328.328 - INFO     - src.optimized_prompts.risk_guardian_engine - 风险守护者引擎 - API密钥加载成功
[CryptoQuant] 2025-07-27 08:58:54,328.328 - INFO     - src.optimized_prompts.risk_guardian_engine - 风险守护者引擎初始化完成
[CryptoQuant] 2025-07-27 08:58:54,329.329 - INFO     - src.multi_contract_manager - 专门决策引擎初始化成功
[CryptoQuant] 2025-07-27 08:58:54,329.329 - INFO     - src.multi_contract_manager - 获取到3个活跃交易对: ['BTC-USDT-SWAP', 'ETH-USDT-SWAP', 'DOGE-USDT-SWAP']
[CryptoQuant] 2025-07-27 08:58:54,330.330 - INFO     - src.multi_contract_manager - 成功初始化了3个交易对
[CryptoQuant] 2025-07-27 08:58:54,330.330 - INFO     - src.multi_contract_manager - 多合约管理器初始化完成
[CryptoQuant] 2025-07-27 08:58:54,331.331 - INFO     - src.main_controller - 多合约管理器初始化完成
[CryptoQuant] 2025-07-27 08:58:54,331.331 - INFO     - src.main_controller - Web仪表盘模块引用已更新
[CryptoQuant] 2025-07-27 08:58:54,332.332 - INFO     - src.main_controller - API相关模块初始化完成
[CryptoQuant] 2025-07-27 08:58:54,332.332 - INFO     - src.web_dashboard - Web仪表盘启动成功: http://127.0.0.1:5000
[CryptoQuant] 2025-07-27 08:59:06,980.980 - INFO     - src.logger_monitor - 日志监控器初始化完成
[CryptoQuant] 2025-07-27 08:59:06,981.981 - INFO     - src.logger_monitor - 日志监控启动成功
[CryptoQuant] 2025-07-27 08:59:06,982.982 - INFO     - src.main_controller - 主控制器开始初始化
[CryptoQuant] 2025-07-27 08:59:06,983.983 - INFO     - src.deprecated.data_fetcher - 🔄 尝试连接OKX域名: https://www.okx.com
[CryptoQuant] 2025-07-27 08:59:09,409.409 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/time "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 08:59:09,410.410 - INFO     - src.deprecated.data_fetcher - 🟢 成功连接OKX域名: https://www.okx.com
[CryptoQuant] 2025-07-27 08:59:09,411.411 - INFO     - src.deprecated.data_fetcher - 🔧 API超时和连接池配置完成
[CryptoQuant] 2025-07-27 08:59:09,943.943 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/time "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 08:59:09,945.945 - INFO     - src.deprecated.data_fetcher - 🟢 OKX连接健康检查通过
[CryptoQuant] 2025-07-27 08:59:09,945.945 - INFO     - src.deprecated.data_fetcher - 🟢 OKX客户端初始化成功 - 使用域名: https://www.okx.com
[CryptoQuant] 2025-07-27 08:59:09,947.947 - INFO     - src.indicator_calculator - 指标参数加载完成
[CryptoQuant] 2025-07-27 08:59:09,948.948 - INFO     - src.risk_manager - 🔧 最终风险参数: {'max_leverage': 5.0, 'stop_loss_pct': 0.02, 'take_profit_pct': 0.04, 'max_position_ratio': 0.05, 'max_daily_loss_usdt': 0.05, 'min_balance_usdt': 10.0, 'safety_margin_ratio': 0.1, 'max_concurrent_positions': 3, 'emergency_stop_loss_pct': 0.15}
[CryptoQuant] 2025-07-27 08:59:09,949.949 - INFO     - src.risk_manager - 🛡️ 现代化风控系统初始化完成
[CryptoQuant] 2025-07-27 08:59:09,950.950 - INFO     - src.risk_manager - 📋 风控参数: {'max_leverage': 5.0, 'stop_loss_pct': 0.02, 'take_profit_pct': 0.04, 'max_position_ratio': 0.05, 'max_daily_loss_usdt': 0.05, 'min_balance_usdt': 10.0, 'safety_margin_ratio': 0.1, 'max_concurrent_positions': 3, 'emergency_stop_loss_pct': 0.15}
[CryptoQuant] 2025-07-27 08:59:09,965.965 - INFO     - src.web_dashboard - Web仪表盘初始化完成
[CryptoQuant] 2025-07-27 08:59:09,966.966 - INFO     - src.main_controller - 定时任务设置完成
[CryptoQuant] 2025-07-27 08:59:09,967.967 - INFO     - src.main_controller - 所有模块初始化完成
[CryptoQuant] 2025-07-27 08:59:09,968.968 - INFO     - src.web_dashboard - 启动Web服务器: http://0.0.0.0:5000
[CryptoQuant] 2025-07-27 08:59:09,969.969 - INFO     - src.main_controller - 开始初始化API相关模块...
[CryptoQuant] 2025-07-27 08:59:09,971.971 - INFO     - src.deprecated.trade_executor - 🔗 初始化交易API - 使用域名: https://www.okx.com
[CryptoQuant] 2025-07-27 08:59:10,006.006 - INFO     - src.deprecated.trade_executor - 🔧 交易API增强超时配置完成: 连接15s, 读取45s, 连接池30个
[CryptoQuant] 2025-07-27 08:59:11,407.407 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/time "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 08:59:11,409.409 - INFO     - src.deprecated.trade_executor - ✅ 交易API初始化完成，连接健康检查通过
[CryptoQuant] 2025-07-27 08:59:11,429.429 - INFO     - src.deprecated.trade_executor - 交易执行器初始化完成
[CryptoQuant] 2025-07-27 08:59:11,430.430 - INFO     - src.main_controller - 交易执行器初始化完成
[CryptoQuant] 2025-07-27 08:59:11,513.513 - INFO     - src.optimized_prompts.smart_hunter_engine - 智能猎手引擎 - API密钥加载成功
[CryptoQuant] 2025-07-27 08:59:11,513.513 - INFO     - src.optimized_prompts.smart_hunter_engine - 智能猎手引擎初始化完成
[CryptoQuant] 2025-07-27 08:59:11,514.514 - INFO     - src.optimized_prompts.risk_guardian_engine - 风险守护者引擎 - API密钥加载成功
[CryptoQuant] 2025-07-27 08:59:11,514.514 - INFO     - src.optimized_prompts.risk_guardian_engine - 风险守护者引擎初始化完成
[CryptoQuant] 2025-07-27 08:59:11,515.515 - INFO     - src.multi_contract_manager - 专门决策引擎初始化成功
[CryptoQuant] 2025-07-27 08:59:11,515.515 - INFO     - src.multi_contract_manager - 获取到3个活跃交易对: ['BTC-USDT-SWAP', 'ETH-USDT-SWAP', 'DOGE-USDT-SWAP']
[CryptoQuant] 2025-07-27 08:59:11,516.516 - INFO     - src.multi_contract_manager - 成功初始化了3个交易对
[CryptoQuant] 2025-07-27 08:59:11,516.516 - INFO     - src.multi_contract_manager - 多合约管理器初始化完成
[CryptoQuant] 2025-07-27 08:59:11,516.516 - INFO     - src.main_controller - 多合约管理器初始化完成
[CryptoQuant] 2025-07-27 08:59:11,517.517 - INFO     - src.main_controller - Web仪表盘模块引用已更新
[CryptoQuant] 2025-07-27 08:59:11,517.517 - INFO     - src.main_controller - API相关模块初始化完成
[CryptoQuant] 2025-07-27 08:59:11,518.518 - INFO     - src.web_dashboard - Web仪表盘启动成功: http://127.0.0.1:5000
[CryptoQuant] 2025-07-27 08:59:18,488.488 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 08:59:19,028.028 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 08:59:19,029.029 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 DOGE-USDT-SWAP: {'adl': '1', 'availPos': '1.52', 'avgPx': '0.23585', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '0.**************', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.179246', 'fundingFee': '0.********', 'gammaBS': '', 'gammaPA': '', 'idxPx': '0.236105', 'imr': '49.**************', 'instId': 'DOGE-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '0.2364', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '86.**************', 'margin': '', 'markPx': '0.23632', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '52225.************', 'mmr': '2.3348416', 'nonSettleAvgPx': '', 'notionalUsd': '359.*********', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '1.52', 'posCcy': '', 'posId': '2720240594715480064', 'posSide': 'short', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.14345912', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '*********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.****************', 'uplRatio': '-0.****************', 'uplRatioLastPx': '-0.****************', 'usdPx': '1.00027', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 08:59:19,030.030 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: DOGE-USDT-SWAP = 49.89 USDT (来源: imr)
[CryptoQuant] 2025-07-27 08:59:19,113.113 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 08:59:19,739.739 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 08:59:19,740.740 - INFO     - src.deprecated.trade_executor - 设置初始余额基准: 131319.35 USDT
[CryptoQuant] 2025-07-27 08:59:19,741.741 - INFO     - src.deprecated.trade_executor - 设置今日余额基准: 131319.35 USDT
[CryptoQuant] 2025-07-27 08:59:32,490.490 - INFO     - src.logger_monitor - 日志监控器初始化完成
[CryptoQuant] 2025-07-27 08:59:32,492.492 - INFO     - src.logger_monitor - 日志监控启动成功
[CryptoQuant] 2025-07-27 08:59:32,493.493 - INFO     - src.main_controller - 主控制器开始初始化
[CryptoQuant] 2025-07-27 08:59:32,494.494 - INFO     - src.deprecated.data_fetcher - 🔄 尝试连接OKX域名: https://www.okx.com
[CryptoQuant] 2025-07-27 08:59:34,766.766 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/time "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 08:59:34,767.767 - INFO     - src.deprecated.data_fetcher - 🟢 成功连接OKX域名: https://www.okx.com
[CryptoQuant] 2025-07-27 08:59:34,768.768 - INFO     - src.deprecated.data_fetcher - 🔧 API超时和连接池配置完成
[CryptoQuant] 2025-07-27 08:59:35,315.315 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/time "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 08:59:35,316.316 - INFO     - src.deprecated.data_fetcher - 🟢 OKX连接健康检查通过
[CryptoQuant] 2025-07-27 08:59:35,317.317 - INFO     - src.deprecated.data_fetcher - 🟢 OKX客户端初始化成功 - 使用域名: https://www.okx.com
[CryptoQuant] 2025-07-27 08:59:35,318.318 - INFO     - src.indicator_calculator - 指标参数加载完成
[CryptoQuant] 2025-07-27 08:59:35,318.318 - INFO     - src.risk_manager - 🔧 最终风险参数: {'max_leverage': 5.0, 'stop_loss_pct': 0.02, 'take_profit_pct': 0.04, 'max_position_ratio': 0.05, 'max_daily_loss_usdt': 0.05, 'min_balance_usdt': 10.0, 'safety_margin_ratio': 0.1, 'max_concurrent_positions': 3, 'emergency_stop_loss_pct': 0.15}
[CryptoQuant] 2025-07-27 08:59:35,319.319 - INFO     - src.risk_manager - 🛡️ 现代化风控系统初始化完成
[CryptoQuant] 2025-07-27 08:59:35,319.319 - INFO     - src.risk_manager - 📋 风控参数: {'max_leverage': 5.0, 'stop_loss_pct': 0.02, 'take_profit_pct': 0.04, 'max_position_ratio': 0.05, 'max_daily_loss_usdt': 0.05, 'min_balance_usdt': 10.0, 'safety_margin_ratio': 0.1, 'max_concurrent_positions': 3, 'emergency_stop_loss_pct': 0.15}
[CryptoQuant] 2025-07-27 08:59:35,328.328 - INFO     - src.web_dashboard - Web仪表盘初始化完成
[CryptoQuant] 2025-07-27 08:59:35,329.329 - INFO     - src.main_controller - 定时任务设置完成
[CryptoQuant] 2025-07-27 08:59:35,330.330 - INFO     - src.main_controller - 所有模块初始化完成
[CryptoQuant] 2025-07-27 08:59:35,331.331 - INFO     - src.web_dashboard - 启动Web服务器: http://0.0.0.0:5000
[CryptoQuant] 2025-07-27 08:59:35,332.332 - INFO     - src.main_controller - 开始初始化API相关模块...
[CryptoQuant] 2025-07-27 08:59:35,333.333 - INFO     - src.deprecated.trade_executor - 🔗 初始化交易API - 使用域名: https://www.okx.com
[CryptoQuant] 2025-07-27 08:59:35,362.362 - INFO     - src.deprecated.trade_executor - 🔧 交易API增强超时配置完成: 连接15s, 读取45s, 连接池30个
[CryptoQuant] 2025-07-27 08:59:37,205.205 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/time "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 08:59:37,206.206 - INFO     - src.deprecated.trade_executor - ✅ 交易API初始化完成，连接健康检查通过
[CryptoQuant] 2025-07-27 08:59:37,226.226 - INFO     - src.deprecated.trade_executor - 交易执行器初始化完成
[CryptoQuant] 2025-07-27 08:59:37,226.226 - INFO     - src.main_controller - 交易执行器初始化完成
[CryptoQuant] 2025-07-27 08:59:37,244.244 - INFO     - src.logger_monitor - 日志监控器初始化完成
[CryptoQuant] 2025-07-27 08:59:37,247.247 - INFO     - src.logger_monitor - 日志监控启动成功
[CryptoQuant] 2025-07-27 08:59:37,247.247 - DEBUG    - src.logger_monitor - 这是一条调试信息。
[CryptoQuant] 2025-07-27 08:59:37,248.248 - INFO     - src.logger_monitor - 这是一条普通信息，表示操作成功完成。
[CryptoQuant] 2025-07-27 08:59:37,248.248 - WARNING  - src.logger_monitor - 这是一条警告信息，表示可能存在问题。
[CryptoQuant] 2025-07-27 08:59:37,249.249 - ERROR    - src.logger_monitor - 这是一条错误信息，表示发生了严重错误。
[CryptoQuant] 2025-07-27 08:59:37,249.249 - CRITICAL - src.logger_monitor - 这是一条严重错误信息，系统即将崩溃。
Traceback (most recent call last):
  File "D:\job\大模型加密货币量化系统\test_logger.py", line 43, in main
    raise ValueError("模拟一个异常")
ValueError: 模拟一个异常
[CryptoQuant] 2025-07-27 08:59:37,308.308 - INFO     - src.optimized_prompts.smart_hunter_engine - 智能猎手引擎 - API密钥加载成功
[CryptoQuant] 2025-07-27 08:59:37,309.309 - INFO     - src.optimized_prompts.smart_hunter_engine - 智能猎手引擎初始化完成
[CryptoQuant] 2025-07-27 08:59:37,309.309 - INFO     - src.optimized_prompts.risk_guardian_engine - 风险守护者引擎 - API密钥加载成功
[CryptoQuant] 2025-07-27 08:59:37,309.309 - INFO     - src.optimized_prompts.risk_guardian_engine - 风险守护者引擎初始化完成
[CryptoQuant] 2025-07-27 08:59:37,310.310 - INFO     - src.multi_contract_manager - 专门决策引擎初始化成功
[CryptoQuant] 2025-07-27 08:59:37,310.310 - INFO     - src.multi_contract_manager - 获取到3个活跃交易对: ['BTC-USDT-SWAP', 'ETH-USDT-SWAP', 'DOGE-USDT-SWAP']
[CryptoQuant] 2025-07-27 08:59:37,311.311 - INFO     - src.multi_contract_manager - 成功初始化了3个交易对
[CryptoQuant] 2025-07-27 08:59:37,311.311 - INFO     - src.multi_contract_manager - 多合约管理器初始化完成
[CryptoQuant] 2025-07-27 08:59:37,311.311 - INFO     - src.main_controller - 多合约管理器初始化完成
[CryptoQuant] 2025-07-27 08:59:37,312.312 - INFO     - src.main_controller - Web仪表盘模块引用已更新
[CryptoQuant] 2025-07-27 08:59:37,312.312 - INFO     - src.main_controller - API相关模块初始化完成
[CryptoQuant] 2025-07-27 08:59:37,313.313 - INFO     - src.web_dashboard - Web仪表盘启动成功: http://127.0.0.1:5000
[CryptoQuant] 2025-07-27 08:59:38,252.252 - INFO     - src.logger_monitor - 性能日志: 数据处理 成功, 耗时: 0.123秒
[CryptoQuant] 2025-07-27 08:59:38,253.253 - INFO     - src.logger_monitor - 性能日志: API调用 失败, 耗时: 0.543秒
[CryptoQuant] 2025-07-27 08:59:38,254.254 - INFO     - src.logger_monitor - 交易事件: ORDER_FILLED - BTC/USDT - {"price": 30000, "quantity": 0.01}
[CryptoQuant] 2025-07-27 08:59:38,255.255 - ERROR    - src.logger_monitor - 保存交易日志失败: name 'trade_log' is not defined
Traceback (most recent call last):
  File "D:\job\大模型加密货币量化系统\src\logger_monitor.py", line 460, in log_trade_event
    trade_logs.append(trade_log)
                      ^^^^^^^^^
NameError: name 'trade_log' is not defined
[CryptoQuant] 2025-07-27 08:59:43,264.264 - INFO     - src.logger_monitor - 日志监控已停止
[CryptoQuant] 2025-07-27 09:00:02,483.483 - INFO     - src.logger_monitor - 日志监控器初始化完成
[CryptoQuant] 2025-07-27 09:00:02,485.485 - INFO     - src.logger_monitor - 日志监控启动成功
[CryptoQuant] 2025-07-27 09:00:02,486.486 - INFO     - src.main_controller - 主控制器开始初始化
[CryptoQuant] 2025-07-27 09:00:02,487.487 - INFO     - src.deprecated.data_fetcher - 🔄 尝试连接OKX域名: https://www.okx.com
[CryptoQuant] 2025-07-27 09:00:04,074.074 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/time "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:00:04,075.075 - INFO     - src.deprecated.data_fetcher - 🟢 成功连接OKX域名: https://www.okx.com
[CryptoQuant] 2025-07-27 09:00:04,076.076 - INFO     - src.deprecated.data_fetcher - 🔧 API超时和连接池配置完成
[CryptoQuant] 2025-07-27 09:00:04,458.458 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/time "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:00:04,459.459 - INFO     - src.deprecated.data_fetcher - 🟢 OKX连接健康检查通过
[CryptoQuant] 2025-07-27 09:00:04,460.460 - INFO     - src.deprecated.data_fetcher - 🟢 OKX客户端初始化成功 - 使用域名: https://www.okx.com
[CryptoQuant] 2025-07-27 09:00:04,460.460 - INFO     - src.indicator_calculator - 指标参数加载完成
[CryptoQuant] 2025-07-27 09:00:04,462.462 - INFO     - src.risk_manager - 🔧 最终风险参数: {'max_leverage': 5.0, 'stop_loss_pct': 0.02, 'take_profit_pct': 0.04, 'max_position_ratio': 0.05, 'max_daily_loss_usdt': 0.05, 'min_balance_usdt': 10.0, 'safety_margin_ratio': 0.1, 'max_concurrent_positions': 3, 'emergency_stop_loss_pct': 0.15}
[CryptoQuant] 2025-07-27 09:00:04,463.463 - INFO     - src.risk_manager - 🛡️ 现代化风控系统初始化完成
[CryptoQuant] 2025-07-27 09:00:04,463.463 - INFO     - src.risk_manager - 📋 风控参数: {'max_leverage': 5.0, 'stop_loss_pct': 0.02, 'take_profit_pct': 0.04, 'max_position_ratio': 0.05, 'max_daily_loss_usdt': 0.05, 'min_balance_usdt': 10.0, 'safety_margin_ratio': 0.1, 'max_concurrent_positions': 3, 'emergency_stop_loss_pct': 0.15}
[CryptoQuant] 2025-07-27 09:00:04,473.473 - INFO     - src.web_dashboard - Web仪表盘初始化完成
[CryptoQuant] 2025-07-27 09:00:04,474.474 - INFO     - src.main_controller - 定时任务设置完成
[CryptoQuant] 2025-07-27 09:00:04,475.475 - INFO     - src.main_controller - 所有模块初始化完成
[CryptoQuant] 2025-07-27 09:00:04,476.476 - INFO     - src.web_dashboard - 启动Web服务器: http://0.0.0.0:5000
[CryptoQuant] 2025-07-27 09:00:04,476.476 - INFO     - src.main_controller - 开始初始化API相关模块...
[CryptoQuant] 2025-07-27 09:00:04,478.478 - INFO     - src.deprecated.trade_executor - 🔗 初始化交易API - 使用域名: https://www.okx.com
[CryptoQuant] 2025-07-27 09:00:04,506.506 - INFO     - src.deprecated.trade_executor - 🔧 交易API增强超时配置完成: 连接15s, 读取45s, 连接池30个
[CryptoQuant] 2025-07-27 09:00:06,462.462 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/time "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:00:06,463.463 - INFO     - src.deprecated.trade_executor - ✅ 交易API初始化完成，连接健康检查通过
[CryptoQuant] 2025-07-27 09:00:06,483.483 - INFO     - src.deprecated.trade_executor - 交易执行器初始化完成
[CryptoQuant] 2025-07-27 09:00:06,483.483 - INFO     - src.main_controller - 交易执行器初始化完成
[CryptoQuant] 2025-07-27 09:00:06,558.558 - INFO     - src.optimized_prompts.smart_hunter_engine - 智能猎手引擎 - API密钥加载成功
[CryptoQuant] 2025-07-27 09:00:06,559.559 - INFO     - src.optimized_prompts.smart_hunter_engine - 智能猎手引擎初始化完成
[CryptoQuant] 2025-07-27 09:00:06,560.560 - INFO     - src.optimized_prompts.risk_guardian_engine - 风险守护者引擎 - API密钥加载成功
[CryptoQuant] 2025-07-27 09:00:06,560.560 - INFO     - src.optimized_prompts.risk_guardian_engine - 风险守护者引擎初始化完成
[CryptoQuant] 2025-07-27 09:00:06,561.561 - INFO     - src.multi_contract_manager - 专门决策引擎初始化成功
[CryptoQuant] 2025-07-27 09:00:06,561.561 - INFO     - src.multi_contract_manager - 获取到3个活跃交易对: ['BTC-USDT-SWAP', 'ETH-USDT-SWAP', 'DOGE-USDT-SWAP']
[CryptoQuant] 2025-07-27 09:00:06,562.562 - INFO     - src.multi_contract_manager - 成功初始化了3个交易对
[CryptoQuant] 2025-07-27 09:00:06,562.562 - INFO     - src.multi_contract_manager - 多合约管理器初始化完成
[CryptoQuant] 2025-07-27 09:00:06,562.562 - INFO     - src.main_controller - 多合约管理器初始化完成
[CryptoQuant] 2025-07-27 09:00:06,563.563 - INFO     - src.main_controller - Web仪表盘模块引用已更新
[CryptoQuant] 2025-07-27 09:00:06,563.563 - INFO     - src.main_controller - API相关模块初始化完成
[CryptoQuant] 2025-07-27 09:00:06,564.564 - INFO     - src.web_dashboard - Web仪表盘启动成功: http://127.0.0.1:5000
[CryptoQuant] 2025-07-27 09:00:07,872.872 - INFO     - src.logger_monitor - 日志监控器初始化完成
[CryptoQuant] 2025-07-27 09:00:07,873.873 - INFO     - src.logger_monitor - 日志监控启动成功
[CryptoQuant] 2025-07-27 09:00:07,873.873 - DEBUG    - src.logger_monitor - 这是一条调试信息。
[CryptoQuant] 2025-07-27 09:00:07,874.874 - INFO     - src.logger_monitor - 这是一条普通信息，表示操作成功完成。
[CryptoQuant] 2025-07-27 09:00:07,874.874 - WARNING  - src.logger_monitor - 这是一条警告信息，表示可能存在问题。
[CryptoQuant] 2025-07-27 09:00:07,875.875 - ERROR    - src.logger_monitor - 这是一条错误信息，表示发生了严重错误。
[CryptoQuant] 2025-07-27 09:00:07,875.875 - CRITICAL - src.logger_monitor - 这是一条严重错误信息，系统即将崩溃。
Traceback (most recent call last):
  File "D:\job\大模型加密货币量化系统\test_logger.py", line 43, in main
    raise ValueError("模拟一个异常")
ValueError: 模拟一个异常
[CryptoQuant] 2025-07-27 09:00:08,877.877 - INFO     - src.logger_monitor - 性能日志: 数据处理 成功, 耗时: 0.123秒
[CryptoQuant] 2025-07-27 09:00:08,877.877 - INFO     - src.logger_monitor - 性能日志: API调用 失败, 耗时: 0.543秒
[CryptoQuant] 2025-07-27 09:00:08,878.878 - INFO     - src.logger_monitor - 交易事件: ORDER_FILLED - BTC/USDT - {"price": 30000, "quantity": 0.01}
[CryptoQuant] 2025-07-27 09:00:13,893.893 - INFO     - src.logger_monitor - 日志监控已停止
[CryptoQuant] 2025-07-27 09:00:17,869.869 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:00:18,243.243 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:00:18,516.516 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:00:18,517.517 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 DOGE-USDT-SWAP: {'adl': '1', 'availPos': '1.52', 'avgPx': '0.23585', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '0.**************', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.179246', 'fundingFee': '0.********', 'gammaBS': '', 'gammaPA': '', 'idxPx': '0.236359', 'imr': '49.**************', 'instId': 'DOGE-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '0.23635', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '86.**************', 'margin': '', 'markPx': '0.2364', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '52208.***********', 'mmr': '2.335632', 'nonSettleAvgPx': '', 'notionalUsd': '359.**************', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '1.52', 'posCcy': '', 'posId': '2720240594715480064', 'posSide': 'short', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.14345912', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '*********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.****************', 'uplRatio': '-0.****************', 'uplRatioLastPx': '-0.****************', 'usdPx': '1.00027', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:00:18,518.518 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: DOGE-USDT-SWAP = 49.91 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:00:18,607.607 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:00:18,608.608 - INFO     - src.deprecated.trade_executor - 设置初始余额基准: 131319.25 USDT
[CryptoQuant] 2025-07-27 09:00:18,609.609 - INFO     - src.deprecated.trade_executor - 设置今日余额基准: 131319.25 USDT
[CryptoQuant] 2025-07-27 09:00:30,770.770 - INFO     - src.multi_contract_manager - 多合约管理器已停止
[CryptoQuant] 2025-07-27 09:00:30,771.771 - INFO     - src.web_dashboard - Web仪表盘停止请求已发送
[CryptoQuant] 2025-07-27 09:00:35,780.780 - INFO     - src.logger_monitor - 日志监控已停止
[CryptoQuant] 2025-07-27 09:00:35,781.781 - INFO     - src.deprecated.data_fetcher - 数据缓存已清空
[CryptoQuant] 2025-07-27 09:00:35,781.781 - INFO     - src.main_controller - 系统已停止
2025-07-27 09:00:35,913 - src.multi_contract_manager - INFO - 多合约管理器已停止
2025-07-27 09:00:35,913 - src.web_dashboard - INFO - Web仪表盘停止请求已发送
[CryptoQuant] 2025-07-27 09:00:44,339.339 - INFO     - src.logger_monitor - 日志监控器初始化完成
[CryptoQuant] 2025-07-27 09:00:44,340.340 - INFO     - src.logger_monitor - 日志监控启动成功
[CryptoQuant] 2025-07-27 09:00:44,341.341 - INFO     - src.main_controller - 主控制器开始初始化
[CryptoQuant] 2025-07-27 09:00:44,342.342 - INFO     - src.deprecated.data_fetcher - 🔄 尝试连接OKX域名: https://www.okx.com
[CryptoQuant] 2025-07-27 09:00:46,652.652 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/time "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:00:46,653.653 - INFO     - src.deprecated.data_fetcher - 🟢 成功连接OKX域名: https://www.okx.com
[CryptoQuant] 2025-07-27 09:00:46,654.654 - INFO     - src.deprecated.data_fetcher - 🔧 API超时和连接池配置完成
[CryptoQuant] 2025-07-27 09:00:47,253.253 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/time "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:00:47,254.254 - INFO     - src.deprecated.data_fetcher - 🟢 OKX连接健康检查通过
[CryptoQuant] 2025-07-27 09:00:47,254.254 - INFO     - src.deprecated.data_fetcher - 🟢 OKX客户端初始化成功 - 使用域名: https://www.okx.com
[CryptoQuant] 2025-07-27 09:00:47,255.255 - INFO     - src.indicator_calculator - 指标参数加载完成
[CryptoQuant] 2025-07-27 09:00:47,256.256 - INFO     - src.risk_manager - 🔧 最终风险参数: {'max_leverage': 5.0, 'stop_loss_pct': 0.02, 'take_profit_pct': 0.04, 'max_position_ratio': 0.05, 'max_daily_loss_usdt': 0.05, 'min_balance_usdt': 10.0, 'safety_margin_ratio': 0.1, 'max_concurrent_positions': 3, 'emergency_stop_loss_pct': 0.15}
[CryptoQuant] 2025-07-27 09:00:47,257.257 - INFO     - src.risk_manager - 🛡️ 现代化风控系统初始化完成
[CryptoQuant] 2025-07-27 09:00:47,257.257 - INFO     - src.risk_manager - 📋 风控参数: {'max_leverage': 5.0, 'stop_loss_pct': 0.02, 'take_profit_pct': 0.04, 'max_position_ratio': 0.05, 'max_daily_loss_usdt': 0.05, 'min_balance_usdt': 10.0, 'safety_margin_ratio': 0.1, 'max_concurrent_positions': 3, 'emergency_stop_loss_pct': 0.15}
[CryptoQuant] 2025-07-27 09:00:47,268.268 - INFO     - src.web_dashboard - Web仪表盘初始化完成
[CryptoQuant] 2025-07-27 09:00:47,269.269 - INFO     - src.main_controller - 定时任务设置完成
[CryptoQuant] 2025-07-27 09:00:47,269.269 - INFO     - src.main_controller - 所有模块初始化完成
[CryptoQuant] 2025-07-27 09:00:47,270.270 - INFO     - src.web_dashboard - 启动Web服务器: http://0.0.0.0:5000
[CryptoQuant] 2025-07-27 09:00:47,271.271 - INFO     - src.main_controller - 开始初始化API相关模块...
[CryptoQuant] 2025-07-27 09:00:47,271.271 - INFO     - src.deprecated.trade_executor - 🔗 初始化交易API - 使用域名: https://www.okx.com
[CryptoQuant] 2025-07-27 09:00:47,301.301 - INFO     - src.deprecated.trade_executor - 🔧 交易API增强超时配置完成: 连接15s, 读取45s, 连接池30个
[CryptoQuant] 2025-07-27 09:00:49,134.134 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/time "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:00:49,135.135 - INFO     - src.deprecated.trade_executor - ✅ 交易API初始化完成，连接健康检查通过
[CryptoQuant] 2025-07-27 09:00:49,157.157 - INFO     - src.deprecated.trade_executor - 交易执行器初始化完成
[CryptoQuant] 2025-07-27 09:00:49,157.157 - INFO     - src.main_controller - 交易执行器初始化完成
[CryptoQuant] 2025-07-27 09:00:49,231.231 - INFO     - src.optimized_prompts.smart_hunter_engine - 智能猎手引擎 - API密钥加载成功
[CryptoQuant] 2025-07-27 09:00:49,231.231 - INFO     - src.optimized_prompts.smart_hunter_engine - 智能猎手引擎初始化完成
[CryptoQuant] 2025-07-27 09:00:49,233.233 - INFO     - src.optimized_prompts.risk_guardian_engine - 风险守护者引擎 - API密钥加载成功
[CryptoQuant] 2025-07-27 09:00:49,233.233 - INFO     - src.optimized_prompts.risk_guardian_engine - 风险守护者引擎初始化完成
[CryptoQuant] 2025-07-27 09:00:49,234.234 - INFO     - src.multi_contract_manager - 专门决策引擎初始化成功
[CryptoQuant] 2025-07-27 09:00:49,235.235 - INFO     - src.multi_contract_manager - 获取到3个活跃交易对: ['BTC-USDT-SWAP', 'ETH-USDT-SWAP', 'DOGE-USDT-SWAP']
[CryptoQuant] 2025-07-27 09:00:49,235.235 - INFO     - src.multi_contract_manager - 成功初始化了3个交易对
[CryptoQuant] 2025-07-27 09:00:49,236.236 - INFO     - src.multi_contract_manager - 多合约管理器初始化完成
[CryptoQuant] 2025-07-27 09:00:49,237.237 - INFO     - src.main_controller - 多合约管理器初始化完成
[CryptoQuant] 2025-07-27 09:00:49,237.237 - INFO     - src.main_controller - Web仪表盘模块引用已更新
[CryptoQuant] 2025-07-27 09:00:49,238.238 - INFO     - src.main_controller - API相关模块初始化完成
[CryptoQuant] 2025-07-27 09:00:49,239.239 - INFO     - src.web_dashboard - Web仪表盘启动成功: http://127.0.0.1:5000
[CryptoQuant] 2025-07-27 09:00:50,063.063 - INFO     - src.logger_monitor - 日志监控器初始化完成
[CryptoQuant] 2025-07-27 09:00:50,065.065 - INFO     - src.logger_monitor - 日志监控启动成功
[CryptoQuant] 2025-07-27 09:00:50,066.066 - INFO     - src.main_controller - 主控制器开始初始化
[CryptoQuant] 2025-07-27 09:00:50,067.067 - INFO     - src.deprecated.data_fetcher - 🔄 尝试连接OKX域名: https://www.okx.com
[CryptoQuant] 2025-07-27 09:00:50,589.589 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:00:50,972.972 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:00:51,429.429 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:00:51,431.431 - INFO     - src.deprecated.trade_executor - 设置初始余额基准: 131319.08 USDT
[CryptoQuant] 2025-07-27 09:00:51,432.432 - INFO     - src.deprecated.trade_executor - 设置今日余额基准: 131319.08 USDT
[CryptoQuant] 2025-07-27 09:00:51,558.558 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/time "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:00:51,560.560 - INFO     - src.deprecated.data_fetcher - 🟢 成功连接OKX域名: https://www.okx.com
[CryptoQuant] 2025-07-27 09:00:51,561.561 - INFO     - src.deprecated.data_fetcher - 🔧 API超时和连接池配置完成
[CryptoQuant] 2025-07-27 09:00:51,809.809 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:00:51,811.811 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 DOGE-USDT-SWAP: {'adl': '1', 'availPos': '1.52', 'avgPx': '0.23585', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '0.**************', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.179246', 'fundingFee': '0.********', 'gammaBS': '', 'gammaPA': '', 'idxPx': '0.236425', 'imr': '49.**************', 'instId': 'DOGE-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '0.2365', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '86.**************', 'margin': '', 'markPx': '0.23645', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '52197.***********', 'mmr': '2.****************', 'nonSettleAvgPx': '', 'notionalUsd': '359.**************', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '1.52', 'posCcy': '', 'posId': '2720240594715480064', 'posSide': 'short', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.14345912', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '*********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.9879999999999756', 'uplRatio': '-0.***************', 'uplRatioLastPx': '-0.0198431206275169', 'usdPx': '1.00026', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:00:51,812.812 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: DOGE-USDT-SWAP = 49.92 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:00:51,941.941 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/time "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:00:51,942.942 - INFO     - src.deprecated.data_fetcher - 🟢 OKX连接健康检查通过
[CryptoQuant] 2025-07-27 09:00:51,942.942 - INFO     - src.deprecated.data_fetcher - 🟢 OKX客户端初始化成功 - 使用域名: https://www.okx.com
[CryptoQuant] 2025-07-27 09:00:51,943.943 - INFO     - src.indicator_calculator - 指标参数加载完成
[CryptoQuant] 2025-07-27 09:00:51,944.944 - INFO     - src.risk_manager - 🔧 最终风险参数: {'max_leverage': 5.0, 'stop_loss_pct': 0.02, 'take_profit_pct': 0.04, 'max_position_ratio': 0.05, 'max_daily_loss_usdt': 0.05, 'min_balance_usdt': 10.0, 'safety_margin_ratio': 0.1, 'max_concurrent_positions': 3, 'emergency_stop_loss_pct': 0.15}
[CryptoQuant] 2025-07-27 09:00:51,945.945 - INFO     - src.risk_manager - 🛡️ 现代化风控系统初始化完成
[CryptoQuant] 2025-07-27 09:00:51,945.945 - INFO     - src.risk_manager - 📋 风控参数: {'max_leverage': 5.0, 'stop_loss_pct': 0.02, 'take_profit_pct': 0.04, 'max_position_ratio': 0.05, 'max_daily_loss_usdt': 0.05, 'min_balance_usdt': 10.0, 'safety_margin_ratio': 0.1, 'max_concurrent_positions': 3, 'emergency_stop_loss_pct': 0.15}
[CryptoQuant] 2025-07-27 09:00:51,956.956 - INFO     - src.web_dashboard - Web仪表盘初始化完成
[CryptoQuant] 2025-07-27 09:00:51,957.957 - INFO     - src.main_controller - 定时任务设置完成
[CryptoQuant] 2025-07-27 09:00:51,958.958 - INFO     - src.main_controller - 所有模块初始化完成
[CryptoQuant] 2025-07-27 09:00:51,959.959 - INFO     - src.web_dashboard - 启动Web服务器: http://0.0.0.0:5000
[CryptoQuant] 2025-07-27 09:00:51,959.959 - INFO     - src.main_controller - 开始初始化API相关模块...
[CryptoQuant] 2025-07-27 09:00:51,961.961 - INFO     - src.deprecated.trade_executor - 🔗 初始化交易API - 使用域名: https://www.okx.com
[CryptoQuant] 2025-07-27 09:00:51,997.997 - INFO     - src.deprecated.trade_executor - 🔧 交易API增强超时配置完成: 连接15s, 读取45s, 连接池30个
[CryptoQuant] 2025-07-27 09:00:54,398.398 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/time "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:00:54,399.399 - INFO     - src.deprecated.trade_executor - ✅ 交易API初始化完成，连接健康检查通过
[CryptoQuant] 2025-07-27 09:00:54,419.419 - INFO     - src.deprecated.trade_executor - 交易执行器初始化完成
[CryptoQuant] 2025-07-27 09:00:54,420.420 - INFO     - src.main_controller - 交易执行器初始化完成
[CryptoQuant] 2025-07-27 09:00:54,494.494 - INFO     - src.optimized_prompts.smart_hunter_engine - 智能猎手引擎 - API密钥加载成功
[CryptoQuant] 2025-07-27 09:00:54,495.495 - INFO     - src.optimized_prompts.smart_hunter_engine - 智能猎手引擎初始化完成
[CryptoQuant] 2025-07-27 09:00:54,495.495 - INFO     - src.optimized_prompts.risk_guardian_engine - 风险守护者引擎 - API密钥加载成功
[CryptoQuant] 2025-07-27 09:00:54,496.496 - INFO     - src.optimized_prompts.risk_guardian_engine - 风险守护者引擎初始化完成
[CryptoQuant] 2025-07-27 09:00:54,496.496 - INFO     - src.multi_contract_manager - 专门决策引擎初始化成功
[CryptoQuant] 2025-07-27 09:00:54,497.497 - INFO     - src.multi_contract_manager - 获取到3个活跃交易对: ['BTC-USDT-SWAP', 'ETH-USDT-SWAP', 'DOGE-USDT-SWAP']
[CryptoQuant] 2025-07-27 09:00:54,498.498 - INFO     - src.multi_contract_manager - 成功初始化了3个交易对
[CryptoQuant] 2025-07-27 09:00:54,498.498 - INFO     - src.multi_contract_manager - 多合约管理器初始化完成
[CryptoQuant] 2025-07-27 09:00:54,499.499 - INFO     - src.main_controller - 多合约管理器初始化完成
[CryptoQuant] 2025-07-27 09:00:54,499.499 - INFO     - src.main_controller - Web仪表盘模块引用已更新
[CryptoQuant] 2025-07-27 09:00:54,499.499 - INFO     - src.main_controller - API相关模块初始化完成
[CryptoQuant] 2025-07-27 09:00:54,500.500 - INFO     - src.web_dashboard - Web仪表盘启动成功: http://127.0.0.1:5000
[CryptoQuant] 2025-07-27 09:00:56,996.996 - WARNING  - src.deprecated.data_fetcher - 🔴 连接问题检测到，1.5秒后重试 (1/5): _ssl.c:975: The handshake operation timed out
[CryptoQuant] 2025-07-27 09:00:57,606.606 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:00:58,278.278 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:00:58,924.924 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:00:58,925.925 - INFO     - src.deprecated.trade_executor - 设置初始余额基准: 131319.17 USDT
[CryptoQuant] 2025-07-27 09:00:58,926.926 - INFO     - src.deprecated.trade_executor - 设置今日余额基准: 131319.17 USDT
[CryptoQuant] 2025-07-27 09:00:59,616.616 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:00,366.366 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:00,857.857 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:00,858.858 - INFO     - src.deprecated.data_fetcher - 🟢 API请求在第2次尝试后成功恢复
[CryptoQuant] 2025-07-27 09:01:00,858.858 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 DOGE-USDT-SWAP: {'adl': '1', 'availPos': '1.52', 'avgPx': '0.23585', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '0.**************', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.179246', 'fundingFee': '0.********', 'gammaBS': '', 'gammaPA': '', 'idxPx': '0.236399', 'imr': '49.**************', 'instId': 'DOGE-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '0.23645', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '86.**************', 'margin': '', 'markPx': '0.23645', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '52197.***********', 'mmr': '2.****************', 'nonSettleAvgPx': '', 'notionalUsd': '359.********', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '1.52', 'posCcy': '', 'posId': '2720240594715480064', 'posSide': 'short', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.14345912', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '*********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.****************', 'uplRatio': '-0.***************', 'uplRatioLastPx': '-0.***************', 'usdPx': '1.00027', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:01:00,859.859 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: DOGE-USDT-SWAP = 49.92 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:01:01,168.168 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:01,568.568 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:01,569.569 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 DOGE-USDT-SWAP: {'adl': '1', 'availPos': '1.52', 'avgPx': '0.23585', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '0.**************', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.179246', 'fundingFee': '0.********', 'gammaBS': '', 'gammaPA': '', 'idxPx': '0.236396', 'imr': '49.**************', 'instId': 'DOGE-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '0.23645', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '86.**************', 'margin': '', 'markPx': '0.23645', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '52197.***********', 'mmr': '2.****************', 'nonSettleAvgPx': '', 'notionalUsd': '359.********', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '1.52', 'posCcy': '', 'posId': '2720240594715480064', 'posSide': 'short', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.14345912', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '*********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.****************', 'uplRatio': '-0.***************', 'uplRatioLastPx': '-0.***************', 'usdPx': '1.00027', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:01:01,570.570 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: DOGE-USDT-SWAP = 49.92 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:01:02,268.268 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:02,285.285 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:02,286.286 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 DOGE-USDT-SWAP: {'adl': '1', 'availPos': '1.52', 'avgPx': '0.23585', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '0.**************', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.179246', 'fundingFee': '0.********', 'gammaBS': '', 'gammaPA': '', 'idxPx': '0.236385', 'imr': '49.**************', 'instId': 'DOGE-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '0.23645', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '86.**************', 'margin': '', 'markPx': '0.23644', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '52199.************', 'mmr': '2.****************', 'nonSettleAvgPx': '', 'notionalUsd': '359.*********', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '1.52', 'posCcy': '', 'posId': '2720240594715480064', 'posSide': 'short', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.14345912', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '*********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.****************', 'uplRatio': '-0.****************', 'uplRatioLastPx': '-0.***************', 'usdPx': '1.00027', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:01:02,286.286 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: DOGE-USDT-SWAP = 49.92 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:01:02,992.992 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:02,993.993 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 DOGE-USDT-SWAP: {'adl': '1', 'availPos': '1.52', 'avgPx': '0.23585', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '0.**************', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.179246', 'fundingFee': '0.********', 'gammaBS': '', 'gammaPA': '', 'idxPx': '0.236385', 'imr': '49.913', 'instId': 'DOGE-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '0.23645', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '86.**************', 'margin': '', 'markPx': '0.23643', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '52201.***********', 'mmr': '2.3359284', 'nonSettleAvgPx': '', 'notionalUsd': '359.*********', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '1.52', 'posCcy': '', 'posId': '2720240594715480064', 'posSide': 'short', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.14345912', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '*********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.****************', 'uplRatio': '-0.****************', 'uplRatioLastPx': '-0.***************', 'usdPx': '1.00027', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:01:02,993.993 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: DOGE-USDT-SWAP = 49.91 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:01:03,045.045 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:03,664.664 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:03,665.665 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 DOGE-USDT-SWAP: {'adl': '1', 'availPos': '1.52', 'avgPx': '0.23585', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '0.**************', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.179246', 'fundingFee': '0.********', 'gammaBS': '', 'gammaPA': '', 'idxPx': '0.236382', 'imr': '49.913', 'instId': 'DOGE-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '0.23645', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '86.**************', 'margin': '', 'markPx': '0.23643', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '52201.***********', 'mmr': '2.3359284', 'nonSettleAvgPx': '', 'notionalUsd': '359.*********', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '1.52', 'posCcy': '', 'posId': '2720240594715480064', 'posSide': 'short', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.14345912', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '*********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.****************', 'uplRatio': '-0.****************', 'uplRatioLastPx': '-0.***************', 'usdPx': '1.00027', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:01:03,666.666 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: DOGE-USDT-SWAP = 49.91 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:01:03,798.798 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:05,136.136 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:05,137.137 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 DOGE-USDT-SWAP: {'adl': '1', 'availPos': '1.52', 'avgPx': '0.23585', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '0.**************', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.179246', 'fundingFee': '0.********', 'gammaBS': '', 'gammaPA': '', 'idxPx': '0.236361', 'imr': '49.**************', 'instId': 'DOGE-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '0.23645', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '86.**************', 'margin': '', 'markPx': '0.23641', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '52206.***********', 'mmr': '2.3357308', 'nonSettleAvgPx': '', 'notionalUsd': '359.**************', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '1.52', 'posCcy': '', 'posId': '2720240594715480064', 'posSide': 'short', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.14345912', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '*********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.****************', 'uplRatio': '-0.****************', 'uplRatioLastPx': '-0.***************', 'usdPx': '1.00027', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:01:05,137.137 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: DOGE-USDT-SWAP = 49.91 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:01:05,254.254 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:06,215.215 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:06,878.878 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:08,035.035 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:08,036.036 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 DOGE-USDT-SWAP: {'adl': '1', 'availPos': '1.52', 'avgPx': '0.23585', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '0.**************', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.179246', 'fundingFee': '0.********', 'gammaBS': '', 'gammaPA': '', 'idxPx': '0.236348', 'imr': '49.***************', 'instId': 'DOGE-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '0.23645', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '86.**************', 'margin': '', 'markPx': '0.23639', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '52210.************', 'mmr': '2.****************', 'nonSettleAvgPx': '', 'notionalUsd': '359.*********', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '1.52', 'posCcy': '', 'posId': '2720240594715480064', 'posSide': 'short', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.14345912', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '*********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.****************', 'uplRatio': '-0.****************', 'uplRatioLastPx': '-0.***************', 'usdPx': '1.00027', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:01:08,037.037 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: DOGE-USDT-SWAP = 49.90 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:01:08,275.275 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:09,003.003 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:09,653.653 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:11,134.134 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:11,135.135 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 DOGE-USDT-SWAP: {'adl': '1', 'availPos': '1.52', 'avgPx': '0.23585', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '0.**************', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.179246', 'fundingFee': '0.********', 'gammaBS': '', 'gammaPA': '', 'idxPx': '0.236276', 'imr': '49.894', 'instId': 'DOGE-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '0.23645', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '86.**************', 'margin': '', 'markPx': '0.23634', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '52221.***********', 'mmr': '2.****************', 'nonSettleAvgPx': '', 'notionalUsd': '359.*********', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '1.52', 'posCcy': '', 'posId': '2720240594715480064', 'posSide': 'short', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.14345912', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '*********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.****************', 'uplRatio': '-0.****************', 'uplRatioLastPx': '-0.***************', 'usdPx': '1.00027', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:01:11,136.136 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: DOGE-USDT-SWAP = 49.89 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:01:11,152.152 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:11,829.829 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:12,547.547 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:14,183.183 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:14,185.185 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 DOGE-USDT-SWAP: {'adl': '1', 'availPos': '1.52', 'avgPx': '0.23585', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '0.**************', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.179246', 'fundingFee': '0.********', 'gammaBS': '', 'gammaPA': '', 'idxPx': '0.236262', 'imr': '49.**************', 'instId': 'DOGE-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '0.23645', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '86.**************', 'margin': '', 'markPx': '0.23631', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '52228.***********', 'mmr': '2.3347428', 'nonSettleAvgPx': '', 'notionalUsd': '359.**************', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '1.52', 'posCcy': '', 'posId': '2720240594715480064', 'posSide': 'short', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.14345912', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '*********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.****************', 'uplRatio': '-0.****************', 'uplRatioLastPx': '-0.***************', 'usdPx': '1.00026', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:01:14,185.185 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: DOGE-USDT-SWAP = 49.89 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:01:14,271.271 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:14,984.984 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:15,716.716 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:17,151.151 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:17,256.256 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:17,257.257 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 DOGE-USDT-SWAP: {'adl': '1', 'availPos': '1.52', 'avgPx': '0.23585', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '0.**************', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.179246', 'fundingFee': '0.********', 'gammaBS': '', 'gammaPA': '', 'idxPx': '0.236257', 'imr': '49.**************', 'instId': 'DOGE-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '0.23645', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '86.**************', 'margin': '', 'markPx': '0.23629', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '52232.***********', 'mmr': '2.3345452', 'nonSettleAvgPx': '', 'notionalUsd': '359.**************', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '1.52', 'posCcy': '', 'posId': '2720240594715480064', 'posSide': 'short', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.14345912', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '*********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.****************', 'uplRatio': '-0.***************', 'uplRatioLastPx': '-0.***************', 'usdPx': '1.00026', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:01:17,258.258 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: DOGE-USDT-SWAP = 49.88 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:01:17,778.778 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:18,416.416 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:20,101.101 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:20,167.167 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:20,168.168 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 DOGE-USDT-SWAP: {'adl': '1', 'availPos': '1.52', 'avgPx': '0.23585', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '0.**************', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.179246', 'fundingFee': '0.********', 'gammaBS': '', 'gammaPA': '', 'idxPx': '0.236268', 'imr': '49.**************', 'instId': 'DOGE-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '0.23645', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '86.**************', 'margin': '', 'markPx': '0.23629', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '52232.***********', 'mmr': '2.3345452', 'nonSettleAvgPx': '', 'notionalUsd': '359.**************', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '1.52', 'posCcy': '', 'posId': '2720240594715480064', 'posSide': 'short', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.14345912', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '*********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.****************', 'uplRatio': '-0.***************', 'uplRatioLastPx': '-0.***************', 'usdPx': '1.00026', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:01:20,169.169 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: DOGE-USDT-SWAP = 49.88 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:01:20,759.759 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:21,401.401 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:23,106.106 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:23,145.145 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:23,147.147 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 DOGE-USDT-SWAP: {'adl': '1', 'availPos': '1.52', 'avgPx': '0.23585', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '0.**************', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.179246', 'fundingFee': '0.********', 'gammaBS': '', 'gammaPA': '', 'idxPx': '0.236254', 'imr': '49.**************', 'instId': 'DOGE-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '0.23645', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '86.**************', 'margin': '', 'markPx': '0.23628', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '52234.***********', 'mmr': '2.****************', 'nonSettleAvgPx': '', 'notionalUsd': '359.**************', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '1.52', 'posCcy': '', 'posId': '2720240594715480064', 'posSide': 'short', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.14345912', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '*********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.****************', 'uplRatio': '-0.****************', 'uplRatioLastPx': '-0.***************', 'usdPx': '1.00026', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:01:23,148.148 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: DOGE-USDT-SWAP = 49.88 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:01:23,742.742 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:24,394.394 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:25,599.599 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:25,639.639 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:25,641.641 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 DOGE-USDT-SWAP: {'adl': '1', 'availPos': '1.52', 'avgPx': '0.23585', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '0.**************', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.179246', 'fundingFee': '0.********', 'gammaBS': '', 'gammaPA': '', 'idxPx': '0.236254', 'imr': '49.***************', 'instId': 'DOGE-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '0.23645', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '86.**************', 'margin': '', 'markPx': '0.23627', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '52237.***********', 'mmr': '2.****************', 'nonSettleAvgPx': '', 'notionalUsd': '359.**************', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '1.52', 'posCcy': '', 'posId': '2720240594715480064', 'posSide': 'short', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.14345912', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '*********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.****************', 'uplRatio': '-0.0128217*********', 'uplRatioLastPx': '-0.***************', 'usdPx': '1.00025', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:01:25,641.641 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: DOGE-USDT-SWAP = 49.88 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:01:25,671.671 - INFO     - src.multi_contract_manager - 获取到3个活跃交易对: ['BTC-USDT-SWAP', 'ETH-USDT-SWAP', 'DOGE-USDT-SWAP']
[CryptoQuant] 2025-07-27 09:01:25,672.672 - INFO     - src.multi_contract_manager - 成功初始化了3个交易对
[CryptoQuant] 2025-07-27 09:01:25,674.674 - INFO     - src.multi_contract_manager - 多合约管理器启动成功
[CryptoQuant] 2025-07-27 09:01:26,198.198 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:26,338.338 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions?instId=BTC-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:26,501.501 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions?instId=ETH-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:26,502.502 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions?instId=DOGE-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:26,503.503 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:26,505.505 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 DOGE-USDT-SWAP: {'adl': '1', 'availPos': '1.52', 'avgPx': '0.23585', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '0.**************', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.179246', 'fundingFee': '0.********', 'gammaBS': '', 'gammaPA': '', 'idxPx': '0.236254', 'imr': '49.***************', 'instId': 'DOGE-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '0.23645', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '86.**************', 'margin': '', 'markPx': '0.23627', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '52237.***********', 'mmr': '2.****************', 'nonSettleAvgPx': '', 'notionalUsd': '359.**************', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '1.52', 'posCcy': '', 'posId': '2720240594715480064', 'posSide': 'short', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.14345912', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '*********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.****************', 'uplRatio': '-0.0128217*********', 'uplRatioLastPx': '-0.***************', 'usdPx': '1.00025', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:01:26,506.506 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 DOGE-USDT-SWAP: {'adl': '1', 'availPos': '1.52', 'avgPx': '0.23585', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '0.**************', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.179246', 'fundingFee': '0.********', 'gammaBS': '', 'gammaPA': '', 'idxPx': '0.236254', 'imr': '49.***************', 'instId': 'DOGE-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '0.23645', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '86.**************', 'margin': '', 'markPx': '0.23627', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '52237.***********', 'mmr': '2.****************', 'nonSettleAvgPx': '', 'notionalUsd': '359.**************', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '1.52', 'posCcy': '', 'posId': '2720240594715480064', 'posSide': 'short', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.14345912', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '*********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.****************', 'uplRatio': '-0.0128217*********', 'uplRatioLastPx': '-0.***************', 'usdPx': '1.00025', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:01:26,507.507 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: DOGE-USDT-SWAP = 49.88 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:01:26,507.507 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: DOGE-USDT-SWAP = 49.88 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:01:26,863.863 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:27,216.216 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions?instId=ETH-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:27,217.217 - INFO     - src.multi_contract_manager - 持仓状态分析: ETH-USDT-SWAP - NO_POSITION - 大小: 0
[CryptoQuant] 2025-07-27 09:01:27,372.372 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions?instId=DOGE-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:27,374.374 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions?instId=BTC-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:27,375.375 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 DOGE-USDT-SWAP: {'adl': '1', 'availPos': '1.52', 'avgPx': '0.23585', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '0.**************', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.179246', 'fundingFee': '0.********', 'gammaBS': '', 'gammaPA': '', 'idxPx': '0.236254', 'imr': '49.***************', 'instId': 'DOGE-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '0.23645', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '86.**************', 'margin': '', 'markPx': '0.23627', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '52237.***********', 'mmr': '2.****************', 'nonSettleAvgPx': '', 'notionalUsd': '359.**************', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '1.52', 'posCcy': '', 'posId': '2720240594715480064', 'posSide': 'short', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.14345912', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '*********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.****************', 'uplRatio': '-0.0128217*********', 'uplRatioLastPx': '-0.***************', 'usdPx': '1.00025', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:01:27,376.376 - INFO     - src.multi_contract_manager - 持仓状态分析: BTC-USDT-SWAP - NO_POSITION - 大小: 0
[CryptoQuant] 2025-07-27 09:01:27,376.376 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: DOGE-USDT-SWAP = 49.88 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:01:27,377.377 - INFO     - src.multi_contract_manager - 持仓状态分析: DOGE-USDT-SWAP - SHORT_POSITION - 大小: 1.52
[CryptoQuant] 2025-07-27 09:01:27,660.660 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:28,348.348 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:29,108.108 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:29,185.185 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:29,187.187 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 DOGE-USDT-SWAP: {'adl': '1', 'availPos': '1.52', 'avgPx': '0.23585', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '0.**************', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.179246', 'fundingFee': '0.********', 'gammaBS': '', 'gammaPA': '', 'idxPx': '0.23624', 'imr': '49.**************', 'instId': 'DOGE-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '0.23645', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '86.**************', 'margin': '', 'markPx': '0.23626', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '52239.***********', 'mmr': '2.****************', 'nonSettleAvgPx': '', 'notionalUsd': '359.**************', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '1.52', 'posCcy': '', 'posId': '2720240594715480064', 'posSide': 'short', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.14345912', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '*********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.****************', 'uplRatio': '-0.****************', 'uplRatioLastPx': '-0.***************', 'usdPx': '1.00025', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:01:29,187.187 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: DOGE-USDT-SWAP = 49.88 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:01:29,877.877 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:30,083.083 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/market/candles?instId=ETH-USDT-SWAP&bar=1H&limit=100 "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:30,601.601 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:30,691.691 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/market/candles?instId=BTC-USDT-SWAP&bar=1H&limit=100 "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:30,692.692 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/market/candles?instId=DOGE-USDT-SWAP&bar=1H&limit=100 "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:30,975.975 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/market/candles?instId=ETH-USDT-SWAP&bar=30m&limit=100 "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:31,366.366 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:31,678.678 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/market/candles?instId=BTC-USDT-SWAP&bar=30m&limit=100 "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:31,722.722 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/market/candles?instId=DOGE-USDT-SWAP&bar=30m&limit=100 "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:32,144.144 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:32,146.146 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 DOGE-USDT-SWAP: {'adl': '1', 'availPos': '1.52', 'avgPx': '0.23585', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '0.**************', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.179246', 'fundingFee': '0.********', 'gammaBS': '', 'gammaPA': '', 'idxPx': '0.236262', 'imr': '49.***************', 'instId': 'DOGE-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '0.2363', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '86.**************', 'margin': '', 'markPx': '0.23627', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '52237.***********', 'mmr': '2.****************', 'nonSettleAvgPx': '', 'notionalUsd': '359.**************', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '1.52', 'posCcy': '', 'posId': '2720240594715480064', 'posSide': 'short', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.14345912', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '*********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.***************', 'uplRatio': '-0.0128217*********', 'uplRatioLastPx': '-0.****************', 'usdPx': '1.00025', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:01:32,146.146 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: DOGE-USDT-SWAP = 49.88 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:01:32,284.284 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:32,563.563 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/market/candles?instId=ETH-USDT-SWAP&bar=15m&limit=100 "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:32,710.710 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/market/candles?instId=BTC-USDT-SWAP&bar=15m&limit=100 "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:32,720.720 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/market/candles?instId=DOGE-USDT-SWAP&bar=15m&limit=100 "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:32,963.963 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:33,256.256 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/market/candles?instId=ETH-USDT-SWAP&bar=5m&limit=100 "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:33,462.462 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/market/candles?instId=BTC-USDT-SWAP&bar=5m&limit=100 "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:33,463.463 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/market/candles?instId=DOGE-USDT-SWAP&bar=5m&limit=100 "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:33,541.541 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:34,042.042 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/market/candles?instId=ETH-USDT-SWAP&bar=1m&limit=100 "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:34,257.257 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/market/candles?instId=BTC-USDT-SWAP&bar=1m&limit=100 "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:34,416.416 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/market/candles?instId=DOGE-USDT-SWAP&bar=1m&limit=100 "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:34,799.799 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/market/ticker?instId=ETH-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:34,822.822 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:01:34,844.844 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:01:34,866.866 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:01:34,888.888 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:01:34,909.909 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:01:34,999.999 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/market/ticker?instId=BTC-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:35,020.020 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:01:35,043.043 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:01:35,067.067 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:01:35,089.089 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:01:35,111.111 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:01:35,149.149 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:35,173.173 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/market/ticker?instId=DOGE-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:35,194.194 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:01:35,194.194 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:35,205.205 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 DOGE-USDT-SWAP: {'adl': '1', 'availPos': '1.52', 'avgPx': '0.23585', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '0.**************', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.179246', 'fundingFee': '0.********', 'gammaBS': '', 'gammaPA': '', 'idxPx': '0.236262', 'imr': '49.**************', 'instId': 'DOGE-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '0.2363', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '86.**************', 'margin': '', 'markPx': '0.23628', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '52234.***********', 'mmr': '2.****************', 'nonSettleAvgPx': '', 'notionalUsd': '359.**************', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '1.52', 'posCcy': '', 'posId': '2720240594715480064', 'posSide': 'short', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.14345912', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '*********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.***************', 'uplRatio': '-0.****************', 'uplRatioLastPx': '-0.****************', 'usdPx': '1.00026', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:01:35,217.217 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:01:35,217.217 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: DOGE-USDT-SWAP = 49.88 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:01:35,240.240 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:01:35,263.263 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:01:35,284.284 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:01:35,571.571 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:35,573.573 - INFO     - src.optimized_prompts.smart_hunter_engine - 🎯 智能猎手开始分析: ETH-USDT-SWAP
[CryptoQuant] 2025-07-27 09:01:35,573.573 - INFO     - src.optimized_prompts.smart_hunter_engine -   - 当前价格: 3849.76
[CryptoQuant] 2025-07-27 09:01:35,573.573 - INFO     - src.optimized_prompts.smart_hunter_engine -   - 24h涨跌: -0.78%
[CryptoQuant] 2025-07-27 09:01:35,574.574 - INFO     - src.optimized_prompts.smart_hunter_engine - 🎯 智能猎手开始AI分析: ETH-USDT-SWAP
[CryptoQuant] 2025-07-27 09:01:35,574.574 - INFO     - src.optimized_prompts.smart_hunter_engine - 智能猎手 - AI调用尝试 1/4
[CryptoQuant] 2025-07-27 09:01:35,796.796 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:35,797.797 - INFO     - src.optimized_prompts.smart_hunter_engine - 🎯 智能猎手开始分析: BTC-USDT-SWAP
[CryptoQuant] 2025-07-27 09:01:35,798.798 - INFO     - src.optimized_prompts.smart_hunter_engine -   - 当前价格: 117962.1
[CryptoQuant] 2025-07-27 09:01:35,798.798 - INFO     - src.optimized_prompts.smart_hunter_engine -   - 24h涨跌: 1.61%
[CryptoQuant] 2025-07-27 09:01:35,799.799 - INFO     - src.optimized_prompts.smart_hunter_engine - 🎯 智能猎手开始AI分析: BTC-USDT-SWAP
[CryptoQuant] 2025-07-27 09:01:35,799.799 - INFO     - src.optimized_prompts.smart_hunter_engine - 智能猎手 - AI调用尝试 1/4
[CryptoQuant] 2025-07-27 09:01:35,832.832 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:36,027.027 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:36,028.028 - INFO     - src.optimized_prompts.risk_guardian_engine - 🛡️ 风险守护者分析持仓: DOGE-USDT-SWAP
[CryptoQuant] 2025-07-27 09:01:36,028.028 - INFO     - src.optimized_prompts.risk_guardian_engine -   - 当前价格: 0.2363
[CryptoQuant] 2025-07-27 09:01:36,029.029 - INFO     - src.optimized_prompts.risk_guardian_engine -   - 开仓均价: 0.23585
[CryptoQuant] 2025-07-27 09:01:36,029.029 - INFO     - src.optimized_prompts.risk_guardian_engine -   - 持仓方向: short
[CryptoQuant] 2025-07-27 09:01:36,030.030 - INFO     - src.optimized_prompts.risk_guardian_engine -   - 杠杆倍数: 7.0x
[CryptoQuant] 2025-07-27 09:01:36,030.030 - INFO     - src.optimized_prompts.risk_guardian_engine -   - 实际盈亏: -1.2822% (-0.64 USDT)
[CryptoQuant] 2025-07-27 09:01:36,030.030 - INFO     - src.optimized_prompts.risk_guardian_engine - 🛡️ 风险守护者开始分析: DOGE-USDT-SWAP
[CryptoQuant] 2025-07-27 09:01:36,031.031 - INFO     - src.optimized_prompts.risk_guardian_engine - 风险守护者 - AI调用尝试 1/4
[CryptoQuant] 2025-07-27 09:01:36,503.503 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:38,199.199 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:38,201.201 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 DOGE-USDT-SWAP: {'adl': '1', 'availPos': '1.52', 'avgPx': '0.23585', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '0.**************', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.179246', 'fundingFee': '0.********', 'gammaBS': '', 'gammaPA': '', 'idxPx': '0.236276', 'imr': '49.**************', 'instId': 'DOGE-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '0.2363', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '86.**************', 'margin': '', 'markPx': '0.23628', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '52234.***********', 'mmr': '2.****************', 'nonSettleAvgPx': '', 'notionalUsd': '359.**************', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '1.52', 'posCcy': '', 'posId': '2720240594715480064', 'posSide': 'short', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.14345912', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '*********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.***************', 'uplRatio': '-0.****************', 'uplRatioLastPx': '-0.****************', 'usdPx': '1.00026', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:01:38,202.202 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: DOGE-USDT-SWAP = 49.88 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:01:38,230.230 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:38,950.950 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:39,671.671 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:41,213.213 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:41,214.214 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 DOGE-USDT-SWAP: {'adl': '1', 'availPos': '1.52', 'avgPx': '0.23585', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '0.**************', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.179246', 'fundingFee': '0.********', 'gammaBS': '', 'gammaPA': '', 'idxPx': '0.236278', 'imr': '49.**************', 'instId': 'DOGE-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '0.2363', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '86.**************', 'margin': '', 'markPx': '0.23628', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '52234.***********', 'mmr': '2.****************', 'nonSettleAvgPx': '', 'notionalUsd': '359.*********', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '1.52', 'posCcy': '', 'posId': '2720240594715480064', 'posSide': 'short', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.14345912', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '*********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.***************', 'uplRatio': '-0.****************', 'uplRatioLastPx': '-0.****************', 'usdPx': '1.00027', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:01:41,215.215 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: DOGE-USDT-SWAP = 49.88 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:01:41,226.226 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:41,998.998 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:42,827.827 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:44,227.227 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:44,228.228 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 DOGE-USDT-SWAP: {'adl': '1', 'availPos': '1.52', 'avgPx': '0.23585', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '0.**************', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.179246', 'fundingFee': '0.********', 'gammaBS': '', 'gammaPA': '', 'idxPx': '0.236278', 'imr': '49.**************', 'instId': 'DOGE-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '0.2363', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '86.**************', 'margin': '', 'markPx': '0.23629', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '52232.***********', 'mmr': '2.3345452', 'nonSettleAvgPx': '', 'notionalUsd': '359.**************', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '1.52', 'posCcy': '', 'posId': '2720240594715480064', 'posSide': 'short', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.14345912', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '*********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.***************', 'uplRatio': '-0.***************', 'uplRatioLastPx': '-0.****************', 'usdPx': '1.00027', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:01:44,228.228 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: DOGE-USDT-SWAP = 49.88 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:01:44,410.410 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:45,193.193 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:45,932.932 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:47,221.221 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:47,223.223 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 DOGE-USDT-SWAP: {'adl': '1', 'availPos': '1.52', 'avgPx': '0.23585', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '0.**************', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.179246', 'fundingFee': '0.********', 'gammaBS': '', 'gammaPA': '', 'idxPx': '0.23627', 'imr': '49.***************', 'instId': 'DOGE-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '0.2363', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '86.**************', 'margin': '', 'markPx': '0.2363', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '52230.***********', 'mmr': '2.334644', 'nonSettleAvgPx': '', 'notionalUsd': '359.********', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '1.52', 'posCcy': '', 'posId': '2720240594715480064', 'posSide': 'short', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.14345912', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '*********', 'uTime': '*************', 'upl': '-0.***************', 'uplLastPx': '-0.***************', 'uplRatio': '-0.****************', 'uplRatioLastPx': '-0.****************', 'usdPx': '1.00027', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:01:47,224.224 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: DOGE-USDT-SWAP = 49.89 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:01:47,253.253 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:48,045.045 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:48,765.765 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:50,219.219 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:50,219.219 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 DOGE-USDT-SWAP: {'adl': '1', 'availPos': '1.52', 'avgPx': '0.23585', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '0.**************', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.179246', 'fundingFee': '0.********', 'gammaBS': '', 'gammaPA': '', 'idxPx': '0.236272', 'imr': '49.**************', 'instId': 'DOGE-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '0.2363', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '86.**************', 'margin': '', 'markPx': '0.23631', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '52228.***********', 'mmr': '2.3347428', 'nonSettleAvgPx': '', 'notionalUsd': '359.**************', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '1.52', 'posCcy': '', 'posId': '2720240594715480064', 'posSide': 'short', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.14345912', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '*********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.***************', 'uplRatio': '-0.****************', 'uplRatioLastPx': '-0.****************', 'usdPx': '1.00026', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:01:50,220.220 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: DOGE-USDT-SWAP = 49.89 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:01:50,239.239 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:50,972.972 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:51,672.672 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:52,953.953 - INFO     - src.config_manager - 置信度参数计算: DOGE-USDT-SWAP
[CryptoQuant] 2025-07-27 09:01:52,954.954 - INFO     - src.config_manager -   置信度: 0.85
[CryptoQuant] 2025-07-27 09:01:52,954.954 - INFO     - src.config_manager -   杠杆: 8.5x (上限: 10x)
[CryptoQuant] 2025-07-27 09:01:52,955.955 - INFO     - src.config_manager -   仓位: 78.37% (上限: 100.0%)
[CryptoQuant] 2025-07-27 09:01:52,955.955 - INFO     - src.config_manager -   止损: 2.75% (上限: 5.0%)
[CryptoQuant] 2025-07-27 09:01:52,956.956 - INFO     - src.config_manager -   止盈: 17.0% (上限: 20.0%)
[CryptoQuant] 2025-07-27 09:01:52,957.957 - INFO     - src.optimized_prompts.risk_guardian_engine - 风险守护者决策完成: DOGE-USDT-SWAP -> CLOSE (置信度: 0.85)
[CryptoQuant] 2025-07-27 09:01:52,958.958 - INFO     - src.multi_contract_manager - 交易逻辑评估: DOGE-USDT-SWAP - AI决策: CLOSE, 当前状态: SHORT_POSITION
[CryptoQuant] 2025-07-27 09:01:52,958.958 - INFO     - src.multi_contract_manager - 交易逻辑结果: DOGE-USDT-SWAP
[CryptoQuant] 2025-07-27 09:01:52,958.958 - INFO     - src.multi_contract_manager -   should_trade: True
[CryptoQuant] 2025-07-27 09:01:52,959.959 - INFO     - src.multi_contract_manager -   reason: AI决策为CLOSE，当前有SHORT_POSITION，执行平仓
[CryptoQuant] 2025-07-27 09:01:52,959.959 - INFO     - src.multi_contract_manager - AI决策为CLOSE，直接执行平仓: DOGE-USDT-SWAP
[CryptoQuant] 2025-07-27 09:01:52,997.997 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:52,998.998 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 DOGE-USDT-SWAP: {'adl': '1', 'availPos': '1.52', 'avgPx': '0.23585', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '0.**************', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.179246', 'fundingFee': '0.********', 'gammaBS': '', 'gammaPA': '', 'idxPx': '0.236277', 'imr': '49.**************', 'instId': 'DOGE-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '0.2363', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '86.**************', 'margin': '', 'markPx': '0.23632', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '52225.************', 'mmr': '2.3348416', 'nonSettleAvgPx': '', 'notionalUsd': '359.*********', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '1.52', 'posCcy': '', 'posId': '2720240594715480064', 'posSide': 'short', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.14345912', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '*********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.***************', 'uplRatio': '-0.****************', 'uplRatioLastPx': '-0.****************', 'usdPx': '1.00026', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:01:52,999.999 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: DOGE-USDT-SWAP = 49.89 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:01:53,057.057 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:53,694.694 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions?instId=DOGE-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:53,695.695 - INFO     - src.deprecated.trade_executor - 实时持仓检查: DOGE-USDT-SWAP 持仓大小=1.52, 保证金模式=cross, 持仓方向=short
[CryptoQuant] 2025-07-27 09:01:53,695.695 - INFO     - src.deprecated.trade_executor - 执行平多头仓位: 1.52 (尝试 1/3) 保证金模式=cross 持仓方向=short
[CryptoQuant] 2025-07-27 09:01:53,858.858 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:54,485.485 - INFO     - src.config_manager - 置信度参数计算: ETH-USDT-SWAP
[CryptoQuant] 2025-07-27 09:01:54,485.485 - INFO     - src.config_manager -   置信度: 0.55
[CryptoQuant] 2025-07-27 09:01:54,486.486 - INFO     - src.config_manager -   杠杆: 5.5x (上限: 10x)
[CryptoQuant] 2025-07-27 09:01:54,486.486 - INFO     - src.config_manager -   仓位: 40.79% (上限: 100.0%)
[CryptoQuant] 2025-07-27 09:01:54,487.487 - INFO     - src.config_manager -   止损: 4.25% (上限: 5.0%)
[CryptoQuant] 2025-07-27 09:01:54,487.487 - INFO     - src.config_manager -   止盈: 11.0% (上限: 20.0%)
[CryptoQuant] 2025-07-27 09:01:54,488.488 - INFO     - src.optimized_prompts.smart_hunter_engine - 智能猎手决策完成: ETH-USDT-SWAP -> HOLD (置信度: 0.55)
[CryptoQuant] 2025-07-27 09:01:54,489.489 - INFO     - src.multi_contract_manager - 交易逻辑评估: ETH-USDT-SWAP - AI决策: HOLD, 当前状态: NO_POSITION
[CryptoQuant] 2025-07-27 09:01:54,490.490 - INFO     - src.multi_contract_manager - 交易逻辑结果: ETH-USDT-SWAP
[CryptoQuant] 2025-07-27 09:01:54,490.490 - INFO     - src.multi_contract_manager -   should_trade: False
[CryptoQuant] 2025-07-27 09:01:54,491.491 - INFO     - src.multi_contract_manager -   reason: AI决策为HOLD，当前无持仓，保持当前状态
[CryptoQuant] 2025-07-27 09:01:54,671.671 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:55,676.676 - INFO     - src.multi_contract_manager - 交易对处理成功: ETH-USDT-SWAP
[CryptoQuant] 2025-07-27 09:01:55,993.993 - INFO     - httpx - HTTP Request: POST https://www.okx.com/api/v5/trade/close-position "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:55,994.994 - INFO     - src.deprecated.trade_executor - 平仓成功: DOGE-USDT-SWAP
[CryptoQuant] 2025-07-27 09:01:56,156.156 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:56,267.267 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:57,010.010 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:57,710.710 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:58,908.908 - INFO     - src.config_manager - 置信度参数计算: BTC-USDT-SWAP
[CryptoQuant] 2025-07-27 09:01:58,908.908 - INFO     - src.config_manager -   置信度: 0.72
[CryptoQuant] 2025-07-27 09:01:58,909.909 - INFO     - src.config_manager -   杠杆: 7.2x (上限: 10x)
[CryptoQuant] 2025-07-27 09:01:58,909.909 - INFO     - src.config_manager -   仓位: 61.09% (上限: 100.0%)
[CryptoQuant] 2025-07-27 09:01:58,909.909 - INFO     - src.config_manager -   止损: 3.4% (上限: 5.0%)
[CryptoQuant] 2025-07-27 09:01:58,910.910 - INFO     - src.config_manager -   止盈: 14.4% (上限: 20.0%)
[CryptoQuant] 2025-07-27 09:01:58,911.911 - INFO     - src.optimized_prompts.smart_hunter_engine - 智能猎手决策完成: BTC-USDT-SWAP -> BUY (置信度: 0.72)
[CryptoQuant] 2025-07-27 09:01:58,911.911 - INFO     - src.multi_contract_manager - 交易逻辑评估: BTC-USDT-SWAP - AI决策: BUY, 当前状态: NO_POSITION
[CryptoQuant] 2025-07-27 09:01:58,913.913 - INFO     - src.multi_contract_manager - 交易逻辑结果: BTC-USDT-SWAP
[CryptoQuant] 2025-07-27 09:01:58,913.913 - INFO     - src.multi_contract_manager -   should_trade: True
[CryptoQuant] 2025-07-27 09:01:58,913.913 - INFO     - src.multi_contract_manager -   reason: AI决策为BUY，当前无持仓，执行开多头
[CryptoQuant] 2025-07-27 09:01:58,914.914 - DEBUG    - src.risk_manager - 🔄 调用兼容接口 check_pre_trade_risk: BTC-USDT-SWAP
[CryptoQuant] 2025-07-27 09:01:58,914.914 - INFO     - src.risk_manager - 🛡️ 开始风险检查: BTC-USDT-SWAP
[CryptoQuant] 2025-07-27 09:01:58,915.915 - DEBUG    - src.risk_manager - 📊 输入参数 - 决策: {'decision': 'BUY', 'confidence': 0.72, 'reasoning': '1. 市场环境识别：ADX(30.3)>25且多周期(15m/5m/1m)呈现看涨信号，符合趋势市场特征。2. 技术面分析：短期周期(15m/5m/1m)RSI中性偏强(50.9-59.6)，MACD在5m/1m周期出现正向柱状图，布林带位置(0.65-0.90)显示价格接近上轨但未超买。3. 资金面分析：小周期(5m/1m)成交量环比放大(0.3x-0.6x)，但大周期缩量需警惕持续性。4. 情绪面分析：1.6%的日内波动率显示市场活跃度适中，未出现极端情绪。', 'leverage': 7.2, 'stop_loss': 3.4, 'take_profit': 14.4, 'investment_amount': 50, 'position_size': 0.05, 'confidence_params': {'should_trade': True, 'leverage': 7.2, 'position_ratio': 61.09, 'stop_loss': 3.4, 'take_profit': 14.4, 'confidence': 0.72, 'max_limits': {'max_leverage': 10, 'max_position_size': 100.0, 'max_stop_loss': 5.0, 'max_take_profit': 20.0}}}
[CryptoQuant] 2025-07-27 09:01:58,915.915 - DEBUG    - src.risk_manager - 💰 输入参数 - 余额: {'USDT': {'available': 131269.54704439567, 'frozen': 49.**************, 'total': 131319.42837772903, 'equity': 131320.08197772902}}
[CryptoQuant] 2025-07-27 09:01:58,916.916 - DEBUG    - src.risk_manager - 📈 输入参数 - 持仓: {}
[CryptoQuant] 2025-07-27 09:01:58,916.916 - DEBUG    - src.risk_manager - 💰 余额信息: 可用=131269.54704439567 USDT, 总计=131319.42837772903 USDT
[CryptoQuant] 2025-07-27 09:01:58,917.917 - DEBUG    - src.risk_manager - 🔧 风控参数: 最小余额=10.0 USDT, 安全边际=10.0%
[CryptoQuant] 2025-07-27 09:01:58,917.917 - DEBUG    - src.risk_manager - 📊 资金需求分析: 仓位大小=0.050, 所需资金=7222.57 USDT
[CryptoQuant] 2025-07-27 09:01:58,918.918 - DEBUG    - src.risk_manager - 📊 仓位分析: BTC-USDT-SWAP 当前比例=0.00%, 新增比例=5.00%, 新增后比例=5.00%, 最大允许=5.00%
[CryptoQuant] 2025-07-27 09:01:58,918.918 - DEBUG    - src.risk_manager - 📈 持仓统计: 当前活跃=0, 最大允许=3
[CryptoQuant] 2025-07-27 09:01:58,919.919 - INFO     - src.risk_manager - ✅ 风险检查通过: BTC-USDT-SWAP, 允许仓位: 0.05
[CryptoQuant] 2025-07-27 09:01:58,919.919 - INFO     - src.multi_contract_manager - 仓位大小调整: BTC-USDT-SWAP
[CryptoQuant] 2025-07-27 09:01:58,920.920 - INFO     - src.multi_contract_manager -   原始仓位: 0.05
[CryptoQuant] 2025-07-27 09:01:58,920.920 - INFO     - src.multi_contract_manager -   风险允许: 0.05
[CryptoQuant] 2025-07-27 09:01:58,920.920 - INFO     - src.multi_contract_manager -   最终仓位: 0.05
[CryptoQuant] 2025-07-27 09:01:58,921.921 - INFO     - src.multi_contract_manager - 执行交易决策: BTC-USDT-SWAP -> BUY
[CryptoQuant] 2025-07-27 09:01:59,149.149 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:59,224.224 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:59,660.660 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions?instId=BTC-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:01:59,661.661 - INFO     - src.deprecated.trade_executor - 设置杠杆: BTC-USDT-SWAP 1x -> 7.2x
[CryptoQuant] 2025-07-27 09:01:59,992.992 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:00,654.654 - INFO     - httpx - HTTP Request: POST https://www.okx.com/api/v5/account/set-leverage "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:00,655.655 - INFO     - src.deprecated.trade_executor - 杠杆设置成功: BTC-USDT-SWAP = 7.2x
[CryptoQuant] 2025-07-27 09:02:00,758.758 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:02,162.162 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:02,257.257 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:02,435.435 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/market/ticker?instId=BTC-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:03,018.018 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:03,728.728 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:05,059.059 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/instruments?instType=SWAP&instId=BTC-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:05,060.060 - INFO     - src.deprecated.trade_executor - 🎯 使用OKX合约规格精确计算:
[CryptoQuant] 2025-07-27 09:02:05,061.061 - INFO     - src.deprecated.trade_executor -   - 合约: BTC-USDT-SWAP
[CryptoQuant] 2025-07-27 09:02:05,061.061 - INFO     - src.deprecated.trade_executor -   - 合约面值(ctVal): 0.01
[CryptoQuant] 2025-07-27 09:02:05,061.061 - INFO     - src.deprecated.trade_executor -   - 合约乘数(ctMult): 1.0
[CryptoQuant] 2025-07-27 09:02:05,062.062 - INFO     - src.deprecated.trade_executor -   - 当前价格: 117837.8
[CryptoQuant] 2025-07-27 09:02:05,062.062 - INFO     - src.deprecated.trade_executor -   - 目标保证金: 50 USDT
[CryptoQuant] 2025-07-27 09:02:05,062.062 - INFO     - src.deprecated.trade_executor -   - 杠杆倍数: 7.2x
[CryptoQuant] 2025-07-27 09:02:05,063.063 - INFO     - src.deprecated.trade_executor -   - 名义价值: 360.0 USDT
[CryptoQuant] 2025-07-27 09:02:05,063.063 - INFO     - src.deprecated.trade_executor -   - 计算张数: 360.0 ÷ (0.01 × 1.0 × 117837.8) = 0.305505
[CryptoQuant] 2025-07-27 09:02:05,194.194 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:05,225.225 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:05,678.678 - INFO     - src.multi_contract_manager - 交易对处理成功: DOGE-USDT-SWAP
[CryptoQuant] 2025-07-27 09:02:05,919.919 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:06,599.599 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:06,787.787 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/instruments?instType=SWAP&instId=BTC-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:06,788.788 - INFO     - src.deprecated.trade_executor - 精度调整详情: 原始数量=0.305505, 最小数量=0.01, 精度单位=0.01, 调整后=0.3
[CryptoQuant] 2025-07-27 09:02:06,789.789 - INFO     - src.deprecated.trade_executor - 🔍 最终下单参数: symbol=BTC-USDT-SWAP, size=0.3, 目标保证金=50 USDT
[CryptoQuant] 2025-07-27 09:02:08,125.125 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:08,233.233 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:08,778.778 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:09,440.440 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:09,490.490 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/instruments?instType=SWAP&instId=BTC-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:09,493.493 - INFO     - src.deprecated.trade_executor - 精度调整详情: 原始数量=0.300000, 最小数量=0.01, 精度单位=0.01, 调整后=0.3
[CryptoQuant] 2025-07-27 09:02:09,494.494 - INFO     - src.deprecated.trade_executor - 🔄 下单尝试 1/3: BTC-USDT-SWAP, size=0.3
[CryptoQuant] 2025-07-27 09:02:11,153.153 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:11,227.227 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:11,292.292 - INFO     - httpx - HTTP Request: POST https://www.okx.com/api/v5/trade/order "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:11,294.294 - INFO     - src.deprecated.trade_executor - ✅ 下单成功: BTC-USDT-SWAP
[CryptoQuant] 2025-07-27 09:02:11,294.294 - INFO     - src.deprecated.trade_executor - 🔍 下单响应: {'code': '0', 'data': [{'clOrdId': '', 'ordId': '2720450128084013056', 'sCode': '0', 'sMsg': 'Order placed', 'tag': '', 'ts': '*************'}], 'inTime': '****************', 'msg': '', 'outTime': '****************'}
[CryptoQuant] 2025-07-27 09:02:11,295.295 - INFO     - src.deprecated.trade_executor - 多头开仓成功，订单ID: 2720450128084013056
[CryptoQuant] 2025-07-27 09:02:11,858.858 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:12,556.556 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:12,844.844 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/trade/order?instId=BTC-USDT-SWAP&ordId=2720450128084013056 "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:12,846.846 - INFO     - src.deprecated.trade_executor - 🔍 订单执行详情:
[CryptoQuant] 2025-07-27 09:02:12,846.846 - INFO     - src.deprecated.trade_executor -   - 请求数量: 0.3
[CryptoQuant] 2025-07-27 09:02:12,846.846 - INFO     - src.deprecated.trade_executor -   - 实际成交数量: 0.3
[CryptoQuant] 2025-07-27 09:02:12,847.847 - INFO     - src.deprecated.trade_executor -   - 成交均价: 117924.8
[CryptoQuant] 2025-07-27 09:02:12,848.848 - INFO     - src.deprecated.trade_executor -   - 实际成交价值: 353.77 USDT
[CryptoQuant] 2025-07-27 09:02:12,848.848 - INFO     - src.deprecated.trade_executor -   - 杠杆倍数: 7.2x
[CryptoQuant] 2025-07-27 09:02:12,849.849 - INFO     - src.deprecated.trade_executor -   - 实际保证金: 49.14 USDT
[CryptoQuant] 2025-07-27 09:02:12,849.849 - INFO     - src.deprecated.trade_executor - 止盈止损参数检查: stop_loss=3.4, take_profit=14.4
[CryptoQuant] 2025-07-27 09:02:14,197.197 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:14,441.441 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:14,443.443 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117938.2', 'imr': '49.**************', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117930', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117894.4', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82508.***********', 'mmr': '1.4147328', 'nonSettleAvgPx': '', 'notionalUsd': '353.*********', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.0912000000000262', 'uplLastPx': '0.****************', 'uplRatio': '-0.0018560981235503', 'uplRatioLastPx': '0.****************', 'usdPx': '1.00028', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:02:14,444.444 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.12 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:02:14,661.661 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/market/ticker?instId=BTC-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:14,870.870 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:15,198.198 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/market/ticker?instId=BTC-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:15,199.199 - INFO     - src.deprecated.trade_executor - 🔍 止盈验证 - 目标收益率: 14.4%
[CryptoQuant] 2025-07-27 09:02:15,200.200 - INFO     - src.deprecated.trade_executor -   - 杠杆倍数: 7.2x
[CryptoQuant] 2025-07-27 09:02:15,200.200 - INFO     - src.deprecated.trade_executor -   - 要求价格变动: 2.000000%
[CryptoQuant] 2025-07-27 09:02:15,533.533 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:16,518.518 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/instruments?instType=SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:17,216.216 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:17,218.218 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:17,219.219 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117937.8', 'imr': '49.**************', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117935.7', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117893.5', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82509.***********', 'mmr': '1.414722', 'nonSettleAvgPx': '', 'notionalUsd': '353.********', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '0.****************', 'uplRatio': '-0.***************', 'uplRatioLastPx': '0.***************', 'usdPx': '1.00028', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:02:17,220.220 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.12 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:02:17,884.884 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:18,311.311 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/instruments?instType=SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:18,314.314 - INFO     - src.deprecated.trade_executor - 杠杆止盈止损价格计算: BTC-USDT-SWAP
[CryptoQuant] 2025-07-27 09:02:18,315.315 - INFO     - src.deprecated.trade_executor -   - 当前价格: 117930.0
[CryptoQuant] 2025-07-27 09:02:18,316.316 - INFO     - src.deprecated.trade_executor -   - 杠杆倍数: 7.2x
[CryptoQuant] 2025-07-27 09:02:18,316.316 - INFO     - src.deprecated.trade_executor -   - 目标保证金止损: 3.4% -> 价格变化: 0.4722% -> 止损价格: 117373.1
[CryptoQuant] 2025-07-27 09:02:18,316.316 - INFO     - src.deprecated.trade_executor -   - 目标保证金止盈: 14.4% -> 价格变化: 2.0000% -> 止盈价格: 120288.6
[CryptoQuant] 2025-07-27 09:02:18,317.317 - INFO     - src.deprecated.trade_executor -   - 持仓数量: 0.3
[CryptoQuant] 2025-07-27 09:02:18,621.621 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:18,860.860 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/market/ticker?instId=BTC-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:20,169.169 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:20,183.183 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:20,185.185 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117937.9', 'imr': '49.**************', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117930', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117884.3', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82515.***********', 'mmr': '1.4146116', 'nonSettleAvgPx': '', 'notionalUsd': '353.**************', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.1215', 'uplLastPx': '0.****************', 'uplRatio': '-0.0024727623027555', 'uplRatioLastPx': '0.****************', 'usdPx': '1.00028', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:02:20,185.185 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.12 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:02:20,258.258 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/instruments?instType=SWAP&instId=BTC-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:20,673.673 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/instruments?instType=SWAP&instId=BTC-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:20,674.674 - INFO     - src.deprecated.trade_executor - OCO订单参数: {'instId': 'BTC-USDT-SWAP', 'tdMode': 'cross', 'side': 'sell', 'ordType': 'oco', 'sz': '0.3', 'posSide': 'long', 'tpTriggerPx': '120288.6', 'tpOrdPx': '-1', 'slTriggerPx': '117373.1', 'slOrdPx': '-1'}
[CryptoQuant] 2025-07-27 09:02:20,878.878 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:21,586.586 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:23,110.110 - INFO     - httpx - HTTP Request: POST https://www.okx.com/api/v5/trade/order-algo "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:23,111.111 - INFO     - src.deprecated.trade_executor - ✅ OCO订单设置成功: BTC-USDT-SWAP
[CryptoQuant] 2025-07-27 09:02:23,112.112 - INFO     - src.deprecated.trade_executor -   - 止损价格: 117373.1 (原始: 117373.1)
[CryptoQuant] 2025-07-27 09:02:23,112.112 - INFO     - src.deprecated.trade_executor -   - 止盈价格: 120288.6 (原始: 120288.6)
[CryptoQuant] 2025-07-27 09:02:23,113.113 - INFO     - src.deprecated.trade_executor -   - 当前市价: 117930.0
[CryptoQuant] 2025-07-27 09:02:23,113.113 - INFO     - src.deprecated.trade_executor -   - 订单ID: 2720450521449451520
[CryptoQuant] 2025-07-27 09:02:23,114.114 - INFO     - src.deprecated.trade_executor - BUY决策执行成功: BTC-USDT-SWAP 数量=0.3
[CryptoQuant] 2025-07-27 09:02:23,141.141 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:23,142.142 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117937.9', 'imr': '49.118375', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117930', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117884.1', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82515.***********', 'mmr': '1.****************', 'nonSettleAvgPx': '', 'notionalUsd': '353.**************', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '0.****************', 'uplRatio': '-0.****************', 'uplRatioLastPx': '0.****************', 'usdPx': '1.00027', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:02:23,143.143 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.12 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:02:23,203.203 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:23,874.874 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:24,574.574 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:25,680.680 - INFO     - src.multi_contract_manager - 交易对处理成功: BTC-USDT-SWAP
[CryptoQuant] 2025-07-27 09:02:26,179.179 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:26,184.184 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:26,185.185 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117937.6', 'imr': '49.11825', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117805', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117883.8', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82516.***********', 'mmr': '1.4146056', 'nonSettleAvgPx': '', 'notionalUsd': '353.*********', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.123', 'uplLastPx': '-0.****************', 'uplRatio': '-0.****************', 'uplRatioLastPx': '-0.****************', 'usdPx': '1.00027', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:02:26,186.186 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.12 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:02:26,410.410 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions?instId=BTC-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:26,411.411 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117937.6', 'imr': '49.**************', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117935.7', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117885.8', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82514.***********', 'mmr': '1.4146296', 'nonSettleAvgPx': '', 'notionalUsd': '353.*********', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.117', 'uplLastPx': '0.****************', 'uplRatio': '-0.****************', 'uplRatioLastPx': '0.***************', 'usdPx': '1.00027', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:02:26,412.412 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.12 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:02:26,561.561 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions?instId=ETH-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:26,565.565 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions?instId=DOGE-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:26,909.909 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:27,067.067 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions?instId=BTC-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:27,068.068 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117937.6', 'imr': '49.**************', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117926.8', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117883.6', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82516.***********', 'mmr': '1.****************', 'nonSettleAvgPx': '', 'notionalUsd': '353.*********', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.1235999999999913', 'uplLastPx': '0.006', 'uplRatio': '-0.00**************', 'uplRatioLastPx': '0.****************', 'usdPx': '1.00027', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:02:27,069.069 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.12 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:02:27,069.069 - INFO     - src.multi_contract_manager - 持仓状态分析: BTC-USDT-SWAP - LONG_POSITION - 大小: 0.3
[CryptoQuant] 2025-07-27 09:02:27,235.235 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions?instId=ETH-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:27,236.236 - INFO     - src.multi_contract_manager - 持仓状态分析: ETH-USDT-SWAP - NO_POSITION - 大小: 0
[CryptoQuant] 2025-07-27 09:02:27,319.319 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions?instId=DOGE-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:27,321.321 - INFO     - src.multi_contract_manager - 持仓状态分析: DOGE-USDT-SWAP - NO_POSITION - 大小: 0
[CryptoQuant] 2025-07-27 09:02:27,608.608 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:28,850.850 - WARNING  - src.deprecated.data_fetcher - 🔴 连接问题检测到，1.5秒后重试 (1/5): Server disconnected
[CryptoQuant] 2025-07-27 09:02:28,851.851 - WARNING  - src.deprecated.data_fetcher - 🔴 连接问题检测到，1.5秒后重试 (1/5): Server disconnected
[CryptoQuant] 2025-07-27 09:02:28,851.851 - WARNING  - src.deprecated.data_fetcher - 🔴 连接问题检测到，1.5秒后重试 (1/5): Server disconnected
[CryptoQuant] 2025-07-27 09:02:29,133.133 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:29,201.201 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:29,202.202 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117937.6', 'imr': '49.**************', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117930', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117878.3', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82519.***********', 'mmr': '1.****************', 'nonSettleAvgPx': '', 'notionalUsd': '353.**************', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.1395', 'uplLastPx': '0.****************', 'uplRatio': '-0.****************', 'uplRatioLastPx': '0.****************', 'usdPx': '1.00027', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:02:29,203.203 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.12 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:02:29,778.778 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:30,460.460 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:32,125.125 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:32,191.191 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/market/candles?instId=BTC-USDT-SWAP&bar=1m&limit=100 "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:32,192.192 - INFO     - src.deprecated.data_fetcher - 🟢 API请求在第2次尝试后成功恢复
[CryptoQuant] 2025-07-27 09:02:32,201.201 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:32,202.202 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117937.4', 'imr': '49.**************', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117926.8', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117881.2', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82517.***********', 'mmr': '1.4145744', 'nonSettleAvgPx': '', 'notionalUsd': '353.*********', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.1308000000000175', 'uplLastPx': '0.006', 'uplRatio': '-0.0026620354666703', 'uplRatioLastPx': '0.****************', 'usdPx': '1.00028', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:02:32,203.203 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.12 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:02:32,540.540 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/market/candles?instId=DOGE-USDT-SWAP&bar=1m&limit=100 "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:32,541.541 - INFO     - src.deprecated.data_fetcher - 🟢 API请求在第2次尝试后成功恢复
[CryptoQuant] 2025-07-27 09:02:32,742.742 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:33,023.023 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/market/ticker?instId=BTC-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:33,023.023 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/market/candles?instId=ETH-USDT-SWAP&bar=1m&limit=100 "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:33,045.045 - INFO     - src.deprecated.data_fetcher - 🟢 API请求在第2次尝试后成功恢复
[CryptoQuant] 2025-07-27 09:02:33,046.046 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:02:33,077.077 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:02:33,099.099 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:02:33,120.120 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:02:33,142.142 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:02:33,188.188 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/market/ticker?instId=DOGE-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:33,208.208 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:02:33,231.231 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:02:33,254.254 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:02:33,278.278 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:02:33,301.301 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:02:33,392.392 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:33,691.691 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/market/ticker?instId=ETH-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:33,710.710 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:02:33,733.733 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:02:33,756.756 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:02:33,785.785 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:02:33,807.807 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:02:33,822.822 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:33,824.824 - INFO     - src.optimized_prompts.risk_guardian_engine - 🛡️ 风险守护者分析持仓: BTC-USDT-SWAP
[CryptoQuant] 2025-07-27 09:02:33,824.824 - INFO     - src.optimized_prompts.risk_guardian_engine -   - 当前价格: 117930.0
[CryptoQuant] 2025-07-27 09:02:33,824.824 - INFO     - src.optimized_prompts.risk_guardian_engine -   - 开仓均价: 117924.8
[CryptoQuant] 2025-07-27 09:02:33,825.825 - INFO     - src.optimized_prompts.risk_guardian_engine -   - 持仓方向: long
[CryptoQuant] 2025-07-27 09:02:33,825.825 - INFO     - src.optimized_prompts.risk_guardian_engine -   - 杠杆倍数: 7.0x
[CryptoQuant] 2025-07-27 09:02:33,825.825 - INFO     - src.optimized_prompts.risk_guardian_engine -   - 实际盈亏: -0.2381% (-0.12 USDT)
[CryptoQuant] 2025-07-27 09:02:33,826.826 - INFO     - src.optimized_prompts.risk_guardian_engine - 🛡️ 风险守护者开始分析: BTC-USDT-SWAP
[CryptoQuant] 2025-07-27 09:02:33,826.826 - INFO     - src.optimized_prompts.risk_guardian_engine - 风险守护者 - AI调用尝试 1/4
[CryptoQuant] 2025-07-27 09:02:33,990.990 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:33,991.991 - INFO     - src.optimized_prompts.smart_hunter_engine - 🎯 智能猎手开始分析: DOGE-USDT-SWAP
[CryptoQuant] 2025-07-27 09:02:33,992.992 - INFO     - src.optimized_prompts.smart_hunter_engine -   - 当前价格: 0.236
[CryptoQuant] 2025-07-27 09:02:33,992.992 - INFO     - src.optimized_prompts.smart_hunter_engine -   - 24h涨跌: 0.51%
[CryptoQuant] 2025-07-27 09:02:33,993.993 - INFO     - src.optimized_prompts.smart_hunter_engine - 🎯 智能猎手开始AI分析: DOGE-USDT-SWAP
[CryptoQuant] 2025-07-27 09:02:33,994.994 - INFO     - src.optimized_prompts.smart_hunter_engine - 智能猎手 - AI调用尝试 1/4
[CryptoQuant] 2025-07-27 09:02:34,537.537 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:34,539.539 - INFO     - src.optimized_prompts.smart_hunter_engine - 🎯 智能猎手开始分析: ETH-USDT-SWAP
[CryptoQuant] 2025-07-27 09:02:34,539.539 - INFO     - src.optimized_prompts.smart_hunter_engine -   - 当前价格: 3857.06
[CryptoQuant] 2025-07-27 09:02:34,540.540 - INFO     - src.optimized_prompts.smart_hunter_engine -   - 24h涨跌: -0.56%
[CryptoQuant] 2025-07-27 09:02:34,540.540 - INFO     - src.optimized_prompts.smart_hunter_engine - 🎯 智能猎手开始AI分析: ETH-USDT-SWAP
[CryptoQuant] 2025-07-27 09:02:34,540.540 - INFO     - src.optimized_prompts.smart_hunter_engine - 智能猎手 - AI调用尝试 1/4
[CryptoQuant] 2025-07-27 09:02:35,100.100 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:35,101.101 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117937.4', 'imr': '49.***************', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117930', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117876', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82521.***********', 'mmr': '1.414512', 'nonSettleAvgPx': '', 'notionalUsd': '353.**************', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '0.****************', 'uplRatio': '-0.****************', 'uplRatioLastPx': '0.****************', 'usdPx': '1.00029', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:02:35,102.102 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.11 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:02:35,149.149 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:35,797.797 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:36,447.447 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:38,175.175 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:38,205.205 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:38,206.206 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117936.6', 'imr': '49.***************', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117930', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117879.4', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82519.***********', 'mmr': '1.4145528', 'nonSettleAvgPx': '', 'notionalUsd': '353.**************', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '0.****************', 'uplRatio': '-0.****************', 'uplRatioLastPx': '0.****************', 'usdPx': '1.00029', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:02:38,207.207 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.12 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:02:38,950.950 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:39,615.615 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:41,140.140 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:41,172.172 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:41,174.174 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117936.4', 'imr': '49.1165', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117805', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117879.6', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82519.**********', 'mmr': '1.****************', 'nonSettleAvgPx': '', 'notionalUsd': '353.**************', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.****************', 'uplRatio': '-0.***************', 'uplRatioLastPx': '-0.****************', 'usdPx': '1.00029', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:02:41,174.174 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.12 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:02:41,807.807 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:42,496.496 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:44,173.173 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:44,202.202 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:44,203.203 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117935.5', 'imr': '49.**************', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117948.4', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117887.6', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82513.**********', 'mmr': '1.4146512', 'nonSettleAvgPx': '', 'notionalUsd': '353.**************', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '0.****************', 'uplRatio': '-0.****************', 'uplRatioLastPx': '0.****************', 'usdPx': '1.00029', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:02:44,204.204 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.12 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:02:44,862.862 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:45,399.399 - INFO     - src.config_manager - 置信度参数计算: DOGE-USDT-SWAP
[CryptoQuant] 2025-07-27 09:02:45,399.399 - INFO     - src.config_manager -   置信度: 0.45
[CryptoQuant] 2025-07-27 09:02:45,400.400 - INFO     - src.config_manager -   杠杆: 4.5x (上限: 10x)
[CryptoQuant] 2025-07-27 09:02:45,401.401 - INFO     - src.config_manager -   仓位: 30.19% (上限: 100.0%)
[CryptoQuant] 2025-07-27 09:02:45,401.401 - INFO     - src.config_manager -   止损: 4.75% (上限: 5.0%)
[CryptoQuant] 2025-07-27 09:02:45,402.402 - INFO     - src.config_manager -   止盈: 9.0% (上限: 20.0%)
[CryptoQuant] 2025-07-27 09:02:45,403.403 - INFO     - src.optimized_prompts.smart_hunter_engine - 智能猎手决策完成: DOGE-USDT-SWAP -> HOLD (置信度: 0.45)
[CryptoQuant] 2025-07-27 09:02:45,404.404 - INFO     - src.multi_contract_manager - 交易逻辑评估: DOGE-USDT-SWAP - AI决策: HOLD, 当前状态: NO_POSITION
[CryptoQuant] 2025-07-27 09:02:45,404.404 - INFO     - src.multi_contract_manager - 交易逻辑结果: DOGE-USDT-SWAP
[CryptoQuant] 2025-07-27 09:02:45,404.404 - INFO     - src.multi_contract_manager -   should_trade: False
[CryptoQuant] 2025-07-27 09:02:45,405.405 - INFO     - src.multi_contract_manager -   reason: AI决策为HOLD，当前无持仓，保持当前状态
[CryptoQuant] 2025-07-27 09:02:45,583.583 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:45,684.684 - INFO     - src.multi_contract_manager - 交易对处理成功: DOGE-USDT-SWAP
[CryptoQuant] 2025-07-27 09:02:47,108.108 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:47,109.109 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117935.5', 'imr': '49.***************', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117805', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117895.2', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82508.***********', 'mmr': '1.4147424', 'nonSettleAvgPx': '', 'notionalUsd': '353.**************', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.****************', 'uplRatio': '-0.****************', 'uplRatioLastPx': '-0.****************', 'usdPx': '1.00029', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:02:47,110.110 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.12 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:02:47,159.159 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:47,845.845 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:48,524.524 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:48,570.570 - INFO     - src.config_manager - 置信度参数计算: ETH-USDT-SWAP
[CryptoQuant] 2025-07-27 09:02:48,570.570 - INFO     - src.config_manager -   置信度: 0.72
[CryptoQuant] 2025-07-27 09:02:48,571.571 - INFO     - src.config_manager -   杠杆: 7.2x (上限: 10x)
[CryptoQuant] 2025-07-27 09:02:48,572.572 - INFO     - src.config_manager -   仓位: 61.09% (上限: 100.0%)
[CryptoQuant] 2025-07-27 09:02:48,572.572 - INFO     - src.config_manager -   止损: 3.4% (上限: 5.0%)
[CryptoQuant] 2025-07-27 09:02:48,573.573 - INFO     - src.config_manager -   止盈: 14.4% (上限: 20.0%)
[CryptoQuant] 2025-07-27 09:02:48,574.574 - INFO     - src.optimized_prompts.smart_hunter_engine - 智能猎手决策完成: ETH-USDT-SWAP -> BUY (置信度: 0.72)
[CryptoQuant] 2025-07-27 09:02:48,575.575 - INFO     - src.multi_contract_manager - 交易逻辑评估: ETH-USDT-SWAP - AI决策: BUY, 当前状态: NO_POSITION
[CryptoQuant] 2025-07-27 09:02:48,576.576 - INFO     - src.multi_contract_manager - 交易逻辑结果: ETH-USDT-SWAP
[CryptoQuant] 2025-07-27 09:02:48,577.577 - INFO     - src.multi_contract_manager -   should_trade: True
[CryptoQuant] 2025-07-27 09:02:48,577.577 - INFO     - src.multi_contract_manager -   reason: AI决策为BUY，当前无持仓，执行开多头
[CryptoQuant] 2025-07-27 09:02:48,578.578 - DEBUG    - src.risk_manager - 🔄 调用兼容接口 check_pre_trade_risk: ETH-USDT-SWAP
[CryptoQuant] 2025-07-27 09:02:48,579.579 - INFO     - src.risk_manager - 🛡️ 开始风险检查: ETH-USDT-SWAP
[CryptoQuant] 2025-07-27 09:02:48,580.580 - DEBUG    - src.risk_manager - 📊 输入参数 - 决策: {'decision': 'BUY', 'confidence': 0.72, 'reasoning': '1. 市场环境：多周期（1H至1m）均显示看涨信号，ADX在1m周期达到21.2（>20），短期趋势明确，符合趋势市场特征。\n2. 技术面：RSI（51.6-60.8）未超买，MACD在1m/5m周期呈正值加速，布林带位置（0.68-0.96）显示价格在中上轨运行，技术指标共振支持多头。\n3. 资金面：1m周期成交量放大至1.2倍，量价齐升，但大周期缩量需警惕持续性。\n4. 情绪面：短期波动率正常，价格未出现异常波动，多周期趋势一致性强化多头情绪。\n5. 风险：大周期缩量可能限制趋势强度，需防范回调风险。', 'leverage': 7.2, 'stop_loss': 3.4, 'take_profit': 14.4, 'investment_amount': 50, 'position_size': 0.05, 'confidence_params': {'should_trade': True, 'leverage': 7.2, 'position_ratio': 61.09, 'stop_loss': 3.4, 'take_profit': 14.4, 'confidence': 0.72, 'max_limits': {'max_leverage': 10, 'max_position_size': 100.0, 'max_stop_loss': 5.0, 'max_take_profit': 20.0}}}
[CryptoQuant] 2025-07-27 09:02:48,581.581 - DEBUG    - src.risk_manager - 💰 输入参数 - 余额: {'USDT': {'available': 131269.63035152902, 'frozen': 49.115375, 'total': 131318.74572652904, 'equity': 131318.88942652903}}
[CryptoQuant] 2025-07-27 09:02:48,582.582 - DEBUG    - src.risk_manager - 📈 输入参数 - 持仓: {}
[CryptoQuant] 2025-07-27 09:02:48,583.583 - DEBUG    - src.risk_manager - 💰 余额信息: 可用=131269.63035152902 USDT, 总计=131318.74572652904 USDT
[CryptoQuant] 2025-07-27 09:02:48,584.584 - DEBUG    - src.risk_manager - 🔧 风控参数: 最小余额=10.0 USDT, 安全边际=10.0%
[CryptoQuant] 2025-07-27 09:02:48,585.585 - DEBUG    - src.risk_manager - 📊 资金需求分析: 仓位大小=0.050, 所需资金=7222.53 USDT
[CryptoQuant] 2025-07-27 09:02:48,586.586 - DEBUG    - src.risk_manager - 📊 仓位分析: ETH-USDT-SWAP 当前比例=0.00%, 新增比例=5.00%, 新增后比例=5.00%, 最大允许=5.00%
[CryptoQuant] 2025-07-27 09:02:48,587.587 - DEBUG    - src.risk_manager - 📈 持仓统计: 当前活跃=0, 最大允许=3
[CryptoQuant] 2025-07-27 09:02:48,588.588 - INFO     - src.risk_manager - ✅ 风险检查通过: ETH-USDT-SWAP, 允许仓位: 0.05
[CryptoQuant] 2025-07-27 09:02:48,588.588 - INFO     - src.multi_contract_manager - 仓位大小调整: ETH-USDT-SWAP
[CryptoQuant] 2025-07-27 09:02:48,589.589 - INFO     - src.multi_contract_manager -   原始仓位: 0.05
[CryptoQuant] 2025-07-27 09:02:48,589.589 - INFO     - src.multi_contract_manager -   风险允许: 0.05
[CryptoQuant] 2025-07-27 09:02:48,590.590 - INFO     - src.multi_contract_manager -   最终仓位: 0.05
[CryptoQuant] 2025-07-27 09:02:48,591.591 - INFO     - src.multi_contract_manager - 执行交易决策: ETH-USDT-SWAP -> BUY
[CryptoQuant] 2025-07-27 09:02:49,248.248 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions?instId=ETH-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:49,249.249 - INFO     - src.deprecated.trade_executor - 设置杠杆: ETH-USDT-SWAP 1x -> 7.2x
[CryptoQuant] 2025-07-27 09:02:49,758.758 - INFO     - src.config_manager - 置信度参数计算: BTC-USDT-SWAP
[CryptoQuant] 2025-07-27 09:02:49,759.759 - INFO     - src.config_manager -   置信度: 0.75
[CryptoQuant] 2025-07-27 09:02:49,759.759 - INFO     - src.config_manager -   杠杆: 7.5x (上限: 10x)
[CryptoQuant] 2025-07-27 09:02:49,760.760 - INFO     - src.config_manager -   仓位: 64.95% (上限: 100.0%)
[CryptoQuant] 2025-07-27 09:02:49,760.760 - INFO     - src.config_manager -   止损: 3.25% (上限: 5.0%)
[CryptoQuant] 2025-07-27 09:02:49,761.761 - INFO     - src.config_manager -   止盈: 15.0% (上限: 20.0%)
[CryptoQuant] 2025-07-27 09:02:49,762.762 - INFO     - src.optimized_prompts.risk_guardian_engine - 风险守护者决策完成: BTC-USDT-SWAP -> HOLD (置信度: 0.75)
[CryptoQuant] 2025-07-27 09:02:49,762.762 - INFO     - src.multi_contract_manager - 交易逻辑评估: BTC-USDT-SWAP - AI决策: HOLD, 当前状态: LONG_POSITION
[CryptoQuant] 2025-07-27 09:02:49,762.762 - INFO     - src.multi_contract_manager - 交易逻辑结果: BTC-USDT-SWAP
[CryptoQuant] 2025-07-27 09:02:49,763.763 - INFO     - src.multi_contract_manager -   should_trade: True
[CryptoQuant] 2025-07-27 09:02:49,764.764 - INFO     - src.multi_contract_manager -   reason: AI决策为HOLD，当前有LONG_POSITION，更新止盈止损
[CryptoQuant] 2025-07-27 09:02:49,764.764 - DEBUG    - src.risk_manager - 🔄 调用兼容接口 check_pre_trade_risk: BTC-USDT-SWAP
[CryptoQuant] 2025-07-27 09:02:49,765.765 - INFO     - src.risk_manager - 🛡️ 开始风险检查: BTC-USDT-SWAP
[CryptoQuant] 2025-07-27 09:02:49,766.766 - DEBUG    - src.risk_manager - 📊 输入参数 - 决策: {'decision': 'HOLD', 'confidence': 0.75, 'reasoning': '当前持仓处于新生期（盈亏-0.24%），需密切监控趋势发展。虽然1H和15m周期显示高风险信号（趋势下跌/上涨动能不足），但30m周期显示安全状态且市场整体波动较低（24h+1.4%）。杠杆风险可控（7x），建议给予2-4小时观察窗口，重点关注成交量配合和关键位突破情况。', 'stop_loss': 3.25, 'take_profit': 15.0, 'confidence_params': {'should_trade': True, 'leverage': 7.5, 'position_ratio': 64.95, 'stop_loss': 3.25, 'take_profit': 15.0, 'confidence': 0.75, 'max_limits': {'max_leverage': 10, 'max_position_size': 100.0, 'max_stop_loss': 5.0, 'max_take_profit': 20.0}}}
[CryptoQuant] 2025-07-27 09:02:49,767.767 - DEBUG    - src.risk_manager - 💰 输入参数 - 余额: {'USDT': {'available': 131269.63293486237, 'frozen': 49.11579166666666, 'total': 131318.74872652904, 'equity': 131318.88942652903}}
[CryptoQuant] 2025-07-27 09:02:49,768.768 - DEBUG    - src.risk_manager - 📈 输入参数 - 持仓: {'BTC-USDT-SWAP': {'symbol': 'BTC-USDT-SWAP', 'position_side': 'long', 'size': 0.3, 'available_size': 0.3, 'avg_price': 117924.8, 'unrealized_pnl': -0.117, 'unrealized_pnl_ratio': -0.****************, 'margin': 49.**************, 'leverage': 7.2, 'mark_price': 117885.8, 'liquidation_price': 0, 'update_time': datetime.datetime(2025, 7, 27, 9, 2, 11, 758000)}}
[CryptoQuant] 2025-07-27 09:02:49,769.769 - DEBUG    - src.risk_manager - 💰 余额信息: 可用=131269.63293486237 USDT, 总计=131318.74872652904 USDT
[CryptoQuant] 2025-07-27 09:02:49,770.770 - DEBUG    - src.risk_manager - 🔧 风控参数: 最小余额=10.0 USDT, 安全边际=10.0%
[CryptoQuant] 2025-07-27 09:02:49,772.772 - DEBUG    - src.risk_manager - 📊 资金需求分析: 仓位大小=0.100, 所需资金=14445.06 USDT
[CryptoQuant] 2025-07-27 09:02:49,772.772 - DEBUG    - src.risk_manager - 📊 仓位分析: BTC-USDT-SWAP 当前比例=26.93%, 新增比例=10.00%, 新增后比例=36.93%, 最大允许=5.00%
[CryptoQuant] 2025-07-27 09:02:49,773.773 - INFO     - src.risk_manager - 🔧 BTC-USDT-SWAP 仓位调整: 原始=0.100 -> 调整后=0.000 (已达最大限制)
[CryptoQuant] 2025-07-27 09:02:49,774.774 - DEBUG    - src.risk_manager - 📈 持仓统计: 当前活跃=1, 最大允许=3
[CryptoQuant] 2025-07-27 09:02:49,774.774 - INFO     - src.risk_manager - ✅ 风险检查通过: BTC-USDT-SWAP, 允许仓位: 0
[CryptoQuant] 2025-07-27 09:02:49,775.775 - INFO     - src.multi_contract_manager - 仓位大小调整: BTC-USDT-SWAP
[CryptoQuant] 2025-07-27 09:02:49,775.775 - INFO     - src.multi_contract_manager -   原始仓位: 0.1
[CryptoQuant] 2025-07-27 09:02:49,776.776 - INFO     - src.multi_contract_manager -   风险允许: 0
[CryptoQuant] 2025-07-27 09:02:49,776.776 - INFO     - src.multi_contract_manager -   最终仓位: 0
[CryptoQuant] 2025-07-27 09:02:49,776.776 - WARNING  - src.multi_contract_manager - 仓位大小为0，跳过交易: BTC-USDT-SWAP
[CryptoQuant] 2025-07-27 09:02:50,057.057 - INFO     - httpx - HTTP Request: POST https://www.okx.com/api/v5/account/set-leverage "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:50,057.057 - INFO     - src.deprecated.trade_executor - 杠杆设置成功: ETH-USDT-SWAP = 7.2x
[CryptoQuant] 2025-07-27 09:02:50,236.236 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:50,237.237 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117935.5', 'imr': '49.***************', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117935.7', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117912', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82496.***********', 'mmr': '1.414944', 'nonSettleAvgPx': '', 'notionalUsd': '353.********', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '0.****************', 'uplRatio': '-0.****************', 'uplRatioLastPx': '0.***************', 'usdPx': '1.00029', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:02:50,237.237 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.13 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:02:50,381.381 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:51,022.022 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:51,709.709 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:51,750.750 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/market/ticker?instId=ETH-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:53,155.155 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:53,195.195 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:53,197.197 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117935.5', 'imr': '49.**************', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117926.8', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117898.1', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82506.***********', 'mmr': '1.4147772', 'nonSettleAvgPx': '', 'notionalUsd': '353.*********', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '0.006', 'uplRatio': '-0.****************', 'uplRatioLastPx': '0.****************', 'usdPx': '1.00029', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:02:53,198.198 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.12 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:02:53,608.608 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/instruments?instType=SWAP&instId=ETH-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:53,609.609 - INFO     - src.deprecated.trade_executor - 🎯 使用OKX合约规格精确计算:
[CryptoQuant] 2025-07-27 09:02:53,610.610 - INFO     - src.deprecated.trade_executor -   - 合约: ETH-USDT-SWAP
[CryptoQuant] 2025-07-27 09:02:53,610.610 - INFO     - src.deprecated.trade_executor -   - 合约面值(ctVal): 0.1
[CryptoQuant] 2025-07-27 09:02:53,612.612 - INFO     - src.deprecated.trade_executor -   - 合约乘数(ctMult): 1.0
[CryptoQuant] 2025-07-27 09:02:53,612.612 - INFO     - src.deprecated.trade_executor -   - 当前价格: 3861.0
[CryptoQuant] 2025-07-27 09:02:53,613.613 - INFO     - src.deprecated.trade_executor -   - 目标保证金: 50 USDT
[CryptoQuant] 2025-07-27 09:02:53,613.613 - INFO     - src.deprecated.trade_executor -   - 杠杆倍数: 7.2x
[CryptoQuant] 2025-07-27 09:02:53,613.613 - INFO     - src.deprecated.trade_executor -   - 名义价值: 360.0 USDT
[CryptoQuant] 2025-07-27 09:02:53,614.614 - INFO     - src.deprecated.trade_executor -   - 计算张数: 360.0 ÷ (0.1 × 1.0 × 3861.0) = 0.932401
[CryptoQuant] 2025-07-27 09:02:53,808.808 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:54,486.486 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:54,943.943 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/instruments?instType=SWAP&instId=ETH-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:54,944.944 - INFO     - src.deprecated.trade_executor - 精度调整详情: 原始数量=0.932401, 最小数量=0.01, 精度单位=0.01, 调整后=0.93
[CryptoQuant] 2025-07-27 09:02:54,945.945 - INFO     - src.deprecated.trade_executor - 🔍 最终下单参数: symbol=ETH-USDT-SWAP, size=0.93, 目标保证金=50 USDT
[CryptoQuant] 2025-07-27 09:02:55,685.685 - WARNING  - src.multi_contract_manager - 交易对处理失败: BTC-USDT-SWAP - 未知错误
[CryptoQuant] 2025-07-27 09:02:56,782.782 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/instruments?instType=SWAP&instId=ETH-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:56,783.783 - INFO     - src.deprecated.trade_executor - 精度调整详情: 原始数量=0.930000, 最小数量=0.01, 精度单位=0.01, 调整后=0.93
[CryptoQuant] 2025-07-27 09:02:56,784.784 - INFO     - src.deprecated.trade_executor - 🔄 下单尝试 1/3: ETH-USDT-SWAP, size=0.93
[CryptoQuant] 2025-07-27 09:02:58,789.789 - INFO     - httpx - HTTP Request: POST https://www.okx.com/api/v5/trade/order "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:02:58,789.789 - INFO     - src.deprecated.trade_executor - ✅ 下单成功: ETH-USDT-SWAP
[CryptoQuant] 2025-07-27 09:02:58,790.790 - INFO     - src.deprecated.trade_executor - 🔍 下单响应: {'code': '0', 'data': [{'clOrdId': '', 'ordId': '2720451721550434304', 'sCode': '0', 'sMsg': 'Order placed', 'tag': '', 'ts': '1753578179245'}], 'inTime': '1753578179244645', 'msg': '', 'outTime': '1753578179246179'}
[CryptoQuant] 2025-07-27 09:02:58,790.790 - INFO     - src.deprecated.trade_executor - 多头开仓成功，订单ID: 2720451721550434304
[CryptoQuant] 2025-07-27 09:03:00,264.264 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/trade/order?instId=ETH-USDT-SWAP&ordId=2720451721550434304 "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:03:00,265.265 - INFO     - src.deprecated.trade_executor - 🔍 订单执行详情:
[CryptoQuant] 2025-07-27 09:03:00,265.265 - INFO     - src.deprecated.trade_executor -   - 请求数量: 0.93
[CryptoQuant] 2025-07-27 09:03:00,266.266 - INFO     - src.deprecated.trade_executor -   - 实际成交数量: 0.93
[CryptoQuant] 2025-07-27 09:03:00,266.266 - INFO     - src.deprecated.trade_executor -   - 成交均价: 3871.94
[CryptoQuant] 2025-07-27 09:03:00,267.267 - INFO     - src.deprecated.trade_executor -   - 实际成交价值: 36.01 USDT
[CryptoQuant] 2025-07-27 09:03:00,267.267 - INFO     - src.deprecated.trade_executor -   - 杠杆倍数: 7.2x
[CryptoQuant] 2025-07-27 09:03:00,268.268 - INFO     - src.deprecated.trade_executor -   - 实际保证金: 5.00 USDT
[CryptoQuant] 2025-07-27 09:03:00,269.269 - INFO     - src.deprecated.trade_executor - 止盈止损参数检查: stop_loss=3.4, take_profit=14.4
[CryptoQuant] 2025-07-27 09:03:01,910.910 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/market/ticker?instId=ETH-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:03:02,274.274 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/market/ticker?instId=ETH-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:03:02,275.275 - INFO     - src.deprecated.trade_executor - 🔍 止盈验证 - 目标收益率: 14.4%
[CryptoQuant] 2025-07-27 09:03:02,276.276 - INFO     - src.deprecated.trade_executor -   - 杠杆倍数: 7.2x
[CryptoQuant] 2025-07-27 09:03:02,276.276 - INFO     - src.deprecated.trade_executor -   - 要求价格变动: 2.000000%
[CryptoQuant] 2025-07-27 09:03:03,659.659 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/instruments?instType=SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:03:04,931.931 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/instruments?instType=SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:03:04,934.934 - INFO     - src.deprecated.trade_executor - 杠杆止盈止损价格计算: ETH-USDT-SWAP
[CryptoQuant] 2025-07-27 09:03:04,935.935 - INFO     - src.deprecated.trade_executor -   - 当前价格: 3870.0
[CryptoQuant] 2025-07-27 09:03:04,936.936 - INFO     - src.deprecated.trade_executor -   - 杠杆倍数: 7.2x
[CryptoQuant] 2025-07-27 09:03:04,937.937 - INFO     - src.deprecated.trade_executor -   - 目标保证金止损: 3.4% -> 价格变化: 0.4722% -> 止损价格: 3851.73
[CryptoQuant] 2025-07-27 09:03:04,938.938 - INFO     - src.deprecated.trade_executor -   - 目标保证金止盈: 14.4% -> 价格变化: 2.0000% -> 止盈价格: 3947.4
[CryptoQuant] 2025-07-27 09:03:04,938.938 - INFO     - src.deprecated.trade_executor -   - 持仓数量: 0.93
[CryptoQuant] 2025-07-27 09:03:05,300.300 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/market/ticker?instId=ETH-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:03:07,786.786 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/instruments?instType=SWAP&instId=ETH-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:03:08,361.361 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/instruments?instType=SWAP&instId=ETH-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:03:08,362.362 - INFO     - src.deprecated.trade_executor - OCO订单参数: {'instId': 'ETH-USDT-SWAP', 'tdMode': 'cross', 'side': 'sell', 'ordType': 'oco', 'sz': '0.93', 'posSide': 'long', 'tpTriggerPx': '3947.4', 'tpOrdPx': '-1', 'slTriggerPx': '3851.73', 'slOrdPx': '-1'}
[CryptoQuant] 2025-07-27 09:03:10,172.172 - INFO     - httpx - HTTP Request: POST https://www.okx.com/api/v5/trade/order-algo "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:03:10,173.173 - INFO     - src.deprecated.trade_executor - ✅ OCO订单设置成功: ETH-USDT-SWAP
[CryptoQuant] 2025-07-27 09:03:10,174.174 - INFO     - src.deprecated.trade_executor -   - 止损价格: 3851.73 (原始: 3851.73)
[CryptoQuant] 2025-07-27 09:03:10,175.175 - INFO     - src.deprecated.trade_executor -   - 止盈价格: 3947.4 (原始: 3947.4)
[CryptoQuant] 2025-07-27 09:03:10,175.175 - INFO     - src.deprecated.trade_executor -   - 当前市价: 3870.15
[CryptoQuant] 2025-07-27 09:03:10,175.175 - INFO     - src.deprecated.trade_executor -   - 订单ID: 2720452103574474752
[CryptoQuant] 2025-07-27 09:03:10,176.176 - INFO     - src.deprecated.trade_executor - BUY决策执行成功: ETH-USDT-SWAP 数量=0.93
[CryptoQuant] 2025-07-27 09:03:15,686.686 - INFO     - src.multi_contract_manager - 交易对处理成功: ETH-USDT-SWAP
[CryptoQuant] 2025-07-27 09:03:18,273.273 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:03:18,464.464 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:03:18,465.465 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 ETH-USDT-SWAP: {'adl': '1', 'availPos': '0.93', 'avgPx': '3871.94', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '3875.************', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.********', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '3754.5541', 'imr': '49.**************', 'instId': 'ETH-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '3871', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '3868.6', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '40905.***********', 'mmr': '1.****************', 'nonSettleAvgPx': '', 'notionalUsd': '359.*********', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.93', 'posCcy': '', 'posId': '2720124824610213888', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.********', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.3106200000000136', 'uplLastPx': '-0.****************', 'uplRatio': '-0.0062108400440095', 'uplRatioLastPx': '-0.****************', 'usdPx': '1.00029', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:03:18,466.466 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: ETH-USDT-SWAP = 49.97 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:03:18,466.466 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117935.5', 'imr': '49.11391666666667', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117926.8', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117873.4', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '40905.***********', 'mmr': '1.4144808', 'nonSettleAvgPx': '', 'notionalUsd': '353.722749858', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '0.006', 'uplRatio': '-0.****************', 'uplRatioLastPx': '0.****************', 'usdPx': '1.00029', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:03:18,467.467 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.11 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:03:18,825.825 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:03:19,385.385 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:03:28,909.909 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions?instId=DOGE-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:03:28,909.909 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions?instId=ETH-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:03:28,911.911 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 ETH-USDT-SWAP: {'adl': '1', 'availPos': '0.93', 'avgPx': '3871.94', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '3875.************', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.********', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '3754.3294', 'imr': '49.**************', 'instId': 'ETH-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '3871', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '3868.68', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '40906.***********', 'mmr': '1.********', 'nonSettleAvgPx': '', 'notionalUsd': '359.**************', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.93', 'posCcy': '', 'posId': '2720124824610213888', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.********', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.****************', 'uplRatio': '-0.****************', 'uplRatioLastPx': '-0.****************', 'usdPx': '1.00029', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:03:28,912.912 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions?instId=BTC-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:03:28,913.913 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: ETH-USDT-SWAP = 49.97 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:03:28,913.913 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117935.5', 'imr': '49.111125', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117926.8', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117866.7', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '40906.***********', 'mmr': '1.4144004', 'nonSettleAvgPx': '', 'notionalUsd': '353.70264402899994', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '0.006', 'uplRatio': '-0.****************', 'uplRatioLastPx': '0.****************', 'usdPx': '1.00029', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:03:28,915.915 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.11 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:03:29,616.616 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions?instId=DOGE-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:03:29,617.617 - INFO     - src.multi_contract_manager - 持仓状态分析: DOGE-USDT-SWAP - NO_POSITION - 大小: 0
[CryptoQuant] 2025-07-27 09:03:29,793.793 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions?instId=ETH-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:03:29,794.794 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 ETH-USDT-SWAP: {'adl': '1', 'availPos': '0.93', 'avgPx': '3871.94', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '3875.************', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.********', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '3753.7923', 'imr': '49.**************', 'instId': 'ETH-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '3871', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '3868.28', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '40909.***********', 'mmr': '1.****************', 'nonSettleAvgPx': '', 'notionalUsd': '359.**************', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.93', 'posCcy': '', 'posId': '2720124824610213888', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.********', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.3403799999999865', 'uplLastPx': '-0.****************', 'uplRatio': '-0.0068************', 'uplRatioLastPx': '-0.****************', 'usdPx': '1.00029', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:03:29,795.795 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: ETH-USDT-SWAP = 49.97 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:03:29,796.796 - INFO     - src.multi_contract_manager - 持仓状态分析: ETH-USDT-SWAP - LONG_POSITION - 大小: 0.93
[CryptoQuant] 2025-07-27 09:03:29,797.797 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions?instId=BTC-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:03:29,799.799 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117929.2', 'imr': '49.**************', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117926.8', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117861.1', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '40909.***********', 'mmr': '1.****************', 'nonSettleAvgPx': '', 'notionalUsd': '353.*********', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.1910999999999913', 'uplLastPx': '0.006', 'uplRatio': '-0.0038892582391488', 'uplRatioLastPx': '0.****************', 'usdPx': '1.00029', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:03:29,800.800 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.11 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:03:29,800.800 - INFO     - src.multi_contract_manager - 持仓状态分析: BTC-USDT-SWAP - LONG_POSITION - 大小: 0.3
[CryptoQuant] 2025-07-27 09:03:31,972.972 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/market/candles?instId=DOGE-USDT-SWAP&bar=30m&limit=100 "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:03:33,457.457 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/market/candles?instId=ETH-USDT-SWAP&bar=30m&limit=100 "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:03:33,496.496 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/market/candles?instId=DOGE-USDT-SWAP&bar=1m&limit=100 "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:03:33,497.497 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/market/candles?instId=BTC-USDT-SWAP&bar=30m&limit=100 "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:03:34,680.680 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/market/ticker?instId=DOGE-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:03:34,681.681 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/market/candles?instId=ETH-USDT-SWAP&bar=5m&limit=100 "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:03:34,682.682 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/market/candles?instId=BTC-USDT-SWAP&bar=5m&limit=100 "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:03:34,706.706 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:03:34,744.744 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:03:34,765.765 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:03:34,787.787 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:03:34,807.807 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:03:35,302.302 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/market/candles?instId=ETH-USDT-SWAP&bar=1m&limit=100 "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:03:35,303.303 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/market/candles?instId=BTC-USDT-SWAP&bar=1m&limit=100 "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:03:35,963.963 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/market/ticker?instId=ETH-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:03:35,963.963 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/market/ticker?instId=BTC-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:03:35,993.993 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:03:36,004.004 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:03:36,026.026 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:03:36,048.048 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:03:36,070.070 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:03:36,093.093 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:03:36,118.118 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:03:36,179.179 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:03:36,179.179 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:03:36,204.204 - INFO     - src.indicator_calculator - 所有指标计算完成，共63列
[CryptoQuant] 2025-07-27 09:03:37,236.236 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:03:37,237.237 - INFO     - src.optimized_prompts.smart_hunter_engine - 🎯 智能猎手开始分析: DOGE-USDT-SWAP
[CryptoQuant] 2025-07-27 09:03:37,238.238 - INFO     - src.optimized_prompts.smart_hunter_engine -   - 当前价格: 0.2358
[CryptoQuant] 2025-07-27 09:03:37,239.239 - INFO     - src.optimized_prompts.smart_hunter_engine -   - 24h涨跌: 0.30%
[CryptoQuant] 2025-07-27 09:03:37,239.239 - INFO     - src.optimized_prompts.smart_hunter_engine - 🎯 智能猎手开始AI分析: DOGE-USDT-SWAP
[CryptoQuant] 2025-07-27 09:03:37,240.240 - INFO     - src.optimized_prompts.smart_hunter_engine - 智能猎手 - AI调用尝试 1/4
[CryptoQuant] 2025-07-27 09:03:37,831.831 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:03:37,831.831 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:03:37,832.832 - INFO     - src.optimized_prompts.risk_guardian_engine - 🛡️ 风险守护者分析持仓: ETH-USDT-SWAP
[CryptoQuant] 2025-07-27 09:03:37,833.833 - INFO     - src.optimized_prompts.risk_guardian_engine - 🛡️ 风险守护者分析持仓: BTC-USDT-SWAP
[CryptoQuant] 2025-07-27 09:03:37,833.833 - INFO     - src.optimized_prompts.risk_guardian_engine -   - 当前价格: 3871.0
[CryptoQuant] 2025-07-27 09:03:37,834.834 - INFO     - src.optimized_prompts.risk_guardian_engine -   - 当前价格: 117926.8
[CryptoQuant] 2025-07-27 09:03:37,834.834 - INFO     - src.optimized_prompts.risk_guardian_engine -   - 开仓均价: 3871.94
[CryptoQuant] 2025-07-27 09:03:37,834.834 - INFO     - src.optimized_prompts.risk_guardian_engine -   - 开仓均价: 117924.8
[CryptoQuant] 2025-07-27 09:03:37,835.835 - INFO     - src.optimized_prompts.risk_guardian_engine -   - 持仓方向: long
[CryptoQuant] 2025-07-27 09:03:37,835.835 - INFO     - src.optimized_prompts.risk_guardian_engine -   - 持仓方向: long
[CryptoQuant] 2025-07-27 09:03:37,835.835 - INFO     - src.optimized_prompts.risk_guardian_engine -   - 杠杆倍数: 7.0x
[CryptoQuant] 2025-07-27 09:03:37,836.836 - INFO     - src.optimized_prompts.risk_guardian_engine -   - 杠杆倍数: 7.0x
[CryptoQuant] 2025-07-27 09:03:37,836.836 - INFO     - src.optimized_prompts.risk_guardian_engine -   - 实际盈亏: -0.6062% (-0.30 USDT)
[CryptoQuant] 2025-07-27 09:03:37,837.837 - INFO     - src.optimized_prompts.risk_guardian_engine -   - 实际盈亏: -0.3547% (-0.17 USDT)
[CryptoQuant] 2025-07-27 09:03:37,838.838 - INFO     - src.optimized_prompts.risk_guardian_engine - 🛡️ 风险守护者开始分析: ETH-USDT-SWAP
[CryptoQuant] 2025-07-27 09:03:37,838.838 - INFO     - src.optimized_prompts.risk_guardian_engine - 🛡️ 风险守护者开始分析: BTC-USDT-SWAP
[CryptoQuant] 2025-07-27 09:03:37,839.839 - INFO     - src.optimized_prompts.risk_guardian_engine - 风险守护者 - AI调用尝试 1/4
[CryptoQuant] 2025-07-27 09:03:37,839.839 - INFO     - src.optimized_prompts.risk_guardian_engine - 风险守护者 - AI调用尝试 1/4
[CryptoQuant] 2025-07-27 09:03:47,431.431 - INFO     - src.config_manager - 置信度参数计算: BTC-USDT-SWAP
[CryptoQuant] 2025-07-27 09:03:47,432.432 - INFO     - src.config_manager -   置信度: 0.70
[CryptoQuant] 2025-07-27 09:03:47,432.432 - INFO     - src.config_manager -   杠杆: 7.0x (上限: 10x)
[CryptoQuant] 2025-07-27 09:03:47,433.433 - INFO     - src.config_manager -   仓位: 58.57% (上限: 100.0%)
[CryptoQuant] 2025-07-27 09:03:47,433.433 - INFO     - src.config_manager -   止损: 3.5% (上限: 5.0%)
[CryptoQuant] 2025-07-27 09:03:47,434.434 - INFO     - src.config_manager -   止盈: 14.0% (上限: 20.0%)
[CryptoQuant] 2025-07-27 09:03:47,435.435 - INFO     - src.optimized_prompts.risk_guardian_engine - 风险守护者决策完成: BTC-USDT-SWAP -> HOLD (置信度: 0.70)
[CryptoQuant] 2025-07-27 09:03:47,435.435 - INFO     - src.multi_contract_manager - 交易逻辑评估: BTC-USDT-SWAP - AI决策: HOLD, 当前状态: LONG_POSITION
[CryptoQuant] 2025-07-27 09:03:47,436.436 - INFO     - src.multi_contract_manager - 交易逻辑结果: BTC-USDT-SWAP
[CryptoQuant] 2025-07-27 09:03:47,436.436 - INFO     - src.multi_contract_manager -   should_trade: True
[CryptoQuant] 2025-07-27 09:03:47,437.437 - INFO     - src.multi_contract_manager -   reason: AI决策为HOLD，当前有LONG_POSITION，更新止盈止损
[CryptoQuant] 2025-07-27 09:03:47,437.437 - DEBUG    - src.risk_manager - 🔄 调用兼容接口 check_pre_trade_risk: BTC-USDT-SWAP
[CryptoQuant] 2025-07-27 09:03:47,438.438 - INFO     - src.risk_manager - 🛡️ 开始风险检查: BTC-USDT-SWAP
[CryptoQuant] 2025-07-27 09:03:47,439.439 - DEBUG    - src.risk_manager - 📊 输入参数 - 决策: {'decision': 'HOLD', 'confidence': 0.7, 'reasoning': '当前持仓处于新生期，盈亏在±1%以内，虽然短期技术指标显示高风险，但市场整体波动较低，且杠杆风险可控。建议继续持有，给予趋势发展空间，同时严格执行止损策略。', 'stop_loss': 3.5, 'take_profit': 14.0, 'confidence_params': {'should_trade': True, 'leverage': 7.0, 'position_ratio': 58.57, 'stop_loss': 3.5, 'take_profit': 14.0, 'confidence': 0.7, 'max_limits': {'max_leverage': 10, 'max_position_size': 100.0, 'max_stop_loss': 5.0, 'max_take_profit': 20.0}}}
[CryptoQuant] 2025-07-27 09:03:47,440.440 - DEBUG    - src.risk_manager - 💰 输入参数 - 余额: {'USDT': {'available': 131219.24262881902, 'frozen': 99.0964625, 'total': 131318.33909131904, 'equity': 131318.70938131903}}
[CryptoQuant] 2025-07-27 09:03:47,440.440 - DEBUG    - src.risk_manager - 📈 输入参数 - 持仓: {'BTC-USDT-SWAP': {'symbol': 'BTC-USDT-SWAP', 'position_side': 'long', 'size': 0.3, 'available_size': 0.3, 'avg_price': 117924.8, 'unrealized_pnl': -0.****************, 'unrealized_pnl_ratio': -0.****************, 'margin': 49.111125, 'leverage': 7.2, 'mark_price': 117866.7, 'liquidation_price': 0, 'update_time': datetime.datetime(2025, 7, 27, 9, 2, 11, 758000)}}
[CryptoQuant] 2025-07-27 09:03:47,441.441 - DEBUG    - src.risk_manager - 💰 余额信息: 可用=131219.24262881902 USDT, 总计=131318.33909131904 USDT
[CryptoQuant] 2025-07-27 09:03:47,442.442 - DEBUG    - src.risk_manager - 🔧 风控参数: 最小余额=10.0 USDT, 安全边际=10.0%
[CryptoQuant] 2025-07-27 09:03:47,442.442 - DEBUG    - src.risk_manager - 📊 资金需求分析: 仓位大小=0.100, 所需资金=14445.02 USDT
[CryptoQuant] 2025-07-27 09:03:47,443.443 - DEBUG    - src.risk_manager - 📊 仓位分析: BTC-USDT-SWAP 当前比例=26.93%, 新增比例=10.00%, 新增后比例=36.93%, 最大允许=5.00%
[CryptoQuant] 2025-07-27 09:03:47,443.443 - INFO     - src.risk_manager - 🔧 BTC-USDT-SWAP 仓位调整: 原始=0.100 -> 调整后=0.000 (已达最大限制)
[CryptoQuant] 2025-07-27 09:03:47,444.444 - DEBUG    - src.risk_manager - 📈 持仓统计: 当前活跃=1, 最大允许=3
[CryptoQuant] 2025-07-27 09:03:47,445.445 - INFO     - src.risk_manager - ✅ 风险检查通过: BTC-USDT-SWAP, 允许仓位: 0
[CryptoQuant] 2025-07-27 09:03:47,446.446 - INFO     - src.multi_contract_manager - 仓位大小调整: BTC-USDT-SWAP
[CryptoQuant] 2025-07-27 09:03:47,447.447 - INFO     - src.multi_contract_manager -   原始仓位: 0.1
[CryptoQuant] 2025-07-27 09:03:47,447.447 - INFO     - src.multi_contract_manager -   风险允许: 0
[CryptoQuant] 2025-07-27 09:03:47,447.447 - INFO     - src.multi_contract_manager -   最终仓位: 0
[CryptoQuant] 2025-07-27 09:03:47,448.448 - WARNING  - src.multi_contract_manager - 仓位大小为0，跳过交易: BTC-USDT-SWAP
[CryptoQuant] 2025-07-27 09:03:48,998.998 - INFO     - src.config_manager - 置信度参数计算: ETH-USDT-SWAP
[CryptoQuant] 2025-07-27 09:03:48,999.999 - INFO     - src.config_manager -   置信度: 0.65
[CryptoQuant] 2025-07-27 09:03:48,999.999 - INFO     - src.config_manager -   杠杆: 6.5x (上限: 10x)
[CryptoQuant] 2025-07-27 09:03:49,000.000 - INFO     - src.config_manager -   仓位: 52.4% (上限: 100.0%)
[CryptoQuant] 2025-07-27 09:03:49,000.000 - INFO     - src.config_manager -   止损: 3.75% (上限: 5.0%)
[CryptoQuant] 2025-07-27 09:03:49,001.001 - INFO     - src.config_manager -   止盈: 13.0% (上限: 20.0%)
[CryptoQuant] 2025-07-27 09:03:49,002.002 - INFO     - src.optimized_prompts.risk_guardian_engine - 风险守护者决策完成: ETH-USDT-SWAP -> HOLD (置信度: 0.65)
[CryptoQuant] 2025-07-27 09:03:49,002.002 - INFO     - src.multi_contract_manager - 交易逻辑评估: ETH-USDT-SWAP - AI决策: HOLD, 当前状态: LONG_POSITION
[CryptoQuant] 2025-07-27 09:03:49,003.003 - INFO     - src.multi_contract_manager - 交易逻辑结果: ETH-USDT-SWAP
[CryptoQuant] 2025-07-27 09:03:49,003.003 - INFO     - src.multi_contract_manager -   should_trade: True
[CryptoQuant] 2025-07-27 09:03:49,004.004 - INFO     - src.multi_contract_manager -   reason: AI决策为HOLD，当前有LONG_POSITION，更新止盈止损
[CryptoQuant] 2025-07-27 09:03:49,004.004 - DEBUG    - src.risk_manager - 🔄 调用兼容接口 check_pre_trade_risk: ETH-USDT-SWAP
[CryptoQuant] 2025-07-27 09:03:49,005.005 - INFO     - src.risk_manager - 🛡️ 开始风险检查: ETH-USDT-SWAP
[CryptoQuant] 2025-07-27 09:03:49,006.006 - DEBUG    - src.risk_manager - 📊 输入参数 - 决策: {'decision': 'HOLD', 'confidence': 0.65, 'reasoning': '当前持仓处于新生期（亏损-0.61%），虽然多周期显示高风险评级（MACD收敛+动能不足），但市场整体波动率低（24h+0.1%），且杠杆风险可控（7x）。建议给予2-4小时观察窗口，重点关注3870-3900区间的突破情况。', 'stop_loss': 3.75, 'take_profit': 13.0, 'confidence_params': {'should_trade': True, 'leverage': 6.5, 'position_ratio': 52.4, 'stop_loss': 3.75, 'take_profit': 13.0, 'confidence': 0.65, 'max_limits': {'max_leverage': 10, 'max_position_size': 100.0, 'max_stop_loss': 5.0, 'max_take_profit': 20.0}}}
[CryptoQuant] 2025-07-27 09:03:49,006.006 - DEBUG    - src.risk_manager - 💰 输入参数 - 余额: {'USDT': {'available': 131219.24262881902, 'frozen': 99.0964625, 'total': 131318.33909131904, 'equity': 131318.70938131903}}
[CryptoQuant] 2025-07-27 09:03:49,007.007 - DEBUG    - src.risk_manager - 📈 输入参数 - 持仓: {'ETH-USDT-SWAP': {'symbol': 'ETH-USDT-SWAP', 'position_side': 'long', 'size': 0.93, 'available_size': 0.93, 'avg_price': 3871.94, 'unrealized_pnl': -0.****************, 'unrealized_pnl_ratio': -0.****************, 'margin': 49.**************, 'leverage': 7.2, 'mark_price': 3868.68, 'liquidation_price': 0, 'update_time': datetime.datetime(2025, 7, 27, 9, 2, 59, 247000)}}
[CryptoQuant] 2025-07-27 09:03:49,007.007 - DEBUG    - src.risk_manager - 💰 余额信息: 可用=131219.24262881902 USDT, 总计=131318.33909131904 USDT
[CryptoQuant] 2025-07-27 09:03:49,008.008 - DEBUG    - src.risk_manager - 🔧 风控参数: 最小余额=10.0 USDT, 安全边际=10.0%
[CryptoQuant] 2025-07-27 09:03:49,008.008 - DEBUG    - src.risk_manager - 📊 资金需求分析: 仓位大小=0.100, 所需资金=14445.02 USDT
[CryptoQuant] 2025-07-27 09:03:49,009.009 - DEBUG    - src.risk_manager - 📊 仓位分析: ETH-USDT-SWAP 当前比例=2.74%, 新增比例=10.00%, 新增后比例=12.74%, 最大允许=5.00%
[CryptoQuant] 2025-07-27 09:03:49,010.010 - INFO     - src.risk_manager - 🔧 ETH-USDT-SWAP 仓位调整: 原始=0.100 -> 调整后=0.023
[CryptoQuant] 2025-07-27 09:03:49,010.010 - DEBUG    - src.risk_manager - 📈 持仓统计: 当前活跃=1, 最大允许=3
[CryptoQuant] 2025-07-27 09:03:49,012.012 - INFO     - src.risk_manager - ✅ 风险检查通过: ETH-USDT-SWAP, 允许仓位: 0.022601904464402096
[CryptoQuant] 2025-07-27 09:03:49,012.012 - INFO     - src.multi_contract_manager - 仓位大小调整: ETH-USDT-SWAP
[CryptoQuant] 2025-07-27 09:03:49,013.013 - INFO     - src.multi_contract_manager -   原始仓位: 0.1
[CryptoQuant] 2025-07-27 09:03:49,014.014 - INFO     - src.multi_contract_manager -   风险允许: 0.022601904464402096
[CryptoQuant] 2025-07-27 09:03:49,014.014 - INFO     - src.multi_contract_manager -   最终仓位: 0.022601904464402096
[CryptoQuant] 2025-07-27 09:03:49,015.015 - INFO     - src.multi_contract_manager - 执行交易决策: ETH-USDT-SWAP -> HOLD
[CryptoQuant] 2025-07-27 09:03:49,015.015 - INFO     - src.deprecated.trade_executor - HOLD决策止盈止损参数: stop_loss=3.75, take_profit=13.0
[CryptoQuant] 2025-07-27 09:03:49,015.015 - INFO     - src.deprecated.trade_executor - 更新持仓止盈止损: ETH-USDT-SWAP 方向=BUY 数量=0.93
[CryptoQuant] 2025-07-27 09:03:49,016.016 - INFO     - src.deprecated.trade_executor -   - AI建议止损: 3.75%
[CryptoQuant] 2025-07-27 09:03:49,016.016 - INFO     - src.deprecated.trade_executor -   - AI建议止盈: 13.0%
[CryptoQuant] 2025-07-27 09:03:50,014.014 - INFO     - src.config_manager - 置信度参数计算: DOGE-USDT-SWAP
[CryptoQuant] 2025-07-27 09:03:50,015.015 - INFO     - src.config_manager -   置信度: 0.45
[CryptoQuant] 2025-07-27 09:03:50,015.015 - INFO     - src.config_manager -   杠杆: 4.5x (上限: 10x)
[CryptoQuant] 2025-07-27 09:03:50,016.016 - INFO     - src.config_manager -   仓位: 30.19% (上限: 100.0%)
[CryptoQuant] 2025-07-27 09:03:50,016.016 - INFO     - src.config_manager -   止损: 4.75% (上限: 5.0%)
[CryptoQuant] 2025-07-27 09:03:50,016.016 - INFO     - src.config_manager -   止盈: 9.0% (上限: 20.0%)
[CryptoQuant] 2025-07-27 09:03:50,017.017 - INFO     - src.optimized_prompts.smart_hunter_engine - 智能猎手决策完成: DOGE-USDT-SWAP -> HOLD (置信度: 0.45)
[CryptoQuant] 2025-07-27 09:03:50,018.018 - INFO     - src.multi_contract_manager - 交易逻辑评估: DOGE-USDT-SWAP - AI决策: HOLD, 当前状态: NO_POSITION
[CryptoQuant] 2025-07-27 09:03:50,018.018 - INFO     - src.multi_contract_manager - 交易逻辑结果: DOGE-USDT-SWAP
[CryptoQuant] 2025-07-27 09:03:50,019.019 - INFO     - src.multi_contract_manager -   should_trade: False
[CryptoQuant] 2025-07-27 09:03:50,019.019 - INFO     - src.multi_contract_manager -   reason: AI决策为HOLD，当前无持仓，保持当前状态
[CryptoQuant] 2025-07-27 09:03:50,802.802 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/market/ticker?instId=ETH-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:03:52,893.893 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions?instId=ETH-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:03:52,893.893 - INFO     - src.deprecated.trade_executor - 获取实际杠杆倍数: ETH-USDT-SWAP = 1x
[CryptoQuant] 2025-07-27 09:03:54,790.790 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/instruments?instType=SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:03:55,691.691 - WARNING  - src.multi_contract_manager - 交易对处理失败: BTC-USDT-SWAP - 未知错误
[CryptoQuant] 2025-07-27 09:03:55,692.692 - INFO     - src.multi_contract_manager - 交易对处理成功: DOGE-USDT-SWAP
[CryptoQuant] 2025-07-27 09:03:56,344.344 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/instruments?instType=SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:03:56,348.348 - INFO     - src.deprecated.trade_executor - 持仓杠杆止盈止损价格计算: ETH-USDT-SWAP
[CryptoQuant] 2025-07-27 09:03:56,349.349 - INFO     - src.deprecated.trade_executor -   - 当前价格: 3836.0
[CryptoQuant] 2025-07-27 09:03:56,349.349 - INFO     - src.deprecated.trade_executor -   - 杠杆倍数: 1x
[CryptoQuant] 2025-07-27 09:03:56,350.350 - INFO     - src.deprecated.trade_executor -   - 目标保证金止损: 3.75% -> 价格变化: 3.7500% -> 止损价格: 3726.74
[CryptoQuant] 2025-07-27 09:03:56,351.351 - INFO     - src.deprecated.trade_executor -   - 目标保证金止盈: 13.0% -> 价格变化: 13.0000% -> 止盈价格: 4375.29
[CryptoQuant] 2025-07-27 09:03:56,351.351 - INFO     - src.deprecated.trade_executor - 🎯 智能取消止盈止损订单: ETH-USDT-SWAP
[CryptoQuant] 2025-07-27 09:03:56,352.352 - INFO     - src.deprecated.trade_executor - 取消现有止盈止损订单: ETH-USDT-SWAP
[CryptoQuant] 2025-07-27 09:03:58,437.437 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/trade/orders-algo-pending?ordType=oco&instType=SWAP&instId=ETH-USDT-SWAP "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:04:00,605.605 - INFO     - src.logger_monitor - 日志监控器初始化完成
[CryptoQuant] 2025-07-27 09:04:00,607.607 - INFO     - src.logger_monitor - 日志监控启动成功
[CryptoQuant] 2025-07-27 09:04:00,608.608 - INFO     - src.main_controller - 主控制器开始初始化
[CryptoQuant] 2025-07-27 09:04:00,609.609 - INFO     - src.deprecated.data_fetcher - 🔄 尝试连接OKX域名: https://www.okx.com
[CryptoQuant] 2025-07-27 09:04:02,106.106 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/time "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:04:02,107.107 - INFO     - src.deprecated.data_fetcher - 🟢 成功连接OKX域名: https://www.okx.com
[CryptoQuant] 2025-07-27 09:04:02,107.107 - INFO     - src.deprecated.data_fetcher - 🔧 API超时和连接池配置完成
[CryptoQuant] 2025-07-27 09:04:02,465.465 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/time "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:04:02,466.466 - INFO     - src.deprecated.data_fetcher - 🟢 OKX连接健康检查通过
[CryptoQuant] 2025-07-27 09:04:02,466.466 - INFO     - src.deprecated.data_fetcher - 🟢 OKX客户端初始化成功 - 使用域名: https://www.okx.com
[CryptoQuant] 2025-07-27 09:04:02,467.467 - INFO     - src.indicator_calculator - 指标参数加载完成
[CryptoQuant] 2025-07-27 09:04:02,468.468 - INFO     - src.risk_manager - 🔧 最终风险参数: {'max_leverage': 5.0, 'stop_loss_pct': 0.02, 'take_profit_pct': 0.04, 'max_position_ratio': 0.05, 'max_daily_loss_usdt': 0.05, 'min_balance_usdt': 10.0, 'safety_margin_ratio': 0.1, 'max_concurrent_positions': 3, 'emergency_stop_loss_pct': 0.15}
[CryptoQuant] 2025-07-27 09:04:02,469.469 - INFO     - src.risk_manager - 🛡️ 现代化风控系统初始化完成
[CryptoQuant] 2025-07-27 09:04:02,469.469 - INFO     - src.risk_manager - 📋 风控参数: {'max_leverage': 5.0, 'stop_loss_pct': 0.02, 'take_profit_pct': 0.04, 'max_position_ratio': 0.05, 'max_daily_loss_usdt': 0.05, 'min_balance_usdt': 10.0, 'safety_margin_ratio': 0.1, 'max_concurrent_positions': 3, 'emergency_stop_loss_pct': 0.15}
[CryptoQuant] 2025-07-27 09:04:02,480.480 - INFO     - src.web_dashboard - Web仪表盘初始化完成
[CryptoQuant] 2025-07-27 09:04:02,481.481 - INFO     - src.main_controller - 定时任务设置完成
[CryptoQuant] 2025-07-27 09:04:02,482.482 - INFO     - src.main_controller - 所有模块初始化完成
[CryptoQuant] 2025-07-27 09:04:02,483.483 - INFO     - src.web_dashboard - 启动Web服务器: http://0.0.0.0:5000
[CryptoQuant] 2025-07-27 09:04:02,484.484 - INFO     - src.main_controller - 开始初始化API相关模块...
[CryptoQuant] 2025-07-27 09:04:02,485.485 - INFO     - src.deprecated.trade_executor - 🔗 初始化交易API - 使用域名: https://www.okx.com
[CryptoQuant] 2025-07-27 09:04:02,515.515 - INFO     - src.deprecated.trade_executor - 🔧 交易API增强超时配置完成: 连接15s, 读取45s, 连接池30个
[CryptoQuant] 2025-07-27 09:04:04,696.696 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/time "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:04:04,697.697 - INFO     - src.deprecated.trade_executor - ✅ 交易API初始化完成，连接健康检查通过
[CryptoQuant] 2025-07-27 09:04:04,718.718 - INFO     - src.deprecated.trade_executor - 交易执行器初始化完成
[CryptoQuant] 2025-07-27 09:04:04,719.719 - INFO     - src.main_controller - 交易执行器初始化完成
[CryptoQuant] 2025-07-27 09:04:04,801.801 - INFO     - src.optimized_prompts.smart_hunter_engine - 智能猎手引擎 - API密钥加载成功
[CryptoQuant] 2025-07-27 09:04:04,802.802 - INFO     - src.optimized_prompts.smart_hunter_engine - 智能猎手引擎初始化完成
[CryptoQuant] 2025-07-27 09:04:04,802.802 - INFO     - src.optimized_prompts.risk_guardian_engine - 风险守护者引擎 - API密钥加载成功
[CryptoQuant] 2025-07-27 09:04:04,803.803 - INFO     - src.optimized_prompts.risk_guardian_engine - 风险守护者引擎初始化完成
[CryptoQuant] 2025-07-27 09:04:04,803.803 - INFO     - src.multi_contract_manager - 专门决策引擎初始化成功
[CryptoQuant] 2025-07-27 09:04:04,804.804 - INFO     - src.multi_contract_manager - 获取到3个活跃交易对: ['BTC-USDT-SWAP', 'ETH-USDT-SWAP', 'DOGE-USDT-SWAP']
[CryptoQuant] 2025-07-27 09:04:04,804.804 - INFO     - src.multi_contract_manager - 成功初始化了3个交易对
[CryptoQuant] 2025-07-27 09:04:04,805.805 - INFO     - src.multi_contract_manager - 多合约管理器初始化完成
[CryptoQuant] 2025-07-27 09:04:04,805.805 - INFO     - src.main_controller - 多合约管理器初始化完成
[CryptoQuant] 2025-07-27 09:04:04,805.805 - INFO     - src.main_controller - Web仪表盘模块引用已更新
[CryptoQuant] 2025-07-27 09:04:04,805.805 - INFO     - src.main_controller - API相关模块初始化完成
[CryptoQuant] 2025-07-27 09:04:04,806.806 - INFO     - src.web_dashboard - Web仪表盘启动成功: http://127.0.0.1:5000
[CryptoQuant] 2025-07-27 09:04:18,262.262 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:04:18,263.263 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117926.2', 'imr': '49.**************', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117926.8', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117908.5', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82497.***********', 'mmr': '1.414902', 'nonSettleAvgPx': '', 'notionalUsd': '353.*********', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '0.006', 'uplRatio': '-0.****************', 'uplRatioLastPx': '0.****************', 'usdPx': '1.00029', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:04:18,265.265 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.13 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:04:19,033.033 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:04:19,725.725 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:04:20,393.393 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:04:20,394.394 - INFO     - src.deprecated.trade_executor - 设置初始余额基准: 131316.45 USDT
[CryptoQuant] 2025-07-27 09:04:20,395.395 - INFO     - src.deprecated.trade_executor - 设置今日余额基准: 131316.45 USDT
[CryptoQuant] 2025-07-27 09:05:18,298.298 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:05:18,861.861 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:05:19,405.405 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:05:19,639.639 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:05:19,640.640 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117913.7', 'imr': '49.125', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117915.8', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117900', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82503.***********', 'mmr': '1.4148', 'nonSettleAvgPx': '', 'notionalUsd': '353.80611', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.027', 'uplRatio': '-0.***************', 'uplRatioLastPx': '-0.****************', 'usdPx': '1.0003', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:05:19,640.640 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.12 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:05:55,970.970 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:05:56,515.515 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:05:56,615.615 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:05:56,616.616 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117914.4', 'imr': '49.**************', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117910.7', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117898.5', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82504.**********', 'mmr': '1.414782', 'nonSettleAvgPx': '', 'notionalUsd': '353.********', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.****************', 'uplRatio': '-0.***************', 'uplRatioLastPx': '-0.****************', 'usdPx': '1.0003', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:05:56,617.617 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.12 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:05:57,055.055 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:05:57,412.412 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:05:57,414.414 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117914.6', 'imr': '49.1215', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117915.8', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117891.6', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82509.***********', 'mmr': '1.4146992', 'nonSettleAvgPx': '', 'notionalUsd': '353.********', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.027', 'uplRatio': '-0.****************', 'uplRatioLastPx': '-0.****************', 'usdPx': '1.0003', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:05:57,416.416 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.12 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:05:57,598.598 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:05:58,144.144 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:05:58,684.684 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:00,324.324 - INFO     - src.logger_monitor - 日志监控器初始化完成
[CryptoQuant] 2025-07-27 09:06:00,326.326 - INFO     - src.logger_monitor - 日志监控启动成功
[CryptoQuant] 2025-07-27 09:06:00,327.327 - INFO     - src.main_controller - 主控制器开始初始化
[CryptoQuant] 2025-07-27 09:06:00,328.328 - INFO     - src.deprecated.data_fetcher - 🔄 尝试连接OKX域名: https://www.okx.com
[CryptoQuant] 2025-07-27 09:06:02,461.461 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/time "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:02,462.462 - INFO     - src.deprecated.data_fetcher - 🟢 成功连接OKX域名: https://www.okx.com
[CryptoQuant] 2025-07-27 09:06:02,462.462 - INFO     - src.deprecated.data_fetcher - 🔧 API超时和连接池配置完成
[CryptoQuant] 2025-07-27 09:06:02,876.876 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/time "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:02,877.877 - INFO     - src.deprecated.data_fetcher - 🟢 OKX连接健康检查通过
[CryptoQuant] 2025-07-27 09:06:02,877.877 - INFO     - src.deprecated.data_fetcher - 🟢 OKX客户端初始化成功 - 使用域名: https://www.okx.com
[CryptoQuant] 2025-07-27 09:06:02,878.878 - INFO     - src.indicator_calculator - 指标参数加载完成
[CryptoQuant] 2025-07-27 09:06:02,879.879 - INFO     - src.risk_manager - 🔧 最终风险参数: {'max_leverage': 5.0, 'stop_loss_pct': 0.02, 'take_profit_pct': 0.04, 'max_position_ratio': 0.05, 'max_daily_loss_usdt': 0.05, 'min_balance_usdt': 10.0, 'safety_margin_ratio': 0.1, 'max_concurrent_positions': 3, 'emergency_stop_loss_pct': 0.15}
[CryptoQuant] 2025-07-27 09:06:02,880.880 - INFO     - src.risk_manager - 🛡️ 现代化风控系统初始化完成
[CryptoQuant] 2025-07-27 09:06:02,880.880 - INFO     - src.risk_manager - 📋 风控参数: {'max_leverage': 5.0, 'stop_loss_pct': 0.02, 'take_profit_pct': 0.04, 'max_position_ratio': 0.05, 'max_daily_loss_usdt': 0.05, 'min_balance_usdt': 10.0, 'safety_margin_ratio': 0.1, 'max_concurrent_positions': 3, 'emergency_stop_loss_pct': 0.15}
[CryptoQuant] 2025-07-27 09:06:02,891.891 - INFO     - src.web_dashboard - Web仪表盘初始化完成
[CryptoQuant] 2025-07-27 09:06:02,891.891 - INFO     - src.main_controller - 定时任务设置完成
[CryptoQuant] 2025-07-27 09:06:02,892.892 - INFO     - src.main_controller - 所有模块初始化完成
[CryptoQuant] 2025-07-27 09:06:02,893.893 - INFO     - src.web_dashboard - 启动Web服务器: http://0.0.0.0:5000
[CryptoQuant] 2025-07-27 09:06:02,893.893 - INFO     - src.main_controller - 开始初始化API相关模块...
[CryptoQuant] 2025-07-27 09:06:02,894.894 - INFO     - src.deprecated.trade_executor - 🔗 初始化交易API - 使用域名: https://www.okx.com
[CryptoQuant] 2025-07-27 09:06:02,923.923 - INFO     - src.deprecated.trade_executor - 🔧 交易API增强超时配置完成: 连接15s, 读取45s, 连接池30个
[CryptoQuant] 2025-07-27 09:06:04,529.529 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:04,530.530 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117914.6', 'imr': '49.***************', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117915.8', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117898.8', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82504.***********', 'mmr': '1.4147856', 'nonSettleAvgPx': '', 'notionalUsd': '353.**************', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.078', 'uplLastPx': '-0.027', 'uplRatio': '-0.****************', 'uplRatioLastPx': '-0.****************', 'usdPx': '1.0003', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:06:04,530.530 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.12 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:06:05,033.033 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:05,034.034 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117914.6', 'imr': '49.**************', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117915.8', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117900.7', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82502.***********', 'mmr': '1.4148084', 'nonSettleAvgPx': '', 'notionalUsd': '353.**************', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.0723000000000175', 'uplLastPx': '-0.027', 'uplRatio': '-0.001471446209788', 'uplRatioLastPx': '-0.****************', 'usdPx': '1.0003', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:06:05,035.035 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.13 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:06:05,364.364 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/time "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:05,365.365 - INFO     - src.deprecated.trade_executor - ✅ 交易API初始化完成，连接健康检查通过
[CryptoQuant] 2025-07-27 09:06:05,385.385 - INFO     - src.deprecated.trade_executor - 交易执行器初始化完成
[CryptoQuant] 2025-07-27 09:06:05,386.386 - INFO     - src.main_controller - 交易执行器初始化完成
[CryptoQuant] 2025-07-27 09:06:05,455.455 - INFO     - src.optimized_prompts.smart_hunter_engine - 智能猎手引擎 - API密钥加载成功
[CryptoQuant] 2025-07-27 09:06:05,455.455 - INFO     - src.optimized_prompts.smart_hunter_engine - 智能猎手引擎初始化完成
[CryptoQuant] 2025-07-27 09:06:05,455.455 - INFO     - src.optimized_prompts.risk_guardian_engine - 风险守护者引擎 - API密钥加载成功
[CryptoQuant] 2025-07-27 09:06:05,455.455 - INFO     - src.optimized_prompts.risk_guardian_engine - 风险守护者引擎初始化完成
[CryptoQuant] 2025-07-27 09:06:05,455.455 - INFO     - src.multi_contract_manager - 专门决策引擎初始化成功
[CryptoQuant] 2025-07-27 09:06:05,455.455 - INFO     - src.multi_contract_manager - 获取到3个活跃交易对: ['BTC-USDT-SWAP', 'ETH-USDT-SWAP', 'DOGE-USDT-SWAP']
[CryptoQuant] 2025-07-27 09:06:05,470.470 - INFO     - src.multi_contract_manager - 成功初始化了3个交易对
[CryptoQuant] 2025-07-27 09:06:05,471.471 - INFO     - src.multi_contract_manager - 多合约管理器初始化完成
[CryptoQuant] 2025-07-27 09:06:05,472.472 - INFO     - src.main_controller - 多合约管理器初始化完成
[CryptoQuant] 2025-07-27 09:06:05,472.472 - INFO     - src.main_controller - Web仪表盘模块引用已更新
[CryptoQuant] 2025-07-27 09:06:05,472.472 - INFO     - src.main_controller - API相关模块初始化完成
[CryptoQuant] 2025-07-27 09:06:05,472.472 - INFO     - src.web_dashboard - Web仪表盘启动成功: http://127.0.0.1:5000
[CryptoQuant] 2025-07-27 09:06:07,988.988 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:07,990.990 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117914.7', 'imr': '49.***************', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117916', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117892.7', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82508.**********', 'mmr': '1.4147124', 'nonSettleAvgPx': '', 'notionalUsd': '353.**************', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.****************', 'uplRatio': '-0.****************', 'uplRatioLastPx': '-0.****************', 'usdPx': '1.0003', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:06:07,991.991 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.12 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:06:09,251.251 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:09,789.789 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:10,471.471 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:10,473.473 - INFO     - src.deprecated.trade_executor - 设置初始余额基准: 131316.41 USDT
[CryptoQuant] 2025-07-27 09:06:10,473.473 - INFO     - src.deprecated.trade_executor - 设置今日余额基准: 131316.41 USDT
[CryptoQuant] 2025-07-27 09:06:10,939.939 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:10,939.939 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117914.7', 'imr': '49.***************', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117791', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117898.2', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82504.***********', 'mmr': '1.4147784', 'nonSettleAvgPx': '', 'notionalUsd': '353.**************', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.****************', 'uplRatio': '-0.****************', 'uplRatioLastPx': '-0.****************', 'usdPx': '1.0003', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:06:10,939.939 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.12 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:06:11,039.039 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:11,573.573 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:12,137.137 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:13,873.873 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:13,873.873 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117914.7', 'imr': '49.***************', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117916', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117897.3', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82505.***********', 'mmr': '1.4147676', 'nonSettleAvgPx': '', 'notionalUsd': '353.**************', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.0825', 'uplLastPx': '-0.****************', 'uplRatio': '-0.****************', 'uplRatioLastPx': '-0.****************', 'usdPx': '1.0003', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:06:13,873.873 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.12 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:06:14,049.049 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:14,604.604 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:15,147.147 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:16,918.918 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:16,919.919 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117914.7', 'imr': '49.**************', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117790.8', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117896.5', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82505.***********', 'mmr': '1.414758', 'nonSettleAvgPx': '', 'notionalUsd': '353.********', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.402', 'uplRatio': '-0.****************', 'uplRatioLastPx': '-0.****************', 'usdPx': '1.0003', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:06:16,920.920 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.12 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:06:17,056.056 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:17,605.605 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:18,153.153 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:19,880.880 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:19,881.881 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117914.7', 'imr': '49.**************', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117916', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117902.3', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82501.**********', 'mmr': '1.4148276', 'nonSettleAvgPx': '', 'notionalUsd': '353.********', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.0675', 'uplLastPx': '-0.****************', 'uplRatio': '-0.****************', 'uplRatioLastPx': '-0.****************', 'usdPx': '1.0003', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:06:19,882.882 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.13 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:06:20,038.038 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:20,597.597 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:21,319.319 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:22,900.900 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:22,902.902 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117914.7', 'imr': '49.**************', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117915.8', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117898.7', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82504.***********', 'mmr': '1.4147844', 'nonSettleAvgPx': '', 'notionalUsd': '353.********', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.027', 'uplRatio': '-0.****************', 'uplRatioLastPx': '-0.****************', 'usdPx': '1.0003', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:06:22,903.903 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.12 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:06:23,039.039 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:23,588.588 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:24,135.135 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:25,876.876 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:25,878.878 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117915.4', 'imr': '49.**************', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117791', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117904.1', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82500.**********', 'mmr': '1.****************', 'nonSettleAvgPx': '', 'notionalUsd': '353.********', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.****************', 'uplRatio': '-0.****************', 'uplRatioLastPx': '-0.****************', 'usdPx': '1.0003', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:06:25,878.878 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.13 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:06:26,042.042 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:26,597.597 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:27,145.145 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:28,913.913 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:28,915.915 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117915.4', 'imr': '49.**************', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117916', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117901.4', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82502.***********', 'mmr': '1.****************', 'nonSettleAvgPx': '', 'notionalUsd': '353.********', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.****************', 'uplRatio': '-0.****************', 'uplRatioLastPx': '-0.****************', 'usdPx': '1.0003', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:06:28,916.916 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.13 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:06:29,036.036 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:29,590.590 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:30,141.141 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:31,914.914 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:31,916.916 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117915.5', 'imr': '49.**************', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117915.8', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117898.7', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82504.***********', 'mmr': '1.4147844', 'nonSettleAvgPx': '', 'notionalUsd': '353.********', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.027', 'uplRatio': '-0.****************', 'uplRatioLastPx': '-0.****************', 'usdPx': '1.0003', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:06:31,916.916 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.12 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:06:32,059.059 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:32,620.620 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:33,164.164 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:34,907.907 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:34,909.909 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117915.5', 'imr': '49.127125', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117915.8', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117905.1', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82499.***********', 'mmr': '1.4148612', 'nonSettleAvgPx': '', 'notionalUsd': '353.********', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.027', 'uplRatio': '-0.***************', 'uplRatioLastPx': '-0.****************', 'usdPx': '1.0003', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:06:34,909.909 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.13 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:06:35,050.050 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:35,599.599 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:36,143.143 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:37,984.984 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:37,986.986 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117915.5', 'imr': '49.**************', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117915.8', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117898.6', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82504.***********', 'mmr': '1.4147832', 'nonSettleAvgPx': '', 'notionalUsd': '353.*********', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.027', 'uplRatio': '-0.****************', 'uplRatioLastPx': '-0.****************', 'usdPx': '1.00029', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:06:37,986.986 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.12 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:06:38,052.052 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:38,600.600 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:39,172.172 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:40,937.937 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:40,939.939 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117915.5', 'imr': '49.**************', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117916', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117906.5', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82498.**********', 'mmr': '1.414878', 'nonSettleAvgPx': '', 'notionalUsd': '353.*************', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.****************', 'uplRatio': '-0.****************', 'uplRatioLastPx': '-0.****************', 'usdPx': '1.00029', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:06:40,939.939 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.13 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:06:41,038.038 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:41,587.587 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:42,132.132 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:43,888.888 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:43,889.889 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117915.5', 'imr': '49.**************', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117916.1', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117910.8', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82495.***********', 'mmr': '1.4149296', 'nonSettleAvgPx': '', 'notionalUsd': '353.*********', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.042', 'uplLastPx': '-0.****************', 'uplRatio': '-0.****************', 'uplRatioLastPx': '-0.****************', 'usdPx': '1.00029', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:06:43,890.890 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.13 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:06:44,036.036 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:44,591.591 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:45,152.152 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:46,946.946 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:46,947.947 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117915.5', 'imr': '49.**************', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117916', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117913.1', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82494.***********', 'mmr': '1.****************', 'nonSettleAvgPx': '', 'notionalUsd': '353.********', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.****************', 'uplRatio': '-0.****************', 'uplRatioLastPx': '-0.****************', 'usdPx': '1.0003', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:06:46,948.948 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.13 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:06:47,056.056 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:47,614.614 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:48,161.161 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:49,996.996 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:49,997.997 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117924.6', 'imr': '49.**************', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117916.1', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117915.4', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82492.**********', 'mmr': '1.4149848', 'nonSettleAvgPx': '', 'notionalUsd': '353.**************', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '-0.****************', 'uplRatio': '-0.***************', 'uplRatioLastPx': '-0.****************', 'usdPx': '1.00029', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:06:49,998.998 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.13 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:06:50,052.052 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:50,600.600 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:51,148.148 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:52,967.967 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:52,968.968 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117938.4', 'imr': '49.132375', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117944.4', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117917.7', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82490.***********', 'mmr': '1.4150124', 'nonSettleAvgPx': '', 'notionalUsd': '353.********', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '0.****************', 'uplRatio': '-0.****************', 'uplRatioLastPx': '0.****************', 'usdPx': '1.0003', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:06:52,969.969 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.13 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:06:53,050.050 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:53,601.601 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:54,152.152 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:56,952.952 - INFO     - src.logger_monitor - 日志监控器初始化完成
[CryptoQuant] 2025-07-27 09:06:56,954.954 - INFO     - src.logger_monitor - 日志监控启动成功
[CryptoQuant] 2025-07-27 09:06:56,955.955 - INFO     - src.main_controller - 主控制器开始初始化
[CryptoQuant] 2025-07-27 09:06:56,956.956 - INFO     - src.deprecated.data_fetcher - 🔄 尝试连接OKX域名: https://www.okx.com
[CryptoQuant] 2025-07-27 09:06:59,177.177 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/time "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:59,179.179 - INFO     - src.deprecated.data_fetcher - 🟢 成功连接OKX域名: https://www.okx.com
[CryptoQuant] 2025-07-27 09:06:59,180.180 - INFO     - src.deprecated.data_fetcher - 🔧 API超时和连接池配置完成
[CryptoQuant] 2025-07-27 09:06:59,745.745 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/time "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:06:59,747.747 - INFO     - src.deprecated.data_fetcher - 🟢 OKX连接健康检查通过
[CryptoQuant] 2025-07-27 09:06:59,748.748 - INFO     - src.deprecated.data_fetcher - 🟢 OKX客户端初始化成功 - 使用域名: https://www.okx.com
[CryptoQuant] 2025-07-27 09:06:59,749.749 - INFO     - src.indicator_calculator - 指标参数加载完成
[CryptoQuant] 2025-07-27 09:06:59,750.750 - INFO     - src.risk_manager - 🔧 最终风险参数: {'max_leverage': 5.0, 'stop_loss_pct': 0.02, 'take_profit_pct': 0.04, 'max_position_ratio': 0.05, 'max_daily_loss_usdt': 0.05, 'min_balance_usdt': 10.0, 'safety_margin_ratio': 0.1, 'max_concurrent_positions': 3, 'emergency_stop_loss_pct': 0.15}
[CryptoQuant] 2025-07-27 09:06:59,750.750 - INFO     - src.risk_manager - 🛡️ 现代化风控系统初始化完成
[CryptoQuant] 2025-07-27 09:06:59,751.751 - INFO     - src.risk_manager - 📋 风控参数: {'max_leverage': 5.0, 'stop_loss_pct': 0.02, 'take_profit_pct': 0.04, 'max_position_ratio': 0.05, 'max_daily_loss_usdt': 0.05, 'min_balance_usdt': 10.0, 'safety_margin_ratio': 0.1, 'max_concurrent_positions': 3, 'emergency_stop_loss_pct': 0.15}
[CryptoQuant] 2025-07-27 09:06:59,760.760 - INFO     - src.web_dashboard - Web仪表盘初始化完成
[CryptoQuant] 2025-07-27 09:06:59,762.762 - INFO     - src.main_controller - 定时任务设置完成
[CryptoQuant] 2025-07-27 09:06:59,762.762 - INFO     - src.main_controller - 所有模块初始化完成
[CryptoQuant] 2025-07-27 09:06:59,764.764 - INFO     - src.web_dashboard - 启动Web服务器: http://0.0.0.0:5000
[CryptoQuant] 2025-07-27 09:06:59,765.765 - INFO     - src.main_controller - 开始初始化API相关模块...
[CryptoQuant] 2025-07-27 09:06:59,765.765 - INFO     - src.deprecated.trade_executor - 🔗 初始化交易API - 使用域名: https://www.okx.com
[CryptoQuant] 2025-07-27 09:06:59,797.797 - INFO     - src.deprecated.trade_executor - 🔧 交易API增强超时配置完成: 连接15s, 读取45s, 连接池30个
[CryptoQuant] 2025-07-27 09:07:01,570.570 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:07:01,571.571 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117954.8', 'imr': '49.***************', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117805', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117924.6', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82486.**********', 'mmr': '1.4150952', 'nonSettleAvgPx': '', 'notionalUsd': '353.*************', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.0005999999999913', 'uplLastPx': '-0.****************', 'uplRatio': '-0.0000122111718657', 'uplRatioLastPx': '-0.****************', 'usdPx': '1.00028', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:07:01,572.572 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.14 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:07:01,602.602 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/time "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:07:01,603.603 - INFO     - src.deprecated.trade_executor - ✅ 交易API初始化完成，连接健康检查通过
[CryptoQuant] 2025-07-27 09:07:01,624.624 - INFO     - src.deprecated.trade_executor - 交易执行器初始化完成
[CryptoQuant] 2025-07-27 09:07:01,625.625 - INFO     - src.main_controller - 交易执行器初始化完成
[CryptoQuant] 2025-07-27 09:07:01,700.700 - INFO     - src.optimized_prompts.smart_hunter_engine - 智能猎手引擎 - API密钥加载成功
[CryptoQuant] 2025-07-27 09:07:01,700.700 - INFO     - src.optimized_prompts.smart_hunter_engine - 智能猎手引擎初始化完成
[CryptoQuant] 2025-07-27 09:07:01,701.701 - INFO     - src.optimized_prompts.risk_guardian_engine - 风险守护者引擎 - API密钥加载成功
[CryptoQuant] 2025-07-27 09:07:01,701.701 - INFO     - src.optimized_prompts.risk_guardian_engine - 风险守护者引擎初始化完成
[CryptoQuant] 2025-07-27 09:07:01,703.703 - INFO     - src.multi_contract_manager - 专门决策引擎初始化成功
[CryptoQuant] 2025-07-27 09:07:01,703.703 - INFO     - src.multi_contract_manager - 获取到3个活跃交易对: ['BTC-USDT-SWAP', 'ETH-USDT-SWAP', 'DOGE-USDT-SWAP']
[CryptoQuant] 2025-07-27 09:07:01,703.703 - INFO     - src.multi_contract_manager - 成功初始化了3个交易对
[CryptoQuant] 2025-07-27 09:07:01,703.703 - INFO     - src.multi_contract_manager - 多合约管理器初始化完成
[CryptoQuant] 2025-07-27 09:07:01,704.704 - INFO     - src.main_controller - 多合约管理器初始化完成
[CryptoQuant] 2025-07-27 09:07:01,704.704 - INFO     - src.main_controller - Web仪表盘模块引用已更新
[CryptoQuant] 2025-07-27 09:07:01,705.705 - INFO     - src.main_controller - API相关模块初始化完成
[CryptoQuant] 2025-07-27 09:07:01,705.705 - INFO     - src.web_dashboard - Web仪表盘启动成功: http://127.0.0.1:5000
[CryptoQuant] 2025-07-27 09:07:02,097.097 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:07:02,099.099 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117958.6', 'imr': '49.**************', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117954.5', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117920', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82489.***********', 'mmr': '1.41504', 'nonSettleAvgPx': '', 'notionalUsd': '353.**************', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.****************', 'uplLastPx': '0.****************', 'uplRatio': '-0.****************', 'uplRatioLastPx': '0.****************', 'usdPx': '1.00028', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:07:02,100.100 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.13 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:07:06,027.027 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:07:06,028.028 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117970.3', 'imr': '49.131375', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117825', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117915.3', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82492.***********', 'mmr': '1.4149836', 'nonSettleAvgPx': '', 'notionalUsd': '353.*********', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '-0.0285', 'uplLastPx': '-0.****************', 'uplRatio': '-0.****************', 'uplRatioLastPx': '-0.****************', 'usdPx': '1.00027', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:07:06,029.029 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.13 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:07:06,913.913 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:07:07,315.315 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:07:07,731.731 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:07:07,733.733 - INFO     - src.deprecated.trade_executor - 设置初始余额基准: 131316.45 USDT
[CryptoQuant] 2025-07-27 09:07:07,733.733 - INFO     - src.deprecated.trade_executor - 设置今日余额基准: 131316.45 USDT
[CryptoQuant] 2025-07-27 09:07:08,894.894 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:07:09,112.112 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:07:09,114.114 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117972.5', 'imr': '49.**************', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117982.2', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117925.1', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82485.***********', 'mmr': '1.4151012', 'nonSettleAvgPx': '', 'notionalUsd': '353.*********', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '0.****************', 'uplLastPx': '0.****************', 'uplRatio': '0.****************', 'uplRatioLastPx': '0.****************', 'usdPx': '1.00029', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:07:09,115.115 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.14 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:07:09,269.269 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:07:09,645.645 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:07:12,296.296 - INFO     - src.logger_monitor - 日志监控器初始化完成
[CryptoQuant] 2025-07-27 09:07:12,297.297 - INFO     - src.logger_monitor - 日志监控启动成功
[CryptoQuant] 2025-07-27 09:07:12,298.298 - INFO     - src.main_controller - 主控制器开始初始化
[CryptoQuant] 2025-07-27 09:07:12,299.299 - INFO     - src.deprecated.data_fetcher - 🔄 尝试连接OKX域名: https://www.okx.com
[CryptoQuant] 2025-07-27 09:07:14,572.572 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/time "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:07:14,573.573 - INFO     - src.deprecated.data_fetcher - 🟢 成功连接OKX域名: https://www.okx.com
[CryptoQuant] 2025-07-27 09:07:14,574.574 - INFO     - src.deprecated.data_fetcher - 🔧 API超时和连接池配置完成
[CryptoQuant] 2025-07-27 09:07:15,184.184 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/time "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:07:15,185.185 - INFO     - src.deprecated.data_fetcher - 🟢 OKX连接健康检查通过
[CryptoQuant] 2025-07-27 09:07:15,185.185 - INFO     - src.deprecated.data_fetcher - 🟢 OKX客户端初始化成功 - 使用域名: https://www.okx.com
[CryptoQuant] 2025-07-27 09:07:15,186.186 - INFO     - src.indicator_calculator - 指标参数加载完成
[CryptoQuant] 2025-07-27 09:07:15,187.187 - INFO     - src.risk_manager - 🔧 最终风险参数: {'max_leverage': 5.0, 'stop_loss_pct': 0.02, 'take_profit_pct': 0.04, 'max_position_ratio': 0.05, 'max_daily_loss_usdt': 0.05, 'min_balance_usdt': 10.0, 'safety_margin_ratio': 0.1, 'max_concurrent_positions': 3, 'emergency_stop_loss_pct': 0.15}
[CryptoQuant] 2025-07-27 09:07:15,187.187 - INFO     - src.risk_manager - 🛡️ 现代化风控系统初始化完成
[CryptoQuant] 2025-07-27 09:07:15,188.188 - INFO     - src.risk_manager - 📋 风控参数: {'max_leverage': 5.0, 'stop_loss_pct': 0.02, 'take_profit_pct': 0.04, 'max_position_ratio': 0.05, 'max_daily_loss_usdt': 0.05, 'min_balance_usdt': 10.0, 'safety_margin_ratio': 0.1, 'max_concurrent_positions': 3, 'emergency_stop_loss_pct': 0.15}
[CryptoQuant] 2025-07-27 09:07:15,197.197 - INFO     - src.web_dashboard - Web仪表盘初始化完成
[CryptoQuant] 2025-07-27 09:07:15,198.198 - INFO     - src.main_controller - 定时任务设置完成
[CryptoQuant] 2025-07-27 09:07:15,199.199 - INFO     - src.main_controller - 所有模块初始化完成
[CryptoQuant] 2025-07-27 09:07:15,201.201 - INFO     - src.web_dashboard - 启动Web服务器: http://0.0.0.0:5000
[CryptoQuant] 2025-07-27 09:07:15,202.202 - INFO     - src.main_controller - 开始初始化API相关模块...
[CryptoQuant] 2025-07-27 09:07:15,203.203 - INFO     - src.deprecated.trade_executor - 🔗 初始化交易API - 使用域名: https://www.okx.com
[CryptoQuant] 2025-07-27 09:07:15,236.236 - INFO     - src.deprecated.trade_executor - 🔧 交易API增强超时配置完成: 连接15s, 读取45s, 连接池30个
[CryptoQuant] 2025-07-27 09:07:16,407.407 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/time "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:07:16,408.408 - INFO     - src.deprecated.trade_executor - ✅ 交易API初始化完成，连接健康检查通过
[CryptoQuant] 2025-07-27 09:07:16,428.428 - INFO     - src.deprecated.trade_executor - 交易执行器初始化完成
[CryptoQuant] 2025-07-27 09:07:16,429.429 - INFO     - src.main_controller - 交易执行器初始化完成
[CryptoQuant] 2025-07-27 09:07:16,527.527 - INFO     - src.optimized_prompts.smart_hunter_engine - 智能猎手引擎 - API密钥加载成功
[CryptoQuant] 2025-07-27 09:07:16,528.528 - INFO     - src.optimized_prompts.smart_hunter_engine - 智能猎手引擎初始化完成
[CryptoQuant] 2025-07-27 09:07:16,528.528 - INFO     - src.optimized_prompts.risk_guardian_engine - 风险守护者引擎 - API密钥加载成功
[CryptoQuant] 2025-07-27 09:07:16,529.529 - INFO     - src.optimized_prompts.risk_guardian_engine - 风险守护者引擎初始化完成
[CryptoQuant] 2025-07-27 09:07:16,530.530 - INFO     - src.multi_contract_manager - 专门决策引擎初始化成功
[CryptoQuant] 2025-07-27 09:07:16,530.530 - INFO     - src.multi_contract_manager - 获取到3个活跃交易对: ['BTC-USDT-SWAP', 'ETH-USDT-SWAP', 'DOGE-USDT-SWAP']
[CryptoQuant] 2025-07-27 09:07:16,531.531 - INFO     - src.multi_contract_manager - 成功初始化了3个交易对
[CryptoQuant] 2025-07-27 09:07:16,532.532 - INFO     - src.multi_contract_manager - 多合约管理器初始化完成
[CryptoQuant] 2025-07-27 09:07:16,532.532 - INFO     - src.main_controller - 多合约管理器初始化完成
[CryptoQuant] 2025-07-27 09:07:16,532.532 - INFO     - src.main_controller - Web仪表盘模块引用已更新
[CryptoQuant] 2025-07-27 09:07:16,533.533 - INFO     - src.main_controller - API相关模块初始化完成
[CryptoQuant] 2025-07-27 09:07:16,534.534 - INFO     - src.web_dashboard - Web仪表盘启动成功: http://127.0.0.1:5000
[CryptoQuant] 2025-07-27 09:07:16,796.796 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:07:16,797.797 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117972.6', 'imr': '49.***************', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117847', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117951.5', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82467.***********', 'mmr': '1.415418', 'nonSettleAvgPx': '', 'notionalUsd': '353.*********', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '0.****************', 'uplLastPx': '-0.****************', 'uplRatio': '0.****************', 'uplRatioLastPx': '-0.****************', 'usdPx': '1.00029', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:07:16,798.798 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.15 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:07:17,270.270 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:07:17,270.270 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117972.6', 'imr': '49.**************', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117982.2', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117951.2', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82467.***********', 'mmr': '1.4154144', 'nonSettleAvgPx': '', 'notionalUsd': '353.*************', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '0.****************', 'uplLastPx': '0.****************', 'uplRatio': '0.**************', 'uplRatioLastPx': '0.****************', 'usdPx': '1.00029', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:07:17,271.271 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.15 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:07:18,070.070 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:07:18,090.090 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117972.6', 'imr': '49.***************', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117982.2', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117955.8', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82464.**********', 'mmr': '1.4154696', 'nonSettleAvgPx': '', 'notionalUsd': '353.*********', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '0.093', 'uplLastPx': '0.****************', 'uplRatio': '0.****************', 'uplRatioLastPx': '0.****************', 'usdPx': '1.00029', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:07:18,091.091 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.15 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:07:19,275.275 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:07:19,659.659 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:07:20,108.108 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:07:20,110.110 - INFO     - src.deprecated.trade_executor - 设置初始余额基准: 131316.60 USDT
[CryptoQuant] 2025-07-27 09:07:20,110.110 - INFO     - src.deprecated.trade_executor - 设置今日余额基准: 131316.60 USDT
[CryptoQuant] 2025-07-27 09:07:21,001.001 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:07:21,007.007 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:07:21,010.010 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117972.5', 'imr': '49.**************', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '117847', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117961.9', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82460.***********', 'mmr': '1.****************', 'nonSettleAvgPx': '', 'notionalUsd': '353.**************', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '0.****************', 'uplLastPx': '-0.****************', 'uplRatio': '0.****************', 'uplRatioLastPx': '-0.****************', 'usdPx': '1.00029', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:07:21,011.011 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.15 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:07:21,497.497 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:07:22,030.030 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:07:57,829.829 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:07:57,898.898 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:07:57,900.900 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '117999.7', 'imr': '49.**************', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '118004.3', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117948.2', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82469.***********', 'mmr': '1.4153784', 'nonSettleAvgPx': '', 'notionalUsd': '353.*********', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '0.****************', 'uplLastPx': '0.2385', 'uplRatio': '0.****************', 'uplRatioLastPx': '0.****************', 'usdPx': '1.00027', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:07:57,901.901 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.15 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:07:58,207.207 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:07:58,582.582 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:08:04,722.722 - INFO     - src.logger_monitor - 日志监控器初始化完成
[CryptoQuant] 2025-07-27 09:08:04,724.724 - INFO     - src.logger_monitor - 日志监控启动成功
[CryptoQuant] 2025-07-27 09:08:04,725.725 - INFO     - src.main_controller - 主控制器开始初始化
[CryptoQuant] 2025-07-27 09:08:04,726.726 - INFO     - src.deprecated.data_fetcher - 🔄 尝试连接OKX域名: https://www.okx.com
[CryptoQuant] 2025-07-27 09:08:06,577.577 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/time "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:08:06,579.579 - INFO     - src.deprecated.data_fetcher - 🟢 成功连接OKX域名: https://www.okx.com
[CryptoQuant] 2025-07-27 09:08:06,580.580 - INFO     - src.deprecated.data_fetcher - 🔧 API超时和连接池配置完成
[CryptoQuant] 2025-07-27 09:08:07,134.134 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/time "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:08:07,135.135 - INFO     - src.deprecated.data_fetcher - 🟢 OKX连接健康检查通过
[CryptoQuant] 2025-07-27 09:08:07,136.136 - INFO     - src.deprecated.data_fetcher - 🟢 OKX客户端初始化成功 - 使用域名: https://www.okx.com
[CryptoQuant] 2025-07-27 09:08:07,137.137 - INFO     - src.indicator_calculator - 指标参数加载完成
[CryptoQuant] 2025-07-27 09:08:07,138.138 - INFO     - src.risk_manager - 🔧 最终风险参数: {'max_leverage': 5.0, 'stop_loss_pct': 0.02, 'take_profit_pct': 0.04, 'max_position_ratio': 0.05, 'max_daily_loss_usdt': 0.05, 'min_balance_usdt': 10.0, 'safety_margin_ratio': 0.1, 'max_concurrent_positions': 3, 'emergency_stop_loss_pct': 0.15}
[CryptoQuant] 2025-07-27 09:08:07,138.138 - INFO     - src.risk_manager - 🛡️ 现代化风控系统初始化完成
[CryptoQuant] 2025-07-27 09:08:07,139.139 - INFO     - src.risk_manager - 📋 风控参数: {'max_leverage': 5.0, 'stop_loss_pct': 0.02, 'take_profit_pct': 0.04, 'max_position_ratio': 0.05, 'max_daily_loss_usdt': 0.05, 'min_balance_usdt': 10.0, 'safety_margin_ratio': 0.1, 'max_concurrent_positions': 3, 'emergency_stop_loss_pct': 0.15}
[CryptoQuant] 2025-07-27 09:08:07,149.149 - INFO     - src.web_dashboard - Web仪表盘初始化完成
[CryptoQuant] 2025-07-27 09:08:07,150.150 - INFO     - src.main_controller - 定时任务设置完成
[CryptoQuant] 2025-07-27 09:08:07,150.150 - INFO     - src.main_controller - 所有模块初始化完成
[CryptoQuant] 2025-07-27 09:08:07,151.151 - INFO     - src.web_dashboard - 启动Web服务器: http://0.0.0.0:5000
[CryptoQuant] 2025-07-27 09:08:07,152.152 - INFO     - src.main_controller - 开始初始化API相关模块...
[CryptoQuant] 2025-07-27 09:08:07,153.153 - INFO     - src.deprecated.trade_executor - 🔗 初始化交易API - 使用域名: https://www.okx.com
[CryptoQuant] 2025-07-27 09:08:07,183.183 - INFO     - src.deprecated.trade_executor - 🔧 交易API增强超时配置完成: 连接15s, 读取45s, 连接池30个
[CryptoQuant] 2025-07-27 09:08:08,981.981 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/public/time "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:08:08,982.982 - INFO     - src.deprecated.trade_executor - ✅ 交易API初始化完成，连接健康检查通过
[CryptoQuant] 2025-07-27 09:08:09,003.003 - INFO     - src.deprecated.trade_executor - 交易执行器初始化完成
[CryptoQuant] 2025-07-27 09:08:09,004.004 - INFO     - src.main_controller - 交易执行器初始化完成
[CryptoQuant] 2025-07-27 09:08:09,086.086 - INFO     - src.optimized_prompts.smart_hunter_engine - 智能猎手引擎 - API密钥加载成功
[CryptoQuant] 2025-07-27 09:08:09,086.086 - INFO     - src.optimized_prompts.smart_hunter_engine - 智能猎手引擎初始化完成
[CryptoQuant] 2025-07-27 09:08:09,087.087 - INFO     - src.optimized_prompts.risk_guardian_engine - 风险守护者引擎 - API密钥加载成功
[CryptoQuant] 2025-07-27 09:08:09,087.087 - INFO     - src.optimized_prompts.risk_guardian_engine - 风险守护者引擎初始化完成
[CryptoQuant] 2025-07-27 09:08:09,088.088 - INFO     - src.multi_contract_manager - 专门决策引擎初始化成功
[CryptoQuant] 2025-07-27 09:08:09,088.088 - INFO     - src.multi_contract_manager - 获取到3个活跃交易对: ['BTC-USDT-SWAP', 'ETH-USDT-SWAP', 'DOGE-USDT-SWAP']
[CryptoQuant] 2025-07-27 09:08:09,088.088 - INFO     - src.multi_contract_manager - 成功初始化了3个交易对
[CryptoQuant] 2025-07-27 09:08:09,088.088 - INFO     - src.multi_contract_manager - 多合约管理器初始化完成
[CryptoQuant] 2025-07-27 09:08:09,088.088 - INFO     - src.main_controller - 多合约管理器初始化完成
[CryptoQuant] 2025-07-27 09:08:09,089.089 - INFO     - src.main_controller - Web仪表盘模块引用已更新
[CryptoQuant] 2025-07-27 09:08:09,089.089 - INFO     - src.main_controller - API相关模块初始化完成
[CryptoQuant] 2025-07-27 09:08:09,089.089 - INFO     - src.web_dashboard - Web仪表盘启动成功: http://127.0.0.1:5000
[CryptoQuant] 2025-07-27 09:08:17,805.805 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:08:17,806.806 - INFO     - src.deprecated.data_fetcher - 🔍 原始持仓数据 BTC-USDT-SWAP: {'adl': '1', 'availPos': '0.3', 'avgPx': '117924.8', 'baseBal': '', 'baseBorrowed': '', 'baseInterest': '', 'bePx': '118042.***********', 'bizRefId': '', 'bizRefType': '', 'cTime': '*************', 'ccy': 'USDT', 'clSpotInUseAmt': '', 'closeOrderAlgo': [], 'deltaBS': '', 'deltaPA': '', 'fee': '-0.1768872', 'fundingFee': '0', 'gammaBS': '', 'gammaPA': '', 'idxPx': '118008.4', 'imr': '49.161625', 'instId': 'BTC-USDT-SWAP', 'instType': 'SWAP', 'interest': '', 'last': '118016.5', 'lever': '7.2', 'liab': '', 'liabCcy': '', 'liqPenalty': '0', 'liqPx': '', 'margin': '', 'markPx': '117987.9', 'maxSpotInUseAmt': '', 'mgnMode': 'cross', 'mgnRatio': '82442.***********', 'mmr': '1.4158548', 'nonSettleAvgPx': '', 'notionalUsd': '354.*********', 'optVal': '', 'pendingCloseOrdLiabVal': '', 'pnl': '0', 'pos': '0.3', 'posCcy': '', 'posId': '*********7955581952', 'posSide': 'long', 'quoteBal': '', 'quoteBorrowed': '', 'quoteInterest': '', 'realizedPnl': '-0.1768872', 'settledPnl': '', 'spotInUseAmt': '', 'spotInUseCcy': '', 'thetaBS': '', 'thetaPA': '', 'tradeId': '**********', 'uTime': '*************', 'upl': '0.****************', 'uplLastPx': '0.****************', 'uplRatio': '0.****************', 'uplRatioLastPx': '0.****************', 'usdPx': '1.00027', 'vegaBS': '', 'vegaPA': ''}
[CryptoQuant] 2025-07-27 09:08:17,807.807 - INFO     - src.deprecated.data_fetcher - 🔍 使用OKX保证金: BTC-USDT-SWAP = 49.16 USDT (来源: imr)
[CryptoQuant] 2025-07-27 09:08:18,749.749 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:08:19,307.307 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:08:19,876.876 - INFO     - httpx - HTTP Request: GET https://www.okx.com/api/v5/account/positions "HTTP/2 200 OK"
[CryptoQuant] 2025-07-27 09:08:19,877.877 - INFO     - src.deprecated.trade_executor - 设置初始余额基准: 131316.69 USDT
[CryptoQuant] 2025-07-27 09:08:19,878.878 - INFO     - src.deprecated.trade_executor - 设置今日余额基准: 131316.69 USDT
[CryptoQuant] 2025-07-27 09:09:11,735.735 - INFO     - src.logger_monitor - 日志监控器初始化完成
[CryptoQuant] 2025-07-27 09:09:11,736.736 - INFO     - src.logger_monitor - 日志监控启动成功
[CryptoQuant] 2025-07-27 09:09:11,738.738 - INFO     - src.logger_monitor - 日志监控器初始化完成
[CryptoQuant] 2025-07-27 09:10:40,154.154 - INFO     - src.logger_monitor - 日志监控器初始化完成
[CryptoQuant] 2025-07-27 09:10:40,156.156 - INFO     - src.logger_monitor - 日志监控启动成功
[CryptoQuant] 2025-07-27 09:10:40,159.159 - INFO     - src.logger_monitor - 日志监控器初始化完成
[CryptoQuant] 2025-07-27 09:10:57,623.623 - INFO     - src.logger_monitor - 日志监控器初始化完成
[CryptoQuant] 2025-07-27 09:10:57,624.624 - INFO     - src.logger_monitor - 日志监控启动成功
[CryptoQuant] 2025-07-27 09:10:57,626.626 - INFO     - src.logger_monitor - 日志监控器初始化完成
[CryptoQuant] 2025-07-27 09:11:32,575.575 - INFO     - src.logger_monitor - 日志监控器初始化完成
[CryptoQuant] 2025-07-27 09:11:32,576.576 - INFO     - src.logger_monitor - 日志监控启动成功
[CryptoQuant] 2025-07-27 09:11:32,578.578 - INFO     - src.logger_monitor - 日志监控器初始化完成
[CryptoQuant] 2025-07-27 09:12:28,790.790 - INFO     - src.logger_monitor - 日志监控器初始化完成
[CryptoQuant] 2025-07-27 09:12:28,791.791 - INFO     - src.logger_monitor - 日志监控启动成功
[CryptoQuant] 2025-07-27 09:12:28,793.793 - INFO     - src.logger_monitor - 日志监控器初始化完成
[CryptoQuant] 2025-07-27 09:14:07,351.351 - INFO     - src.logger_monitor - 日志监控器初始化完成
[CryptoQuant] 2025-07-27 09:14:07,353.353 - INFO     - src.logger_monitor - 日志监控启动成功
[CryptoQuant] 2025-07-27 09:14:07,354.354 - INFO     - src.logger_monitor - 日志监控器初始化完成
[CryptoQuant] 2025-07-27 09:14:30,192.192 - INFO     - src.logger_monitor - 日志监控器初始化完成
[CryptoQuant] 2025-07-27 09:14:30,193.193 - INFO     - src.logger_monitor - 日志监控启动成功
[CryptoQuant] 2025-07-27 09:14:30,195.195 - INFO     - src.logger_monitor - 日志监控器初始化完成
[CryptoQuant] 2025-07-27 09:15:41,130.130 - INFO     - src.logger_monitor - 日志监控器初始化完成
[CryptoQuant] 2025-07-27 09:15:41,132.132 - INFO     - src.logger_monitor - 日志监控启动成功
[CryptoQuant] 2025-07-27 09:15:41,134.134 - INFO     - src.logger_monitor - 日志监控器初始化完成
[CryptoQuant] 2025-07-27 09:16:20,226.226 - INFO     - src.logger_monitor - 日志监控器初始化完成
[CryptoQuant] 2025-07-27 09:16:20,227.227 - INFO     - src.logger_monitor - 日志监控启动成功
[CryptoQuant] 2025-07-27 09:16:20,229.229 - INFO     - src.logger_monitor - 日志监控器初始化完成
[CryptoQuant] 2025-07-27 09:16:41,584.584 - INFO     - src.logger_monitor - 日志监控器初始化完成
[CryptoQuant] 2025-07-27 09:18:56,201.201 - INFO     - src.logger_monitor - 日志监控器初始化完成
[CryptoQuant] 2025-07-27 09:18:56,203.203 - INFO     - src.logger_monitor - 日志监控启动成功
[CryptoQuant] 2025-07-27 09:21:50,638.638 - INFO     - src.multi_contract_manager - 多合约管理器已停止
[CryptoQuant] 2025-07-27 09:21:50,639.639 - INFO     - src.web_dashboard - Web仪表盘停止请求已发送
[CryptoQuant] 2025-07-27 09:21:55,650.650 - INFO     - src.logger_monitor - 日志监控已停止
[CryptoQuant] 2025-07-27 09:21:55,652.652 - INFO     - src.deprecated.data_fetcher - 数据缓存已清空
[CryptoQuant] 2025-07-27 09:21:55,653.653 - INFO     - src.main_controller - 系统已停止
2025-07-27 09:26:44,925.925-INFO-src.logger_monitor-日志监控器初始化完成
2025-07-27 09:26:44,926.926-INFO-src.logger_monitor-日志监控启动成功
2025-07-27 09:26:49,284.284-INFO-src.logger_monitor-日志监控器初始化完成
2025-07-27 09:26:49,286.286-INFO-src.logger_monitor-日志监控启动成功
2025-07-27 09:38:25,293.293-INFO-src.logger_monitor-日志监控器初始化完成
2025-07-27 09:38:25,295.295-INFO-src.logger_monitor-日志监控启动成功
2025-07-27 09:39:13,004.004-INFO-src.logger_monitor-日志监控器初始化完成
2025-07-27 09:39:13,006.006-INFO-src.logger_monitor-日志监控启动成功
2025-07-27 09:41:20,363.363-INFO-src.logger_monitor-日志监控器初始化完成
2025-07-27 09:41:20,365.365-INFO-src.logger_monitor-日志监控启动成功
