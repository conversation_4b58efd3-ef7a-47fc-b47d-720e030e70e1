"""
现代化风控管理系统 - RiskManager
专为解决allowed_size=0问题而设计的风险管理器

核心特性：
1. 合理的默认参数（最小余额10 USDT，避免过度严格）
2. 完整的向后兼容性（支持旧版接口）
3. 增强的调试日志系统
4. 灵活的配置参数管理
5. 智能的资金和仓位风险控制
"""

import logging
from typing import Dict, Any, Optional
from datetime import datetime
from decimal import Decimal, ROUND_HALF_UP


class RiskManager:
    """现代化风险管理器 - 解决allowed_size=0问题的核心组件"""
    
    def __init__(self, config_manager):
        """
        初始化风险管理器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config = config_manager
        self.logger = logging.getLogger(__name__)
        
        # 移除自定义日志处理器设置，使用全局统一的日志配置（由LoggerMonitor设置）
        # 这样确保所有模块都使用相同的日志格式：[CryptoQuant] 时间戳 - 级别 - 模块名 - 消息
        
        # 加载风险参数
        self.risk_params = self._load_risk_params()
        self.logger.info("🛡️ 现代化风控系统初始化完成")
        self.logger.info(f"📋 风控参数: {self.risk_params}")
        
    def _load_risk_params(self) -> Dict[str, Any]:
        """
        加载风险参数
        优先级: 用户配置 > 交易对配置 > 合理默认值
        """
        # 🔧 合理的默认参数（解决allowed_size=0问题）
        defaults = {
            'max_leverage': 3.0,                    # 默认3倍杠杆（保守）
            'stop_loss_pct': 0.02,                  # 2%止损
            'take_profit_pct': 0.04,                # 4%止盈
            'max_position_ratio': 0.05,             # 5%最大仓位比例
            'max_daily_loss_usdt': 100.0,           # 每日最大亏损100 USDT
            'min_balance_usdt': 10.0,               # 🎯 关键修复：最小余额10 USDT（而不是100）
            'safety_margin_ratio': 0.1,             # 10%安全边际
            'max_concurrent_positions': 3,          # 最大并发持仓数
            'emergency_stop_loss_pct': 0.15         # 15%紧急止损
        }
        
        # 从配置获取用户设置
        user_params = self._get_user_params()
        
        # 合并参数
        final_params = defaults.copy()
        final_params.update({
            k: v for k, v in user_params.items()
            if v is not None and v != 0
        })
        
        # 参数验证和调整
        final_params = self._validate_risk_params(final_params)
        
        self.logger.info(f"🔧 最终风险参数: {final_params}")
        return final_params
    
    def _get_user_params(self) -> Dict[str, Any]:
        """从配置中获取用户设置"""
        try:
            # 尝试从多个配置路径获取参数
            risk_config = self.config.get('risk_management', {})
            trading_config = self.config.get('trading_pairs', {})
            
            return {
                'max_leverage': self._safe_float(risk_config.get('max_leverage') or trading_config.get('max_leverage')),
                'stop_loss_pct': self._safe_float(risk_config.get('stop_loss') or trading_config.get('stop_loss')) / 100 if risk_config.get('stop_loss') or trading_config.get('stop_loss') else None,
                'take_profit_pct': self._safe_float(risk_config.get('take_profit') or trading_config.get('take_profit')) / 100 if risk_config.get('take_profit') or trading_config.get('take_profit') else None,
                'max_position_ratio': self._safe_float(risk_config.get('max_position_ratio') or trading_config.get('max_position_ratio')) / 100 if risk_config.get('max_position_ratio') or trading_config.get('max_position_ratio') else None,
                'max_daily_loss_usdt': self._safe_float(risk_config.get('max_daily_loss') or trading_config.get('max_daily_loss')),
                'min_balance_usdt': self._safe_float(risk_config.get('min_balance') or trading_config.get('min_balance')),
                'safety_margin_ratio': self._safe_float(risk_config.get('safety_margin_ratio')),
                'max_concurrent_positions': risk_config.get('max_concurrent_positions'),
                'emergency_stop_loss_pct': self._safe_float(risk_config.get('emergency_stop_loss_pct'))
            }
        except Exception as e:
            self.logger.warning(f"获取用户参数失败: {e}")
            return {}
    
    def _safe_float(self, value) -> Optional[float]:
        """安全转换为浮点数"""
        if value is None:
            return None
        try:
            return float(value)
        except (ValueError, TypeError):
            return None
    
    def _validate_risk_params(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """验证和调整风险参数"""
        validated = params.copy()
        
        # 杠杆限制
        if validated['max_leverage'] > 20:
            self.logger.warning(f"杠杆倍数过高: {validated['max_leverage']}, 已限制为20x")
            validated['max_leverage'] = 20.0
        elif validated['max_leverage'] < 1:
            validated['max_leverage'] = 1.0
        
        # 仓位比例限制
        if validated['max_position_ratio'] > 0.5:  # 最大50%
            self.logger.warning(f"仓位比例过高: {validated['max_position_ratio']:.1%}, 已限制为50%")
            validated['max_position_ratio'] = 0.5
        elif validated['max_position_ratio'] < 0.01:  # 最小1%
            validated['max_position_ratio'] = 0.01
        
        # 最小余额合理性检查
        if validated['min_balance_usdt'] > 1000:
            self.logger.warning(f"最小余额要求过高: {validated['min_balance_usdt']} USDT, 已调整为100 USDT")
            validated['min_balance_usdt'] = 100.0
        elif validated['min_balance_usdt'] < 1:
            validated['min_balance_usdt'] = 1.0
        
        return validated
    
    # 🔄 新版接口（主要入口）
    def check_trade_risk(self, symbol: str, decision: Dict[str, Any],
                        positions: Dict[str, Any], balance: Dict[str, Any]) -> Dict[str, Any]:
        """
        检查交易风险（新版主入口）
        
        Args:
            symbol: 交易对符号
            decision: AI决策信息
            positions: 当前持仓信息
            balance: 账户余额信息
            
        Returns:
            风险检查结果字典
        """
        return self._comprehensive_risk_check(symbol, decision, positions, balance)
    
    # 🔄 兼容接口（向后兼容）
    def check_pre_trade_risk(self, symbol: str, decision: Dict[str, Any],
                           positions: Dict[str, Any], balance: Dict[str, Any]) -> Dict[str, Any]:
        """
        交易前风险检查（兼容旧版接口）
        
        Args:
            symbol: 交易对符号
            decision: 决策信息
            positions: 持仓信息
            balance: 余额信息
            
        Returns:
            风险检查结果
        """
        self.logger.debug(f"🔄 调用兼容接口 check_pre_trade_risk: {symbol}")
        return self._comprehensive_risk_check(symbol, decision, positions, balance)
    
    def _comprehensive_risk_check(self, symbol: str, decision: Dict[str, Any],
                                positions: Dict[str, Any], balance: Dict[str, Any]) -> Dict[str, Any]:
        """
        综合风险检查（核心实现）
        """
        try:
            self.logger.info(f"🛡️ 开始风险检查: {symbol}")
            self.logger.debug(f"📊 输入参数 - 决策: {decision}")
            self.logger.debug(f"💰 输入参数 - 余额: {balance}")
            self.logger.debug(f"📈 输入参数 - 持仓: {positions}")
            
            # 初始化结果
            result = {
                'passed': True,
                'allowed_size': decision.get('position_size', 0.1),
                'reason': '风险检查通过',
                'details': {},
                'errors': []
            }
            
            # 1️⃣ 资金充足性检查
            balance_check = self._check_balance_sufficiency(balance, result['allowed_size'])
            result['details']['balance_check'] = balance_check
            
            if not balance_check['passed']:
                result.update({
                    'passed': False,
                    'allowed_size': balance_check['allowed_size'],
                    'reason': balance_check['reason'],
                    'errors': [balance_check['reason']]
                })
                self.logger.warning(f"❌ 资金检查失败: {balance_check['reason']}")
                return result
            
            # 2️⃣ 仓位风险检查
            position_check = self._check_position_risk(symbol, decision, positions, balance)
            result['details']['position_check'] = position_check
            
            if not position_check['passed']:
                result.update({
                    'passed': False,
                    'allowed_size': position_check['allowed_size'],
                    'reason': position_check['reason'],
                    'errors': result['errors'] + [position_check['reason']]
                })
                self.logger.warning(f"❌ 仓位检查失败: {position_check['reason']}")
                return result
            else:
                # 🔧 关键修复：当仓位检查通过时，使用调整后的仓位大小
                result['allowed_size'] = position_check['allowed_size']
                if position_check['allowed_size'] != decision.get('position_size', 0.1):
                    result['reason'] = f"仓位已调整: {decision.get('position_size', 0.1):.3f} -> {position_check['allowed_size']:.3f}"
            
            # 3️⃣ 并发持仓数量检查
            concurrent_check = self._check_concurrent_positions(positions)
            result['details']['concurrent_check'] = concurrent_check
            
            if not concurrent_check['passed']:
                result.update({
                    'passed': False,
                    'allowed_size': 0,
                    'reason': concurrent_check['reason'],
                    'errors': result['errors'] + [concurrent_check['reason']]
                })
                self.logger.warning(f"❌ 并发检查失败: {concurrent_check['reason']}")
                return result
            
            # ✅ 所有检查通过
            self.logger.info(f"✅ 风险检查通过: {symbol}, 允许仓位: {result['allowed_size']}")
            return result
            
        except Exception as e:
            self.logger.error(f"💥 风险检查异常: {symbol} - {e}")
            import traceback
            self.logger.error(f"详细错误: {traceback.format_exc()}")
            
            return {
                'passed': False,
                'allowed_size': 0,
                'reason': f'风险检查系统异常: {str(e)}',
                'details': {'exception': str(e)},
                'errors': [f'系统异常: {str(e)}']
            }
    
    def _check_balance_sufficiency(self, balance: Dict[str, Any], position_size: float) -> Dict[str, Any]:
        """
        资金充足性检查（解决allowed_size=0的核心逻辑）
        """
        try:
            # 获取USDT余额信息
            usdt_balance = balance.get('USDT', {})
            available = float(usdt_balance.get('available', 0))
            total = float(usdt_balance.get('total', 0))
            
            self.logger.debug(f"💰 余额信息: 可用={available} USDT, 总计={total} USDT")
            
            # 获取最小余额要求
            min_balance = self.risk_params['min_balance_usdt']
            safety_margin = self.risk_params['safety_margin_ratio']
            
            self.logger.debug(f"🔧 风控参数: 最小余额={min_balance} USDT, 安全边际={safety_margin:.1%}")
            
            # 🎯 关键检查1: 最小余额要求（更宽松的条件）
            if available < min_balance:
                self.logger.warning(f"⚠️ 账户余额不足最小要求: {available} < {min_balance} USDT")
                return {
                    'passed': False,
                    'reason': f'账户余额不足: {available:.2f} USDT < 最小要求 {min_balance:.2f} USDT',
                    'allowed_size': 0,
                    'details': {
                        'available': available,
                        'required': min_balance,
                        'deficit': min_balance - available
                    }
                }
            
            # 🎯 关键检查2: 仓位资金需求（考虑安全边际）
            if total <= 0:
                self.logger.warning("⚠️ 总资金为0，无法开仓")
                return {
                    'passed': False,
                    'reason': '总资金为0，无法开仓',
                    'allowed_size': 0,
                    'details': {'total_balance': total}
                }
            
            # 计算所需资金（包含安全边际）
            required_for_position = position_size * total * (1 + safety_margin)
            
            self.logger.debug(f"📊 资金需求分析: 仓位大小={position_size:.3f}, 所需资金={required_for_position:.2f} USDT")
            
            if available < required_for_position:
                # 🔧 智能调整：计算实际可用的仓位大小
                max_affordable_size = (available * 0.95) / total  # 预留5%缓冲
                
                if max_affordable_size >= 0.001:  # 最小可交易仓位
                    self.logger.info(f"🔧 自动调整仓位: {position_size:.3f} -> {max_affordable_size:.3f}")
                    return {
                        'passed': True,
                        'reason': f'资金不足，自动调整仓位大小',
                        'allowed_size': max_affordable_size,
                        'details': {
                            'original_size': position_size,
                            'adjusted_size': max_affordable_size,
                            'available': available,
                            'required': required_for_position
                        }
                    }
                else:
                    return {
                        'passed': False,
                        'reason': f'资金不足: 需要{required_for_position:.2f} USDT, 可用{available:.2f} USDT',
                        'allowed_size': 0,
                        'details': {
                            'available': available,
                            'required': required_for_position,
                            'deficit': required_for_position - available
                        }
                    }
            
            # ✅ 资金检查通过
            return {
                'passed': True,
                'reason': '资金充足',
                'allowed_size': position_size,
                'details': {
                    'available': available,
                    'required': required_for_position,
                    'surplus': available - required_for_position
                }
            }
            
        except Exception as e:
            self.logger.error(f"💥 资金检查异常: {e}")
            return {
                'passed': False,
                'reason': f'资金检查失败: {str(e)}',
                'allowed_size': 0,
                'details': {'exception': str(e)}
            }
    
    def _check_position_risk(self, symbol: str, decision: Dict[str, Any],
                           positions: Dict[str, Any], balance: Dict[str, Any]) -> Dict[str, Any]:
        """仓位风险检查 - 修复版：每个交易对单独检查，不是总和限制"""
        try:
            position_size = decision.get('position_size', 0.1)
            max_ratio = self.risk_params['max_position_ratio']
            
            # 获取账户总资金
            usdt_balance = balance.get('USDT', {})
            total_balance = float(usdt_balance.get('total', 1))
            
            # 🔧 修复逻辑：检查当前交易对的仓位，而不是所有仓位的总和
            current_symbol_position = positions.get(symbol, {})
            current_symbol_size = 0
            
            if current_symbol_position and current_symbol_position.get('size', 0) != 0:
                # 获取当前交易对的持仓大小（转换为USDT价值）
                size = abs(float(current_symbol_position.get('size', 0)))
                mark_price = float(current_symbol_position.get('mark_price', 0))
                current_symbol_value = size * mark_price
                current_symbol_size = current_symbol_value / total_balance if total_balance > 0 else 0
            
            # 计算新增后该交易对的总仓位比例
            new_symbol_ratio = current_symbol_size + position_size
            
            self.logger.debug(f"📊 仓位分析: {symbol} 当前比例={current_symbol_size:.2%}, 新增比例={position_size:.2%}, 新增后比例={new_symbol_ratio:.2%}, 最大允许={max_ratio:.2%}")
            
            # 检查该交易对是否超过最大仓位比例
            if new_symbol_ratio > max_ratio:
                # 计算该交易对允许的最大仓位大小
                remaining_quota = max(0, max_ratio - current_symbol_size)
                
                if remaining_quota >= 0.001:  # 还有足够的仓位空间
                    self.logger.info(f"🔧 {symbol} 仓位调整: 原始={position_size:.3f} -> 调整后={remaining_quota:.3f}")
                    return {
                        'passed': True,
                        'reason': f'仓位调整: 原始={position_size:.3f}, 调整后={remaining_quota:.3f}',
                        'allowed_size': remaining_quota,
                        'details': {
                            'symbol': symbol,
                            'current_ratio': current_symbol_size,
                            'requested_ratio': position_size,
                            'adjusted_ratio': remaining_quota,
                            'new_total_ratio': current_symbol_size + remaining_quota,
                            'max_ratio': max_ratio
                        }
                    }
                else:
                    # 🔧 修复边界情况：当剩余配额为0时，调整为0并返回通过状态
                    self.logger.info(f"🔧 {symbol} 仓位调整: 原始={position_size:.3f} -> 调整后=0.000 (已达最大限制)")
                    return {
                        'passed': True,
                        'reason': f'仓位调整: 原始={position_size:.3f}, 调整后=0.000 (已达最大限制)',
                        'allowed_size': 0,
                        'details': {
                            'symbol': symbol,
                            'current_ratio': current_symbol_size,
                            'requested_ratio': position_size,
                            'adjusted_ratio': 0,
                            'new_total_ratio': current_symbol_size,
                            'max_ratio': max_ratio,
                            'quota_used': current_symbol_size / max_ratio if max_ratio > 0 else 1
                        }
                    }
            
            # ✅ 仓位检查通过
            return {
                'passed': True,
                'reason': f'{symbol} 仓位风险检查通过',
                'allowed_size': position_size,
                'details': {
                    'symbol': symbol,
                    'current_ratio': current_symbol_size,
                    'new_ratio': new_symbol_ratio,
                    'max_ratio': max_ratio,
                    'risk_level': 'LOW' if new_symbol_ratio < max_ratio * 0.5 else 'MODERATE'
                }
            }
            
        except Exception as e:
            self.logger.error(f"💥 仓位检查异常: {e}")
            return {
                'passed': False,
                'reason': f'仓位检查失败: {str(e)}',
                'allowed_size': 0,
                'details': {'exception': str(e)}
            }
    
    def _check_concurrent_positions(self, positions: Dict[str, Any]) -> Dict[str, Any]:
        """并发持仓数量检查"""
        try:
            max_concurrent = self.risk_params['max_concurrent_positions']
            
            # 计算当前活跃持仓数量
            active_positions = sum(
                1 for pos_data in positions.values()
                if pos_data and pos_data.get('size', 0) != 0
            )
            
            self.logger.debug(f"📈 持仓统计: 当前活跃={active_positions}, 最大允许={max_concurrent}")
            
            if active_positions >= max_concurrent:
                return {
                    'passed': False,
                    'reason': f'并发持仓数量超限: {active_positions} >= {max_concurrent}',
                    'details': {
                        'active_positions': active_positions,
                        'max_concurrent': max_concurrent,
                        'active_symbols': [
                            symbol for symbol, pos_data in positions.items()
                            if pos_data and pos_data.get('size', 0) != 0
                        ]
                    }
                }
            
            return {
                'passed': True,
                'reason': '并发持仓检查通过',
                'details': {
                    'active_positions': active_positions,
                    'max_concurrent': max_concurrent,
                    'available_slots': max_concurrent - active_positions
                }
            }
            
        except Exception as e:
            self.logger.error(f"💥 并发检查异常: {e}")
            return {
                'passed': False,
                'reason': f'并发检查失败: {str(e)}',
                'details': {'exception': str(e)}
            }
    
    # 🔄 兼容方法（为了保持向后兼容性）
    def get_risk_summary(self) -> Dict[str, Any]:
        """获取风险摘要"""
        return {
            'risk_params': self.risk_params,
            'status': 'ACTIVE',
            'last_check': datetime.now().isoformat(),
            'version': '3.0.0',
            'features': [
                'Smart balance checking (min 10 USDT)',
                'Automatic position sizing',
                'Concurrent position limits',
                'Enhanced error handling',
                'Backward compatibility'
            ]
        }
    
    def update_risk_params(self, new_params: Dict[str, Any]) -> bool:
        """动态更新风险参数"""
        try:
            self.logger.info(f"🔧 更新风险参数: {new_params}")
            
            # 验证新参数
            validated_params = self._validate_risk_params({**self.risk_params, **new_params})
            
            # 更新参数
            self.risk_params.update(validated_params)
            
            self.logger.info(f"✅ 风险参数更新成功: {self.risk_params}")
            return True
            
        except Exception as e:
            self.logger.error(f"💥 更新风险参数失败: {e}")
            return False
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"RiskManager(min_balance={self.risk_params['min_balance_usdt']}USDT, max_ratio={self.risk_params['max_position_ratio']:.1%})"
    
    def __repr__(self) -> str:
        """详细表示"""
    
    def _calculate_required_margin(self, symbol: str, position_size: float, leverage: int = 3) -> float:
        """
        计算所需保证金
        
        Args:
            symbol: 交易对
            position_size: 仓位大小  
            leverage: 杠杆倍数
            
        Returns:
            float: 所需保证金（USDT）
        """
        try:
            # 假设BTC价格为22000 USDT（实际应该从市场数据获取）
            estimated_price = 22000.0
            
            # 计算名义价值
            notional_value = position_size * estimated_price
            
            # 计算所需保证金 = 名义价值 / 杠杆
            required_margin = notional_value / leverage
            
            self.logger.debug(f"💰 保证金计算: 仓位={position_size}, 价格≈{estimated_price}, 杠杆={leverage}x, 保证金={required_margin:.2f} USDT")
            
            return required_margin
            
        except Exception as e:
            self.logger.error(f"计算保证金失败: {symbol} - {e}")
            # 返回保守估计
            return position_size * 22000 / 3  # 默认3倍杠杆
        return f"RiskManager(params={self.risk_params})"