"""
配置管理模块 - Configuration Manager
负责管理所有系统配置，包括API密钥、交易参数等
使用SQLite3数据库存储配置信息
不再依赖配置文件，完全使用数据库存储
"""

import sqlite3
import json
import os
from typing import Dict, Any, Optional
import logging

class ConfigManager:
    """配置管理器 - 管理系统所有配置参数"""
    
    def __init__(self, db_path: str = "credentials.db"):
        """
        初始化配置管理器
        
        Args:
            db_path: SQLite数据库文件路径
        """
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)

        # 初始化数据库
        self._init_database()

        # 加载配置
        self.config = self._load_config()
    
    def _init_database(self) -> None:
        """初始化SQLite数据库和表结构"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 创建credentials表 - 存储API密钥
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS credentials (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        service_name TEXT UNIQUE NOT NULL,
                        api_key TEXT NOT NULL,
                        api_secret TEXT,
                        passphrase TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # 创建settings表 - 存储系统设置
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS settings (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        category TEXT NOT NULL,
                        key TEXT NOT NULL,
                        value TEXT NOT NULL,
                        description TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(category, key)
                    )
                ''')
                
                # 插入AI配置默认值（如果不存在）
                ai_defaults = [
                    ('ai_settings', 'timeout', '30', 'AI调用超时时间（秒）'),
                    ('ai_settings', 'retries', '5', 'AI调用重试次数'),
                    ('ai_settings', 'temperature', '0.1', 'AI模型温度参数'),
                    ('ai_settings', 'max_tokens', '500', 'AI响应最大token数')
                ]

                # 风险参数默认值
                risk_defaults = [
                    ('risk_management', 'max_total_exposure', '0.8', '最大总仓位比例'),
                    ('risk_management', 'max_single_position', '0.3', '单个仓位最大比例'),
                    ('risk_management', 'max_drawdown', '0.15', '最大回撤比例'),
                    ('risk_management', 'max_daily_loss', '0.05', '单日最大亏损比例'),
                    ('risk_management', 'max_leverage', '5.0', '最大杠杆倍数'),
                    ('risk_management', 'stop_loss_pct', '0.02', '默认止损比例'),
                    ('risk_management', 'take_profit_pct', '0.04', '默认止盈比例'),
                    ('risk_management', 'risk_free_rate', '0.02', '无风险利率'),
                    ('risk_management', 'volatility_threshold', '0.05', '波动率阈值'),
                    ('risk_management', 'correlation_threshold', '0.7', '相关性阈值'),
                    ('risk_management', 'var_confidence', '0.95', 'VaR置信度'),
                    ('risk_management', 'emergency_stop_loss', '0.20', '紧急止损比例')
                ]

                # 初始化所有默认值
                for defaults in [ai_defaults, risk_defaults]:
                    for category, key, value, description in defaults:
                        cursor.execute('''
                            INSERT OR IGNORE INTO settings (category, key, value, description, updated_at)
                            VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
                        ''', (category, key, value, description))

                conn.commit()
                self.logger.info("数据库初始化完成")
                
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")
            raise
    
    def _load_config(self) -> Dict[str, Any]:
        """从数据库加载配置"""
        config = {}

        try:
            # 从数据库加载配置
            config.update(self._load_credentials())
            config.update(self._load_settings())

            # 只有当settings中没有trading_pairs时，才从trading_pairs表加载
            if 'trading_pairs' not in config or not config['trading_pairs']:
                config.update(self._load_trading_pairs())

            # 验证配置完整性（仅在有凭证时验证）
            if config.get('credentials'):
                self._validate_config(config)

            self.logger.info("配置加载完成")
            return config

        except Exception as e:
            self.logger.error(f"配置加载失败: {e}")
            raise
    
    def _load_credentials(self) -> Dict[str, Any]:
        """从数据库加载API凭证"""
        credentials = {}
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT service_name, api_key, api_secret, passphrase FROM credentials")
                
                for row in cursor.fetchall():
                    service_name, api_key, api_secret, passphrase = row
                    credentials[service_name] = {
                        'api_key': api_key,
                        'api_secret': api_secret,
                        'passphrase': passphrase
                    }
                    
        except Exception as e:
            self.logger.warning(f"从数据库加载凭证失败: {e}")
            
        return {'credentials': credentials}
    
    def _load_settings(self) -> Dict[str, Any]:
        """从数据库加载系统设置"""
        settings = {}
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT category, key, value FROM settings")
                
                for row in cursor.fetchall():
                    category, key, value = row
                    if category not in settings:
                        settings[category] = {}
                    
                    # 尝试解析JSON值
                    try:
                        settings[category][key] = json.loads(value)
                    except json.JSONDecodeError:
                        settings[category][key] = value
                        
        except Exception as e:
            self.logger.warning(f"从数据库加载设置失败: {e}")
            
        return settings
    
    def _load_trading_pairs(self) -> Dict[str, Any]:
        """加载全局交易配置"""
        return {'trading_pairs': {}}
    

    def _validate_config(self, config: Dict[str, Any]) -> None:
        """验证配置完整性"""
        required_credentials = ['okx', 'deepseek']
        
        if 'credentials' not in config:
            raise ValueError("缺少API凭证配置")
            
        for service in required_credentials:
            if service not in config['credentials']:
                raise ValueError(f"缺少{service}服务的API凭证")
                
            cred = config['credentials'][service]
            if service == 'okx':
                required_keys = ['api_key', 'api_secret', 'passphrase']
                for key in required_keys:
                    if not cred.get(key):
                        raise ValueError(f"OKX {key} 不能为空")
                        
            elif service == 'deepseek':
                if not cred.get('api_key'):
                    raise ValueError("DeepSeek API Key 不能为空")
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
                
        return value
    
    def set_setting(self, category: str, key: str, value: Any, description: str = "") -> None:
        """设置配置值并保存到数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 将值转换为JSON字符串
                value_str = json.dumps(value) if not isinstance(value, str) else value
                
                cursor.execute('''
                    INSERT OR REPLACE INTO settings (category, key, value, description, updated_at)
                    VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
                ''', (category, key, value_str, description))
                
                conn.commit()
                
                # 更新内存中的配置
                if category not in self.config:
                    self.config[category] = {}
                self.config[category][key] = value
                
                self.logger.info(f"设置已更新: {category}.{key} = {value}")
                
        except Exception as e:
            self.logger.error(f"设置配置失败: {e}")
            raise
    
    def add_trading_pair(self, symbol: str, **kwargs) -> None:
        """保留方法兼容性(不再支持单独配置)"""
        self.logger.warning(f"交易对单独配置已禁用，使用全局配置替代")
    
    def get_active_trading_pairs(self) -> Dict[str, Dict[str, Any]]:
        """获取所有活跃交易对(使用全局配置)"""
        trading_pairs = self.get('trading_pairs', {})
        return {
            symbol: {
                'is_active': True,
                'leverage': trading_pairs.get('max_leverage', 3),
                'max_position_size': trading_pairs.get('base_amount', 30),
                'stop_loss_pct': trading_pairs.get('stop_loss', 2.0),
                'take_profit_pct': trading_pairs.get('take_profit', 4.0)
            }
            for symbol in trading_pairs.get('active_pairs', [])
        }
    
    def reload_config(self) -> None:
        """重新加载配置"""
        self.config = self._load_config()
        self.logger.info("配置已重新加载")

    def get_config(self) -> Dict[str, Any]:
        """获取完整配置"""
        return self.config.copy()

    def get_credentials(self) -> Dict[str, Any]:
        """获取所有凭证"""
        return self.config.get('credentials', {})

    def get_settings(self) -> Dict[str, Any]:
        """获取所有设置"""
        # 从config中提取非credentials和trading_pairs的部分
        settings = {}
        for key, value in self.config.items():
            if key not in ['credentials', 'trading_pairs']:
                settings[key] = value
        return settings

    def get_trading_pairs(self) -> Dict[str, Any]:
        """获取交易对配置"""
        return self.config.get('trading_pairs', {})

    def get_setting(self, category: str, key: str, default: Any = None) -> Any:
        """获取特定设置"""
        return self.get(f"{category}.{key}", default)

    def delete_setting(self, category: str, key: str) -> None:
        """删除设置"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "DELETE FROM settings WHERE category = ? AND key = ?",
                    (category, key)
                )
                conn.commit()

                # 重新加载配置
                self.reload_config()
                self.logger.info(f"设置已删除: {category}.{key}")

        except Exception as e:
            self.logger.error(f"删除设置失败: {e}")
            raise

    def calculate_confidence_based_parameters(self, confidence: float, symbol: str, user_max_position_ratio: float = None) -> dict:
        """根据置信度和数据库配置计算动态交易参数

        Args:
            confidence: AI置信度 (0.0-1.0)
            symbol: 交易对符号
            user_max_position_ratio: 用户设置的最大投资比例 (%)

        Returns:
            dict: 包含杠杆、仓位、止盈止损的参数字典
        """
        try:
            # 获取该交易对的数据库配置上限
            trading_pairs = self.get_trading_pairs()
            pair_config = trading_pairs.get(symbol, {})

            # 优先使用全局数据库配置，然后是交易对特定配置，最后是风险管理配置
            global_config = self.get('trading_pairs', {})
            risk_config = self.get('risk_management', {})
            
            # 获取最大杠杆（优先级：交易对配置 > 全局配置 > 风险管理配置 > 默认值）
            max_leverage = pair_config.get('leverage') or \
                          global_config.get('max_leverage') or \
                          risk_config.get('max_leverage', 5.0)
            
            # 确保杠杆值是数字类型
            try:
                max_leverage = float(max_leverage)
            except (ValueError, TypeError):
                self.logger.warning(f"杠杆配置类型错误: {max_leverage}，使用默认值5.0")
                max_leverage = 5.0
            
            # 获取其他配置上限
            max_position_size = pair_config.get('max_position_size', 100.0)
            max_stop_loss = pair_config.get('stop_loss_pct') or risk_config.get('stop_loss_pct', 5.0)
            max_take_profit = pair_config.get('take_profit_pct') or risk_config.get('take_profit_pct', 20.0)

            # 如果用户设置了最大投资比例，使用更严格的限制
            if user_max_position_ratio is not None:
                max_position_size = min(max_position_size, user_max_position_ratio)
                self.logger.info(f"应用用户投资比例限制: {user_max_position_ratio}%")

            # 置信度类型和安全检查
            try:
                confidence = float(confidence)
            except (ValueError, TypeError):
                self.logger.warning(f"置信度类型转换失败: {confidence}, 使用默认值0.5")
                confidence = 0.5
            confidence = max(0.0, min(1.0, confidence))

            # 如果置信度太低，返回保守参数或不交易
            if confidence < 0.4:
                return {
                    'should_trade': False,
                    'leverage': 1,
                    'position_ratio': 0.5,
                    'stop_loss': max_stop_loss,
                    'take_profit': max_take_profit * 0.3,
                    'reason': f'置信度过低({confidence:.2f}), 建议不交易'
                }

            # 基于置信度计算参数（在数据库限制范围内）
            leverage = max(1, min(confidence * max_leverage, max_leverage))

            # 仓位使用指数映射，让高置信度时仓位增长更明显
            position_ratio = min(pow(confidence, 1.5) * max_position_size, max_position_size)

            # 止损：置信度高时止损小，置信度低时止损大
            stop_loss_multiplier = max(0.2, min(1.2, 1.4 - confidence))
            stop_loss = min(max_stop_loss * stop_loss_multiplier, max_stop_loss)

            # 止盈：置信度越高止盈越大
            take_profit = max(max_take_profit * 0.3, max_take_profit * confidence)

            result = {
                'should_trade': True,
                'leverage': round(leverage, 1),
                'position_ratio': round(position_ratio, 2),
                'stop_loss': round(stop_loss, 2),
                'take_profit': round(take_profit, 2),
                'confidence': confidence,
                'max_limits': {
                    'max_leverage': max_leverage,
                    'max_position_size': max_position_size,
                    'max_stop_loss': max_stop_loss,
                    'max_take_profit': max_take_profit
                }
            }

            self.logger.info(f"置信度参数计算: {symbol}")
            self.logger.info(f"  置信度: {confidence:.2f}")
            self.logger.info(f"  杠杆: {result['leverage']}x (上限: {max_leverage}x)")
            self.logger.info(f"  仓位: {result['position_ratio']}% (上限: {max_position_size}%)")
            self.logger.info(f"  止损: {result['stop_loss']}% (上限: {max_stop_loss}%)")
            self.logger.info(f"  止盈: {result['take_profit']}% (上限: {max_take_profit}%)")

            return result

        except Exception as e:
            self.logger.error(f"计算置信度参数失败: {e}")
            # 返回保守的默认参数
            return {
                'should_trade': False,
                'leverage': 1,
                'position_ratio': 1.0,
                'stop_loss': 5.0,
                'take_profit': 8.0,
                'reason': f'计算失败: {str(e)}'
            }
