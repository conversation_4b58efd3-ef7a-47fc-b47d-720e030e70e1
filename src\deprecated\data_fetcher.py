"""
数据获取模块 - Data Fetcher
负责从OKX交易所获取K线数据、账户信息、持仓信息等
基于python-okx库实现
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
import logging
import time
import asyncio
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime, timedelta
import threading
from okx.MarketData import MarketAPI
from okx.Account import AccountAPI
from okx.PublicData import PublicAPI
from ..config_manager import ConfigManager

class DataFetcher:
    """数据获取器 - 从OKX获取市场数据和账户信息"""
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化数据获取器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)
        
        # 初始化OKX客户端（如果有配置）
        self.market_api = None
        self.account_api = None
        self.public_api = None
        try:
            self._init_okx_client()
        except Exception as e:
            self.logger.warning(f"OKX客户端初始化失败，将在配置完成后重新初始化: {e}")
        
        # 异步执行器 - 用于并行数据获取
        self.executor = ThreadPoolExecutor(max_workers=10, thread_name_prefix="DataFetcher")

        # 数据缓存
        self.kline_cache = {}
        self.account_cache = {}
        self.position_cache = {}

        # 缓存锁
        self.cache_lock = threading.Lock()

        # 最后更新时间
        self.last_update_time = {}
    
    def _init_okx_client(self) -> None:
        """初始化OKX客户端 - 增强连接稳定性和备用连接策略"""
        try:
            okx_config = self.config_manager.get('credentials.okx')
            if not okx_config:
                self.logger.info("OKX API配置不存在，跳过客户端初始化")
                return

            # 优化连接配置 - 移除HTTP/1.1强制设置，使用库默认配置
            import os
            
            # 移除可能影响连接的环境变量设置
            if 'HTTPX_HTTP2' in os.environ:
                del os.environ['HTTPX_HTTP2']

            # OKX备用域名列表 - 提供连接冗余
            backup_domains = [
                'https://www.okx.com',      # 主域名
                'https://aws.okx.com',      # AWS域名
                'https://okx.com',          # 备用域名
                'https://www.okex.com',     # 旧域名（备用）
            ]

            # 尝试使用不同域名初始化客户端
            success = False
            last_error = None
            
            for domain in backup_domains:
                try:
                    self.logger.info(f"🔄 尝试连接OKX域名: {domain}")
                    
                    base_config = {
                        'api_key': okx_config['api_key'],
                        'api_secret_key': okx_config['api_secret'],
                        'passphrase': okx_config['passphrase'],
                        'flag': '1',  # 1: 模拟环境, 0: 实盘环境
                        'domain': domain,
                        'use_server_time': True  # 使用服务器时间
                    }

                    # 创建API实例
                    test_market_api = MarketAPI(**base_config)
                    test_account_api = AccountAPI(**base_config)
                    test_public_api = PublicAPI(**base_config)

                    # 快速健康检查
                    test_response = test_public_api.get_system_time()
                    if test_response and test_response.get('code') == '0':
                        # 连接成功，保存API实例
                        self.market_api = test_market_api
                        self.account_api = test_account_api
                        self.public_api = test_public_api
                        self.current_domain = domain
                        success = True
                        self.logger.info(f"🟢 成功连接OKX域名: {domain}")
                        break
                    else:
                        raise Exception(f"域名 {domain} 健康检查失败")
                        
                except Exception as e:
                    last_error = e
                    self.logger.warning(f"🔴 域名 {domain} 连接失败: {e}")
                    continue

            if not success:
                self.logger.error(f"❌ 所有OKX域名连接失败，最后错误: {last_error}")
                self.market_api = None
                self.account_api = None
                self.public_api = None
                self.current_domain = None
                return

            # 设置请求超时和连接池（如果API支持）
            self._configure_api_timeouts()

            # 进行完整的连接健康检查
            if self._test_connection_health():
                self.logger.info(f"🟢 OKX客户端初始化成功 - 使用域名: {self.current_domain}")
            else:
                self.logger.warning(f"🟡 OKX客户端初始化成功但连接不稳定 - 域名: {self.current_domain}")
            
        except Exception as e:
            self.logger.error(f"OKX客户端初始化失败: {e}")
            # 不再直接抛出异常，允许系统继续运行
            self.market_api = None
            self.account_api = None
            self.public_api = None
            self.current_domain = None

    def _configure_api_timeouts(self) -> None:
        """配置API超时设置和连接池优化"""
        try:
            # 优化的超时配置 - 针对OKX API特点调整
            timeout_config = {
                'connect_timeout': 15,   # 连接超时15秒（增加）
                'read_timeout': 45,      # 读取超时45秒（增加）
                'pool_timeout': 10,      # 连接池超时10秒（增加）
                'pool_connections': 20,  # 连接池大小
                'pool_maxsize': 30,      # 最大连接数
                'pool_block': False      # 非阻塞连接池
            }
            
            # 为每个API实例配置超时和连接池
            for api_name, api in [('market', self.market_api), ('account', self.account_api), ('public', self.public_api)]:
                if api:
                    try:
                        # 尝试配置httpx客户端（如果API使用httpx）
                        if hasattr(api, '_session') or hasattr(api, '_client'):
                            session = getattr(api, '_session', None) or getattr(api, '_client', None)
                            if session and hasattr(session, 'timeout'):
                                session.timeout = 45  # 全局超时
                                self.logger.debug(f"🔧 {api_name} API会话超时已设置: 45秒")
                        
                        # 尝试设置请求参数
                        if hasattr(api, '_request_params'):
                            api._request_params = getattr(api, '_request_params', {})
                            api._request_params.update({
                                'timeout': 45,
                                'retries': 5,  # 增加重试次数
                                'backoff_factor': 0.5,  # 退避因子
                                'pool_connections': 20,
                                'pool_maxsize': 30
                            })
                            self.logger.debug(f"🔧 {api_name} API请求参数已优化")
                        
                        # 尝试设置连接池参数（如果支持）
                        if hasattr(api, '_adapter_config'):
                            api._adapter_config = {
                                'pool_connections': 20,
                                'pool_maxsize': 30,
                                'pool_block': False,
                                'max_retries': 5
                            }
                            self.logger.debug(f"🔧 {api_name} API连接池已配置")
                            
                    except Exception as api_e:
                        self.logger.debug(f"配置{api_name} API失败（非关键）: {api_e}")
                        
            # 设置全局请求优化环境变量
            import os
            os.environ.update({
                'PYTHONHTTPSVERIFY': '1',  # 启用SSL验证
                'CURL_CA_BUNDLE': '',      # 使用系统证书
                'REQUESTS_CA_BUNDLE': '',  # 使用系统证书
            })
            
            self.logger.info("🔧 API超时和连接池配置完成")
            
        except Exception as e:
            self.logger.warning(f"API超时配置失败（部分功能可能受影响）: {e}")

    def _test_connection_health(self) -> bool:
        """测试连接健康状态"""
        try:
            # 测试公共API（不需要认证，最快）
            response = self.public_api.get_system_time()
            if response and response.get('code') == '0':
                self.logger.info("🟢 OKX连接健康检查通过")
                return True
            else:
                self.logger.warning("🟡 OKX连接健康检查异常")
                return False
        except Exception as e:
            self.logger.warning(f"🔴 OKX连接健康检查失败: {e}")
            return False

    def _smart_retry_request(self, api_func, *args, max_retries=5, **kwargs):
        """增强的智能重试机制 - 专门针对连接断开问题优化"""
        last_exception = None
        
        for attempt in range(max_retries):
            try:
                result = api_func(*args, **kwargs)
                if result and result.get('code') == '0':
                    # 成功后记录恢复日志
                    if attempt > 0:
                        self.logger.info(f"🟢 API请求在第{attempt+1}次尝试后成功恢复")
                    return result
                else:
                    error_msg = result.get('msg', '未知API错误') if result else '空响应'
                    raise Exception(f"API返回错误: {error_msg}")

            except Exception as e:
                last_exception = e
                error_msg = str(e).lower()

                # 增强的网络错误检测 - 专门识别OKX常见连接问题
                network_error_keywords = [
                    'timeout', 'connection', 'handshake', 'ssl', 'network',
                    'unreachable', 'disconnected', 'reset', 'refused',
                    'server disconnected', 'connection aborted', 'broken pipe',
                    'no route to host', 'network is unreachable'
                ]
                
                is_network_error = any(keyword in error_msg for keyword in network_error_keywords)
                is_rate_limit = 'rate limit' in error_msg or 'too many requests' in error_msg
                is_server_error = any(code in error_msg for code in ['50000', '50001', '50002', '50004'])

                if attempt < max_retries - 1:
                    if is_rate_limit:
                        # 速率限制：使用更长的等待时间
                        wait_time = min(30, (2 ** attempt) * 5)
                        self.logger.warning(f"🚫 API速率限制，等待{wait_time}秒后重试 ({attempt+1}/{max_retries})")
                        time.sleep(wait_time)
                        
                    elif is_network_error or is_server_error:
                        # 网络/服务器问题：指数退避 + 连接重置
                        base_wait = 2 if is_network_error else 1
                        wait_time = min(30, (base_wait ** attempt) + 0.5)
                        
                        self.logger.warning(f"🔴 连接问题检测到，{wait_time}秒后重试 ({attempt+1}/{max_retries}): {e}")
                        time.sleep(wait_time)

                        # 连接问题时更频繁地重新初始化连接
                        if attempt >= 1:  # 第2次重试开始重新初始化
                            self.logger.info("🔄 重新初始化OKX连接以修复连接问题...")
                            try:
                                self._reinitialize_connection()
                            except Exception as reinit_e:
                                self.logger.debug(f"连接重新初始化失败: {reinit_e}")
                                
                    else:
                        # 其他API错误：较短等待时间
                        wait_time = 0.5 + (attempt * 0.5)
                        self.logger.warning(f"⚠️ API错误，{wait_time}秒后重试 ({attempt+1}/{max_retries}): {e}")
                        time.sleep(wait_time)
                else:
                    # 最后一次重试失败 - 详细记录错误信息
                    self.logger.error(f"❌ API请求最终失败 (重试{max_retries}次): {last_exception}")
                    self.logger.error(f"错误详情: 函数={api_func.__name__ if hasattr(api_func, '__name__') else str(api_func)}")
                    raise last_exception

    def _reinitialize_connection(self) -> None:
        """重新初始化连接 - 针对连接断开问题的修复"""
        try:
            # 清除旧的API实例
            self.market_api = None
            self.account_api = None
            self.public_api = None
            
            # 短暂等待，让旧连接完全关闭
            time.sleep(1)
            
            # 重新初始化
            self._init_okx_client()
            
            # 验证新连接
            if self._test_connection_health():
                self.logger.info("🟢 连接重新初始化成功")
            else:
                self.logger.warning("🟡 连接重新初始化完成，但健康检查未通过")
                
        except Exception as e:
            self.logger.error(f"连接重新初始化失败: {e}")
            # 不抛出异常，允许继续尝试

    def _check_api_ready(self) -> bool:
        """检查API是否准备就绪"""
        if not self.market_api or not self.account_api or not self.public_api:
            self.logger.warning("OKX API客户端未初始化，请先配置API密钥")
            return False
        return True
    
    def get_candlesticks(self, 
                        symbol: str, 
                        timeframe: str, 
                        limit: int = 300,
                        use_cache: bool = True) -> pd.DataFrame:
        """
        获取K线数据
        
        Args:
            symbol: 交易对符号，如 'BTC-USDT-SWAP'
            timeframe: 时间周期，如 '1m', '5m', '15m', '1H', '4H', '1D'
            limit: 获取数据条数，最大300
            use_cache: 是否使用缓存
            
        Returns:
            包含OHLCV数据的DataFrame
        """
        cache_key = f"{symbol}_{timeframe}"

        try:
            # 检查API是否准备就绪
            if not self._check_api_ready():
                return pd.DataFrame()  # 返回空DataFrame

            # 检查缓存
            if use_cache and self._is_cache_valid(cache_key, timeframe):
                with self.cache_lock:
                    return self.kline_cache[cache_key].copy()

            # 使用增强的智能重试机制获取K线数据
            response = self._smart_retry_request(
                self.market_api.get_candlesticks,
                instId=symbol,
                bar=timeframe,
                limit=str(limit),
                max_retries=5  # 对K线数据使用更多重试次数
            )

            # 解析数据
            data = response['data']
            if not data:
                raise Exception(f"没有获取到{symbol}的K线数据")

            # 转换为DataFrame
            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'volCcy', 'volCcyQuote', 'confirm'
            ])

            # 数据类型转换 - 安全处理大整数时间戳
            def safe_timestamp_convert(ts):
                try:
                    # 直接使用字符串转换，避免C long限制
                    return pd.to_datetime(int(str(ts)), unit='ms')
                except (ValueError, OverflowError) as e:
                    self.logger.warning(f"时间戳转换失败: {ts} - {e}")
                    return pd.Timestamp.now()

            df['timestamp'] = df['timestamp'].apply(safe_timestamp_convert)
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

            # 按时间排序
            df = df.sort_values('timestamp').reset_index(drop=True)
        
            # 更新缓存
            with self.cache_lock:
                self.kline_cache[cache_key] = df.copy()
                self.last_update_time[cache_key] = datetime.now()

            self.logger.debug(f"获取K线数据成功: {symbol} {timeframe} {len(df)}条")
            return df

        except Exception as e:
            self.logger.error(f"获取K线数据最终失败: {symbol} {timeframe} - {e}")
            raise
    
    def get_multiple_timeframes(self, 
                               symbol: str, 
                               timeframes: List[str] = ['1m', '5m', '15m'],
                               limit: int = 300) -> Dict[str, pd.DataFrame]:
        """
        获取多个时间周期的K线数据
        
        Args:
            symbol: 交易对符号
            timeframes: 时间周期列表
            limit: 每个周期获取的数据条数
            
        Returns:
            时间周期到DataFrame的映射
        """
        result = {}
        
        for timeframe in timeframes:
            try:
                df = self.get_candlesticks(symbol, timeframe, limit)
                result[timeframe] = df
                
                # 避免API限制，添加延迟
                time.sleep(0.1)
                
            except Exception as e:
                self.logger.warning(f"获取{symbol} {timeframe}数据失败: {e}")
                continue
        
        return result
    
    def get_account_balance(self, currency: str = '') -> Dict[str, Any]:
        """
        获取账户余额

        Args:
            currency: 币种，为空则获取所有币种

        Returns:
            账户余额信息
        """
        try:
            # 检查API是否已初始化
            if not self.account_api:
                self.logger.debug("OKX API未配置，返回空余额信息")
                return {}

            response = self.account_api.get_account_balance(ccy=currency)
            
            if response['code'] != '0':
                raise Exception(f"OKX API错误: {response['msg']}")
            
            # 解析余额数据
            balance_data = {}
            for account in response['data']:
                for detail in account['details']:
                    ccy = detail['ccy']
                    balance_data[ccy] = {
                        'available': float(detail['availBal']),
                        'frozen': float(detail['frozenBal']),
                        'total': float(detail['eq']),  # 修复：使用eq字段
                        'equity': float(detail['cashBal'])
                    }
            
            # 更新缓存
            with self.cache_lock:
                self.account_cache['balance'] = balance_data
                self.last_update_time['balance'] = datetime.now()
            
            self.logger.debug(f"获取账户余额成功: {len(balance_data)}个币种")
            return balance_data
            
        except Exception as e:
            self.logger.error(f"获取账户余额失败: {e}")
            raise
    
    def get_positions(self, symbol: str = '') -> Dict[str, Any]:
        """
        获取持仓信息 - 智能重试机制

        Args:
            symbol: 交易对符号，为空则获取所有持仓

        Returns:
            持仓信息
        """
        try:
            # 检查API是否已初始化
            if not self.account_api:
                self.logger.debug("OKX API未配置，返回空持仓信息")
                return {}

            # 使用智能重试机制
            response = self._smart_retry_request(
                self.account_api.get_positions,
                instId=symbol
            )

            # 解析持仓数据
            positions = {}
            for pos in response['data']:
                try:
                    inst_id = pos['instId']

                    # 安全转换数值，处理空字符串
                    def safe_float(value, default=0):
                        try:
                            return float(value) if value and value != '' else default
                        except (ValueError, TypeError):
                            return default

                    # 正确处理持仓方向：多头为正数，空头为负数
                    pos_size = safe_float(pos.get('pos', 0))
                    pos_side = pos.get('posSide', '')

                        # 只处理有实际持仓的数据
                    if pos_size > 0:
                        # 根据持仓方向调整符号
                        if pos_side == 'short':
                            pos_size = -pos_size  # 空头持仓用负数表示
                        # long持仓保持正数

                        # 调试：打印原始持仓数据
                        self.logger.info(f"🔍 原始持仓数据 {inst_id}: {pos}")

                        # 使用OKX提供的保证金数据（优先使用imr字段）
                        api_imr = safe_float(pos.get('imr', 0))  # Initial Margin Requirement
                        api_margin = safe_float(pos.get('margin', 0))

                        if api_imr > 0:
                            # 优先使用OKX提供的初始保证金要求
                            margin = api_imr
                            self.logger.info(f"🔍 使用OKX保证金: {inst_id} = {margin:.2f} USDT (来源: imr)")
                        elif api_margin > 0:
                            # 其次使用margin字段
                            margin = api_margin
                            self.logger.info(f"🔍 使用OKX保证金: {inst_id} = {margin:.2f} USDT (来源: margin)")
                        else:
                            # 最后才手动计算（通常不会执行到这里）
                            mark_price = safe_float(pos.get('markPx', 0))
                            leverage = safe_float(pos.get('lever', 1))
                            if 'SWAP' in inst_id:
                                # SWAP合约：张数转换为BTC数量
                                btc_amount = pos_size * 0.01
                                position_value = btc_amount * mark_price
                            else:
                                position_value = pos_size * mark_price
                            calculated_margin = position_value / leverage if leverage > 0 else 0
                            self.logger.warning(f"🔍 手动计算保证金: {inst_id} = {calculated_margin:.2f} USDT (OKX数据缺失)")
                            margin = calculated_margin

                        positions[inst_id] = {
                            'symbol': inst_id,
                            'position_side': pos_side,
                            'size': pos_size,  # 多头为正，空头为负
                            'available_size': safe_float(pos.get('availPos', 0)),
                            'avg_price': safe_float(pos.get('avgPx', 0)),
                            'unrealized_pnl': safe_float(pos.get('upl', 0)),
                            'unrealized_pnl_ratio': safe_float(pos.get('uplRatio', 0)),
                            'margin': margin,
                            'leverage': safe_float(pos.get('lever', 1)),
                            'mark_price': safe_float(pos.get('markPx', 0)),
                            'liquidation_price': safe_float(pos.get('liqPx', 0)),
                            'update_time': self._safe_timestamp_to_datetime(pos.get('uTime')) if pos.get('uTime') else datetime.now()
                        }
                except Exception as e:
                    self.logger.warning(f"解析持仓数据失败: {inst_id} - {e}")
                    continue

            # 更新缓存
            with self.cache_lock:
                self.position_cache = positions
                self.last_update_time['positions'] = datetime.now()

            self.logger.debug(f"获取持仓信息成功: {len(positions)}个持仓")
            return positions

        except Exception as e:
            self.logger.error(f"获取持仓信息失败: {e}")
            raise
    
    def get_ticker(self, symbol: str) -> Dict[str, Any]:
        """
        获取单个交易对的行情数据

        Args:
            symbol: 交易对符号

        Returns:
            行情数据
        """
        try:
            # 检查API是否已初始化
            if not self.market_api:
                self.logger.debug("OKX API未配置，返回默认行情数据")
                return {
                    'symbol': symbol,
                    'last_price': 0,
                    'bid_price': 0,
                    'ask_price': 0,
                    'high_24h': 0,
                    'low_24h': 0,
                    'volume_24h': 0,
                    'change_24h': 0,
                    'open_24h': 0,
                    'timestamp': datetime.now()
                }

            response = self.market_api.get_ticker(instId=symbol)
            
            if response['code'] != '0':
                raise Exception(f"OKX API错误: {response['msg']}")
            
            data = response['data'][0]

            # 计算24小时变化百分比
            last_price = float(data['last'])
            open_24h = float(data['open24h'])
            change_24h = ((last_price - open_24h) / open_24h) * 100 if open_24h > 0 else 0

            ticker = {
                'symbol': data['instId'],
                'last_price': last_price,
                'bid_price': float(data['bidPx']),
                'ask_price': float(data['askPx']),
                'high_24h': float(data['high24h']),
                'low_24h': float(data['low24h']),
                'volume_24h': float(data['vol24h']),
                'change_24h': change_24h,
                'open_24h': open_24h,
                'timestamp': self._safe_timestamp_to_datetime(data.get('ts'))
            }
            
            self.logger.debug(f"获取行情数据成功: {symbol} 价格={ticker['last_price']}")
            return ticker
            
        except Exception as e:
            self.logger.error(f"获取行情数据失败: {symbol} - {e}")
            raise
    
    def _is_cache_valid(self, cache_key: str, timeframe: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self.kline_cache or cache_key not in self.last_update_time:
            return False
        
        # 根据时间周期确定缓存有效期
        cache_duration = {
            '1m': timedelta(seconds=30),
            '5m': timedelta(minutes=2),
            '15m': timedelta(minutes=5),
            '1H': timedelta(minutes=15),
            '4H': timedelta(hours=1),
            '1D': timedelta(hours=4)
        }
        
        duration = cache_duration.get(timeframe, timedelta(minutes=1))
        return datetime.now() - self.last_update_time[cache_key] < duration
    
    def clear_cache(self) -> None:
        """清空所有缓存"""
        with self.cache_lock:
            self.kline_cache.clear()
            self.account_cache.clear()
            self.position_cache.clear()
            self.last_update_time.clear()
        
        self.logger.info("数据缓存已清空")

    def _safe_timestamp_to_datetime(self, timestamp) -> datetime:
        """安全地将时间戳转换为datetime对象"""
        try:
            if timestamp is None:
                return datetime.now()

            # 转换为字符串再转为整数，避免C long限制
            ts_int = int(str(timestamp))

            # 检查时间戳是否合理（毫秒级）
            if ts_int > *************:  # 超过合理范围
                self.logger.warning(f"时间戳过大: {ts_int}")
                return datetime.now()

            return datetime.fromtimestamp(ts_int / 1000)

        except (ValueError, OverflowError, OSError) as e:
            self.logger.warning(f"时间戳转换失败: {timestamp} - {e}")
            return datetime.now()
    
    def get_market_summary(self, symbols: List[str]) -> Dict[str, Dict[str, Any]]:
        """
        获取多个交易对的市场概览
        
        Args:
            symbols: 交易对符号列表
            
        Returns:
            交易对到市场数据的映射
        """
        summary = {}
        
        for symbol in symbols:
            try:
                ticker = self.get_ticker(symbol)
                summary[symbol] = ticker
                time.sleep(0.1)  # 避免API限制
                
            except Exception as e:
                self.logger.warning(f"获取{symbol}市场数据失败: {e}")
                continue
        
        return summary

    def get_instruments(self, inst_type: str = 'SWAP') -> List[Dict[str, Any]]:
        """
        获取交易工具列表（交易对）

        Args:
            inst_type: 工具类型 - 'SPOT'(现货), 'SWAP'(永续合约), 'FUTURES'(期货), 'OPTION'(期权)

        Returns:
            交易工具列表
        """
        try:
            if not self._check_api_ready():
                return []

            response = self.public_api.get_instruments(instType=inst_type)

            if response['code'] != '0':
                raise Exception(f"OKX API错误: {response['msg']}")

            instruments = []
            for item in response['data']:
                instrument = {
                    'inst_id': item['instId'],
                    'inst_type': item['instType'],
                    'base_ccy': item['baseCcy'],
                    'quote_ccy': item['quoteCcy'],
                    'settle_ccy': item.get('settleCcy', ''),
                    'ct_val': item.get('ctVal', ''),
                    'ct_mult': item.get('ctMult', ''),
                    'ct_val_ccy': item.get('ctValCcy', ''),
                    'opt_type': item.get('optType', ''),
                    'strike': item.get('stk', ''),
                    'list_time': item.get('listTime', ''),
                    'exp_time': item.get('expTime', ''),
                    'lever': item.get('lever', ''),
                    'tick_sz': item.get('tickSz', ''),
                    'lot_sz': item.get('lotSz', ''),
                    'min_sz': item.get('minSz', ''),
                    'ct_type': item.get('ctType', ''),
                    'alias': item.get('alias', ''),
                    'state': item.get('state', '')
                }
                instruments.append(instrument)

            self.logger.info(f"获取{inst_type}交易工具成功: {len(instruments)}个")
            return instruments

        except Exception as e:
            self.logger.error(f"获取交易工具失败: {e}")
            return []

    def get_popular_instruments(self) -> List[Dict[str, Any]]:
        """
        获取热门交易对（USDT永续合约）

        Returns:
            热门交易对列表
        """
        try:
            all_instruments = self.get_instruments('SWAP')

            # 筛选USDT永续合约
            usdt_instruments = [
                inst for inst in all_instruments
                if inst['settle_ccy'] == 'USDT' and inst['state'] == 'live'
            ]

            # 按交易对名称排序，优先显示主流币种
            priority_coins = ['BTC', 'ETH', 'BNB', 'ADA', 'SOL', 'DOGE', 'MATIC', 'DOT', 'AVAX', 'LINK']

            def sort_key(inst):
                base_ccy = inst['base_ccy']
                if base_ccy in priority_coins:
                    return (0, priority_coins.index(base_ccy))
                else:
                    return (1, base_ccy)

            usdt_instruments.sort(key=sort_key)

            # 返回前50个热门交易对
            return usdt_instruments[:50]

        except Exception as e:
            self.logger.error(f"获取热门交易对失败: {e}")
            return []
