#!/usr/bin/env python3
"""
检查内存中的配置
"""

import sys
sys.path.insert(0, 'src')
from src.config_manager import ConfigManager

def check_memory_config():
    """检查内存中的配置"""
    config = ConfigManager()
    print("=== 检查内存中的凭证配置 ===")
    print("配置结构:")
    for key, value in config.config.items():
        print(f"  {key}: {type(value)}")

    if 'credentials' in config.config:
        creds = config.config['credentials']
        print(f"\n凭证详情:")
        for service, service_creds in creds.items():
            print(f"  {service}:")
            if isinstance(service_creds, dict):
                for cred_key, cred_value in service_creds.items():
                    status = "已设置" if cred_value else "未设置"
                    print(f"    {cred_key}: {status}")
            else:
                print(f"    值类型: {type(service_creds)}")

def migrate_credentials_to_db():
    """将内存中的凭证迁移到数据库"""
    print("\n=== 迁移凭证到数据库 ===")
    
    config = ConfigManager()
    
    if 'credentials' not in config.config:
        print("❌ 内存中没有凭证配置")
        return False
    
    creds = config.config['credentials']
    
    if 'okx' not in creds:
        print("❌ 内存中没有OKX凭证配置")
        return False
    
    okx_creds = creds['okx']
    
    if not isinstance(okx_creds, dict):
        print("❌ OKX凭证格式不正确")
        return False
    
    api_key = okx_creds.get('api_key')
    api_secret = okx_creds.get('api_secret') 
    passphrase = okx_creds.get('passphrase')
    
    if not all([api_key, api_secret, passphrase]):
        print("❌ OKX凭证不完整")
        print(f"  API Key: {'已设置' if api_key else '未设置'}")
        print(f"  API Secret: {'已设置' if api_secret else '未设置'}")
        print(f"  Passphrase: {'已设置' if passphrase else '未设置'}")
        return False
    
    # 迁移到数据库
    try:
        import sqlite3
        from datetime import datetime
        
        with sqlite3.connect(config.db_path) as conn:
            cursor = conn.cursor()
            
            # 检查是否已存在
            cursor.execute("SELECT id FROM credentials WHERE service_name = ?", ('okx',))
            existing = cursor.fetchone()
            
            current_time = datetime.now().isoformat()
            
            if existing:
                # 更新现有记录
                cursor.execute("""
                    UPDATE credentials 
                    SET api_key = ?, api_secret = ?, passphrase = ?, updated_at = ?
                    WHERE service_name = ?
                """, (api_key, api_secret, passphrase, current_time, 'okx'))
                print("✅ 已更新数据库中的OKX凭证")
            else:
                # 插入新记录
                cursor.execute("""
                    INSERT INTO credentials (service_name, api_key, api_secret, passphrase, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, ('okx', api_key, api_secret, passphrase, current_time, current_time))
                print("✅ 已添加OKX凭证到数据库")
            
            conn.commit()
        
        return True
        
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        return False

def test_after_migration():
    """测试迁移后的凭证读取"""
    print("\n=== 测试迁移后的凭证读取 ===")
    
    config = ConfigManager()
    
    api_key = config.get_credential('okx', 'api_key')
    api_secret = config.get_credential('okx', 'api_secret')
    passphrase = config.get_credential('okx', 'passphrase')
    
    print(f"API Key: {'已获取' if api_key else '未获取'}")
    print(f"API Secret: {'已获取' if api_secret else '未获取'}")
    print(f"Passphrase: {'已获取' if passphrase else '未获取'}")
    
    if all([api_key, api_secret, passphrase]):
        print("🎉 凭证读取成功！")
        return True
    else:
        print("❌ 凭证读取失败")
        return False

if __name__ == "__main__":
    check_memory_config()
    
    print("\n" + "="*50)
    choice = input("是否要将内存中的凭证迁移到数据库？(y/n): ").strip().lower()
    
    if choice == 'y':
        if migrate_credentials_to_db():
            test_after_migration()
        else:
            print("❌ 迁移失败")
    else:
        print("👋 跳过迁移")
