#!/usr/bin/env python3
"""
验证风险管理器修复效果的简单测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_risk_manager_fix():
    """测试风险管理器修复"""
    print("🧪 验证风险管理器修复效果...")
    
    try:
        # 导入RiskManager
        from src.risk_manager import RiskManager
        from unittest.mock import Mock
        
        # 创建mock配置
        mock_config = Mock()
        mock_config.get.return_value = {}
        
        # 创建风险管理器
        risk_manager = RiskManager(mock_config)
        
        print(f"✅ RiskManager创建成功")
        print(f"📊 最小余额要求: {risk_manager.risk_params.get('min_balance_usdt', '未设置')} USDT")
        
        # 测试低余额情况
        test_balance = {
            'USDT': {
                'available': 20.0,
                'total': 20.0
            }
        }
        
        decision = {
            'decision': 'BUY',
            'confidence': 0.75,
            'position_size': 0.1
        }
        
        result = risk_manager.check_pre_trade_risk(
            symbol='BTC-USDT-SWAP',
            decision=decision,
            positions={},
            balance=test_balance
        )
        
        print(f"📊 20 USDT余额测试结果:")
        print(f"  通过: {result.get('passed', False)}")
        print(f"  允许仓位: {result.get('allowed_size', 0)}")
        
        if result.get('passed', False):
            print("🎉 修复成功！20 USDT余额可以通过风险检查")
        else:
            print("❌ 修复失败，20 USDT余额仍被拒绝")
            print(f"  错误: {result.get('errors', [])}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_risk_manager_fix()