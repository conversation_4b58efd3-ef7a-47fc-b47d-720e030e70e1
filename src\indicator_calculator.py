"""
指标计算模块 - Indicator Calculator
使用TA-Lib库计算各种技术分析指标
支持MA、EMA、RSI、MACD、BOLL等主流指标
"""

import pandas as pd
import numpy as np
import talib
from typing import Dict, List, Optional, Any, Tuple
import logging
import asyncio
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime, timedelta
from .config_manager import ConfigManager

class IndicatorCalculator:
    """技术指标计算器 - 基于TA-Lib计算各种技术指标"""
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化指标计算器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)

        # 异步执行器 - 用于并行计算指标
        self.executor = ThreadPoolExecutor(max_workers=8, thread_name_prefix="IndicatorCalc")

        # 默认指标参数
        self.default_params = {
            'sma_periods': [5, 10, 20, 50],
            'ema_periods': [5, 10, 20, 50],
            'rsi_period': 14,
            'macd_fast': 12,
            'macd_slow': 26,
            'macd_signal': 9,
            'bb_period': 20,
            'bb_std': 2,
            'atr_period': 14,
            'adx_period': 14,
            'stoch_k': 14,
            'stoch_d': 3,
            'cci_period': 14,
            'williams_period': 14
        }
        
        # 从配置加载自定义参数
        self._load_indicator_params()
    
    def _load_indicator_params(self) -> None:
        """从配置加载指标参数"""
        try:
            custom_params = self.config_manager.get('indicators', {})
            self.default_params.update(custom_params)
            self.logger.info("指标参数加载完成")
        except Exception as e:
            self.logger.warning(f"加载指标参数失败，使用默认参数: {e}")
    
    def calculate_moving_averages(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算移动平均线指标
        
        Args:
            df: 包含OHLCV数据的DataFrame
            
        Returns:
            添加了移动平均线的DataFrame
        """
        try:
            close = df['close'].values
            
            # 简单移动平均线 (SMA)
            for period in self.default_params['sma_periods']:
                if len(close) >= period:
                    sma = talib.SMA(close, timeperiod=period)
                    df[f'sma_{period}'] = sma
            
            # 指数移动平均线 (EMA)
            for period in self.default_params['ema_periods']:
                if len(close) >= period:
                    ema = talib.EMA(close, timeperiod=period)
                    df[f'ema_{period}'] = ema
            
            # 加权移动平均线 (WMA)
            if len(close) >= 20:
                wma = talib.WMA(close, timeperiod=20)
                df['wma_20'] = wma
            
            # 三重指数移动平均线 (TEMA)
            if len(close) >= 30:
                tema = talib.TEMA(close, timeperiod=30)
                df['tema_30'] = tema
            
            self.logger.debug("移动平均线计算完成")
            return df
            
        except Exception as e:
            self.logger.error(f"计算移动平均线失败: {e}")
            return df
    
    def calculate_momentum_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算动量指标
        
        Args:
            df: 包含OHLCV数据的DataFrame
            
        Returns:
            添加了动量指标的DataFrame
        """
        try:
            high = df['high'].values
            low = df['low'].values
            close = df['close'].values
            volume = df['volume'].values
            
            # RSI - 相对强弱指数
            if len(close) >= self.default_params['rsi_period']:
                rsi = talib.RSI(close, timeperiod=self.default_params['rsi_period'])
                df['rsi'] = rsi
            
            # MACD - 指数平滑移动平均线
            if len(close) >= self.default_params['macd_slow']:
                macd, macd_signal, macd_hist = talib.MACD(
                    close,
                    fastperiod=self.default_params['macd_fast'],
                    slowperiod=self.default_params['macd_slow'],
                    signalperiod=self.default_params['macd_signal']
                )
                df['macd'] = macd
                df['macd_signal'] = macd_signal
                df['macd_histogram'] = macd_hist
            
            # ADX - 平均趋向指数
            if len(close) >= self.default_params['adx_period']:
                adx = talib.ADX(high, low, close, timeperiod=self.default_params['adx_period'])
                df['adx'] = adx
            
            # 随机指标 (Stochastic)
            if len(close) >= self.default_params['stoch_k']:
                slowk, slowd = talib.STOCH(
                    high, low, close,
                    fastk_period=self.default_params['stoch_k'],
                    slowk_period=self.default_params['stoch_d'],
                    slowd_period=self.default_params['stoch_d']
                )
                df['stoch_k'] = slowk
                df['stoch_d'] = slowd
            
            # CCI - 顺势指标
            if len(close) >= self.default_params['cci_period']:
                cci = talib.CCI(high, low, close, timeperiod=self.default_params['cci_period'])
                df['cci'] = cci
            
            # 威廉指标 (Williams %R)
            if len(close) >= self.default_params['williams_period']:
                willr = talib.WILLR(high, low, close, timeperiod=self.default_params['williams_period'])
                df['williams_r'] = willr
            
            # MFI - 资金流量指数
            if len(close) >= 14:
                mfi = talib.MFI(high, low, close, volume, timeperiod=14)
                df['mfi'] = mfi
            
            # ROC - 变动率
            if len(close) >= 10:
                roc = talib.ROC(close, timeperiod=10)
                df['roc'] = roc
            
            self.logger.debug("动量指标计算完成")
            return df
            
        except Exception as e:
            self.logger.error(f"计算动量指标失败: {e}")
            return df
    
    def calculate_volatility_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算波动率指标
        
        Args:
            df: 包含OHLCV数据的DataFrame
            
        Returns:
            添加了波动率指标的DataFrame
        """
        try:
            high = df['high'].values
            low = df['low'].values
            close = df['close'].values
            
            # 布林带 (Bollinger Bands)
            if len(close) >= self.default_params['bb_period']:
                bb_upper, bb_middle, bb_lower = talib.BBANDS(
                    close,
                    timeperiod=self.default_params['bb_period'],
                    nbdevup=self.default_params['bb_std'],
                    nbdevdn=self.default_params['bb_std']
                )
                df['bb_upper'] = bb_upper
                df['bb_middle'] = bb_middle
                df['bb_lower'] = bb_lower
                
                # 布林带宽度和位置
                df['bb_width'] = (bb_upper - bb_lower) / bb_middle
                df['bb_position'] = (close - bb_lower) / (bb_upper - bb_lower)
            
            # ATR - 真实波动幅度
            if len(close) >= self.default_params['atr_period']:
                atr = talib.ATR(high, low, close, timeperiod=self.default_params['atr_period'])
                df['atr'] = atr
                
                # ATR百分比
                df['atr_pct'] = atr / close * 100
            
            # 标准差
            if len(close) >= 20:
                std = talib.STDDEV(close, timeperiod=20)
                df['std_20'] = std
            
            self.logger.debug("波动率指标计算完成")
            return df
            
        except Exception as e:
            self.logger.error(f"计算波动率指标失败: {e}")
            return df
    
    def calculate_volume_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算成交量指标
        
        Args:
            df: 包含OHLCV数据的DataFrame
            
        Returns:
            添加了成交量指标的DataFrame
        """
        try:
            high = df['high'].values
            low = df['low'].values
            close = df['close'].values
            volume = df['volume'].values
            
            # OBV - 能量潮指标
            if len(close) >= 2:
                obv = talib.OBV(close, volume)
                df['obv'] = obv
            
            # AD - 累积/派发线
            if len(close) >= 2:
                ad = talib.AD(high, low, close, volume)
                df['ad'] = ad
            
            # ADOSC - 累积/派发震荡器
            if len(close) >= 10:
                adosc = talib.ADOSC(high, low, close, volume, fastperiod=3, slowperiod=10)
                df['adosc'] = adosc
            
            # 成交量移动平均
            if len(volume) >= 20:
                # 计算基础成交量MA20
                volume_ma = talib.SMA(volume, timeperiod=20)
                df['volume_ma_20'] = volume_ma

                # 安全计算成交量比率，避免除零错误
                volume_ratio = np.where(volume_ma != 0, volume / volume_ma, 1.0)
                df['volume_ratio'] = volume_ratio

                self.logger.debug(f"成交量MA20计算完成，数据长度: {len(volume)}, MA20有效值: {(~np.isnan(volume_ma)).sum()}")
            else:
                self.logger.warning(f"数据长度不足，无法计算成交量MA20，当前长度: {len(volume)}, 需要至少20个数据点")
            
            self.logger.debug("成交量指标计算完成")
            return df
            
        except Exception as e:
            self.logger.error(f"计算成交量指标失败: {e}")
            return df
    
    def calculate_pattern_recognition(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算K线形态识别
        
        Args:
            df: 包含OHLCV数据的DataFrame
            
        Returns:
            添加了形态识别的DataFrame
        """
        try:
            open_price = df['open'].values
            high = df['high'].values
            low = df['low'].values
            close = df['close'].values
            
            # 主要K线形态
            patterns = {
                'doji': talib.CDLDOJI,
                'hammer': talib.CDLHAMMER,
                'hanging_man': talib.CDLHANGINGMAN,
                'shooting_star': talib.CDLSHOOTINGSTAR,
                'engulfing': talib.CDLENGULFING,
                'harami': talib.CDLHARAMI,
                'dark_cloud': talib.CDLDARKCLOUDCOVER,
                'piercing': talib.CDLPIERCING,
                'morning_star': talib.CDLMORNINGSTAR,
                'evening_star': talib.CDLEVENINGSTAR,
                'three_white_soldiers': talib.CDL3WHITESOLDIERS,
                'three_black_crows': talib.CDL3BLACKCROWS
            }
            
            for pattern_name, pattern_func in patterns.items():
                try:
                    if len(close) >= 3:  # 大多数形态需要至少3根K线
                        pattern_result = pattern_func(open_price, high, low, close)
                        df[f'pattern_{pattern_name}'] = pattern_result
                except Exception as e:
                    self.logger.warning(f"计算形态{pattern_name}失败: {e}")
                    continue
            
            self.logger.debug("K线形态识别完成")
            return df
            
        except Exception as e:
            self.logger.error(f"计算K线形态失败: {e}")
            return df
    
    def calculate_all_indicators(self, df: pd.DataFrame, symbol: str = "UNKNOWN") -> pd.DataFrame:
        """
        计算所有技术指标

        Args:
            df: 包含OHLCV数据的DataFrame
            symbol: 交易对符号（用于日志输出）

        Returns:
            添加了所有指标的DataFrame
        """
        try:
            # 确保数据有效
            if df.empty or len(df) < 2:
                self.logger.warning("数据不足，无法计算指标")
                return df
            
            # 复制DataFrame避免修改原数据
            result_df = df.copy()
            
            # 计算各类指标
            result_df = self.calculate_moving_averages(result_df)
            result_df = self.calculate_momentum_indicators(result_df)
            result_df = self.calculate_volatility_indicators(result_df)
            result_df = self.calculate_volume_indicators(result_df)
            result_df = self.calculate_pattern_recognition(result_df)
            
            # 添加自定义指标
            result_df = self._calculate_custom_indicators(result_df)
            
            # 数据清理和对齐
            result_df = self._clean_and_align_data(result_df)
            
            self.logger.info(f"所有指标计算完成，共{len(result_df.columns)}列")

            # 输出详细的指标计算结果
            self._print_indicator_results(symbol, result_df)

            return result_df
            
        except Exception as e:
            self.logger.error(f"计算所有指标失败: {e}")
            return df
    
    def _calculate_custom_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算自定义指标"""
        try:
            close = df['close'].values
            
            # 价格变化率
            if len(close) >= 2:
                df['price_change'] = df['close'].pct_change()
                df['price_change_abs'] = df['price_change'].abs()
            
            # 高低点突破
            if len(close) >= 20:
                df['high_20'] = df['high'].rolling(20).max()
                df['low_20'] = df['low'].rolling(20).min()
                df['breakout_high'] = (df['close'] > df['high_20'].shift(1)).astype(int)
                df['breakout_low'] = (df['close'] < df['low_20'].shift(1)).astype(int)
            
            # 趋势强度
            if 'sma_5' in df.columns and 'sma_20' in df.columns:
                df['trend_strength'] = (df['sma_5'] - df['sma_20']) / df['sma_20']
            
            # 波动率
            if len(close) >= 10:
                df['volatility_10'] = df['close'].rolling(10).std() / df['close'].rolling(10).mean()
            
            return df
            
        except Exception as e:
            self.logger.warning(f"计算自定义指标失败: {e}")
            return df
    
    def _clean_and_align_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清理和对齐数据"""
        try:
            # 移除无穷大值
            df = df.replace([np.inf, -np.inf], np.nan)
            
            # 前向填充NaN值（仅对指标列）
            indicator_columns = [col for col in df.columns if col not in ['timestamp', 'open', 'high', 'low', 'close', 'volume']]
            df[indicator_columns] = df[indicator_columns].ffill()
            
            # 移除仍然包含NaN的行（通常是开头几行）
            df = df.dropna(subset=['close'])
            
            return df
            
        except Exception as e:
            self.logger.warning(f"数据清理失败: {e}")
            return df
    
    def get_latest_indicators(self, df: pd.DataFrame, count: int = 1) -> Dict[str, Any]:
        """
        获取最新的指标值
        
        Args:
            df: 包含指标的DataFrame
            count: 获取最新几个值
            
        Returns:
            最新指标值的字典
        """
        try:
            if df.empty:
                return {}
            
            # 获取最新的指标值
            latest_data = df.tail(count)
            
            # 转换为字典格式
            indicators = {}
            for column in latest_data.columns:
                if column not in ['timestamp']:
                    values = latest_data[column].tolist()
                    indicators[column] = values[-1] if count == 1 else values
            
            return indicators
            
        except Exception as e:
            self.logger.error(f"获取最新指标失败: {e}")
            return {}
    
    def create_feature_vector(self, df: pd.DataFrame, lookback: int = 10) -> np.ndarray:
        """
        创建用于AI决策的特征向量
        
        Args:
            df: 包含指标的DataFrame
            lookback: 回看周期
            
        Returns:
            特征向量数组
        """
        try:
            if len(df) < lookback:
                self.logger.warning(f"数据不足，需要至少{lookback}条数据")
                return np.array([])
            
            # 选择关键指标
            key_indicators = [
                'close', 'volume', 'rsi', 'macd', 'macd_signal', 
                'bb_position', 'atr_pct', 'adx', 'stoch_k', 'cci'
            ]
            
            # 过滤存在的指标
            available_indicators = [col for col in key_indicators if col in df.columns]
            
            if not available_indicators:
                self.logger.warning("没有可用的指标数据")
                return np.array([])
            
            # 获取最新数据
            recent_data = df[available_indicators].tail(lookback)
            
            # 标准化数据
            normalized_data = (recent_data - recent_data.mean()) / (recent_data.std() + 1e-8)
            
            # 展平为一维向量
            feature_vector = normalized_data.values.flatten()

            # 输出特征向量详细信息
            self._print_feature_vector_details(available_indicators, recent_data, normalized_data, feature_vector)

            return feature_vector
            
        except Exception as e:
            self.logger.error(f"创建特征向量失败: {e}")
            return np.array([])

    def _print_feature_vector_details(self, indicators: list, raw_data: pd.DataFrame, normalized_data: pd.DataFrame, feature_vector: np.ndarray):
        """输出特征向量的详细信息"""
        try:
            print(f"\n🔢 ===== 特征向量详细信息 =====")
            print(f"📊 使用的指标: {indicators}")
            print(f"📈 数据维度: {raw_data.shape}")
            print(f"🎯 特征向量长度: {len(feature_vector)}")

            print(f"\n📋 原始数据 (最新5行):")
            print(raw_data.tail().round(4))

            print(f"\n🔄 标准化后数据 (最新5行):")
            print(normalized_data.tail().round(4))

            print(f"\n🎯 特征向量 (前20个值):")
            print(f"  {feature_vector[:20].round(4)}")
            if len(feature_vector) > 20:
                print(f"  ... (共{len(feature_vector)}个特征)")

            print(f"=" * 50)

        except Exception as e:
            self.logger.error(f"输出特征向量详情失败: {e}")

    async def calculate_multi_timeframe_indicators_async(self,
                                                       data_fetcher,
                                                       symbol: str,
                                                       timeframes: List[str]) -> Dict[str, Dict[str, Any]]:
        """
        异步计算多时间周期技术指标 - 并行获取数据和计算指标

        Args:
            data_fetcher: 数据获取器实例
            symbol: 交易对符号
            timeframes: 时间周期列表

        Returns:
            多时间周期指标数据
        """
        try:
            # 并行获取多个时间周期的K线数据
            tasks = []
            for timeframe in timeframes:
                task = asyncio.create_task(
                    self._get_kline_data_async(data_fetcher, symbol, timeframe)
                )
                tasks.append((timeframe, task))

            # 等待所有数据获取完成
            multi_timeframe_data = {}
            for timeframe, task in tasks:
                try:
                    kline_data = await task
                    if kline_data is not None and not kline_data.empty:
                        multi_timeframe_data[timeframe] = kline_data
                        self.logger.debug(f"✅ {symbol} {timeframe} 数据获取成功: {len(kline_data)}条")
                    else:
                        self.logger.warning(f"❌ {symbol} {timeframe} 数据获取失败或为空")
                except Exception as e:
                    self.logger.error(f"❌ {symbol} {timeframe} 数据获取异常: {e}")

            if not multi_timeframe_data:
                self.logger.error(f"所有时间周期数据获取失败: {symbol}")
                return {}

            # 并行计算各时间周期的指标
            indicator_tasks = []
            for timeframe, df in multi_timeframe_data.items():
                task = asyncio.create_task(
                    self._calculate_timeframe_indicators_async(df, timeframe)
                )
                indicator_tasks.append((timeframe, task))

            # 等待所有指标计算完成
            multi_indicators = {}
            for timeframe, task in indicator_tasks:
                try:
                    indicators = await task
                    multi_indicators[timeframe] = indicators
                    self.logger.debug(f"✅ {symbol} {timeframe} 指标计算成功: {len(indicators)}个")
                except Exception as e:
                    self.logger.error(f"❌ {symbol} {timeframe} 指标计算异常: {e}")

            self.logger.info(f"🚀 {symbol} 异步多时间周期指标计算完成: {len(multi_indicators)}个周期")
            return multi_indicators

        except Exception as e:
            self.logger.error(f"异步多时间周期指标计算失败: {symbol} - {e}")
            return {}

    async def _get_kline_data_async(self, data_fetcher, symbol: str, timeframe: str) -> Optional[pd.DataFrame]:
        """异步获取K线数据"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self.executor,
            data_fetcher.get_kline_data,
            symbol,
            timeframe,
            200  # limit
        )

    async def _calculate_timeframe_indicators_async(self, df: pd.DataFrame, timeframe: str) -> Dict[str, Any]:
        """异步计算单个时间周期的指标"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self.executor,
            self._calculate_single_timeframe_indicators,
            df,
            timeframe
        )

    def _calculate_single_timeframe_indicators(self, df: pd.DataFrame, timeframe: str) -> Dict[str, Any]:
        """计算单个时间周期的指标（同步版本，用于异步执行）"""
        try:
            # 使用现有的指标计算逻辑
            indicators = self.calculate_indicators(df)

            # 添加时间周期特定的指标
            timeframe_indicators = self._calculate_timeframe_specific_indicators(df, timeframe)
            indicators.update(timeframe_indicators)

            return indicators

        except Exception as e:
            self.logger.error(f"计算{timeframe}指标失败: {e}")
            return {}

    def calculate_multi_timeframe_indicators(self,
                                           multi_timeframe_data: Dict[str, pd.DataFrame],
                                           symbol: str = "UNKNOWN") -> Dict[str, Dict[str, Any]]:
        """
        计算多时间周期技术指标

        Args:
            multi_timeframe_data: 多时间周期K线数据字典
            symbol: 交易对符号

        Returns:
            各时间周期的指标字典
        """
        try:
            multi_indicators = {}

            for timeframe, df in multi_timeframe_data.items():
                if df is None or df.empty:
                    self.logger.warning(f"时间周期{timeframe}数据为空: {symbol}")
                    continue

                # 计算该时间周期的所有指标
                indicators_df = self.calculate_all_indicators(df, f"{symbol}_{timeframe}")

                if indicators_df is not None and not indicators_df.empty:
                    # 获取最新指标值
                    latest_indicators = self.get_latest_indicators(indicators_df)

                    # 添加时间周期标识
                    latest_indicators['timeframe'] = timeframe

                    # 添加时间周期特定的分析
                    latest_indicators.update(self._calculate_timeframe_specific_indicators(indicators_df, timeframe))

                    multi_indicators[timeframe] = latest_indicators

                    self.logger.debug(f"{symbol} {timeframe}指标计算完成")
                else:
                    self.logger.warning(f"{symbol} {timeframe}指标计算失败")

            # 多时间周期指标摘要输出已取消，专注显示AI输入数据
            # self._print_multi_timeframe_summary(symbol, multi_indicators)

            return multi_indicators

        except Exception as e:
            self.logger.error(f"计算多时间周期指标失败: {symbol} - {e}")
            return {}

    def _calculate_timeframe_specific_indicators(self, df: pd.DataFrame, timeframe: str) -> Dict[str, Any]:
        """计算时间周期特定的指标"""
        try:
            specific_indicators = {}

            if len(df) < 2:
                return specific_indicators

            # 计算价格变化率（根据时间周期调整）
            timeframe_multipliers = {
                '1m': 1, '5m': 5, '15m': 15, '30m': 30, '1H': 60
            }

            multiplier = timeframe_multipliers.get(timeframe, 1)

            # 价格变化率
            if 'close' in df.columns:
                latest_close = df['close'].iloc[-1]
                prev_close = df['close'].iloc[-2] if len(df) >= 2 else latest_close

                specific_indicators[f'price_change_{timeframe}'] = (latest_close - prev_close) / prev_close * 100

                # 计算该时间周期的波动率
                if len(df) >= 10:
                    returns = df['close'].pct_change().dropna()
                    specific_indicators[f'volatility_{timeframe}'] = returns.std() * 100

                # 计算该时间周期的趋势强度
                if len(df) >= 20:
                    sma_short = df['close'].rolling(5).mean().iloc[-1]
                    sma_long = df['close'].rolling(20).mean().iloc[-1]
                    if sma_long != 0:
                        specific_indicators[f'trend_strength_{timeframe}'] = (sma_short - sma_long) / sma_long * 100

            # 成交量分析
            if 'volume' in df.columns and len(df) >= 10:
                avg_volume = df['volume'].rolling(10).mean().iloc[-1]
                current_volume = df['volume'].iloc[-1]
                if avg_volume > 0:
                    specific_indicators[f'volume_ratio_{timeframe}'] = current_volume / avg_volume

            return specific_indicators

        except Exception as e:
            self.logger.warning(f"计算{timeframe}特定指标失败: {e}")
            return {}

    def _print_multi_timeframe_summary(self, symbol: str, multi_indicators: Dict[str, Dict[str, Any]]):
        """输出多时间周期指标详细摘要"""
        try:
            if not multi_indicators:
                self.logger.warning(f"{symbol} - 多时间周期指标为空")
                return

            print(f"\n📊 ===== {symbol} 多时间周期指标摘要 =====")

            for timeframe in ['1H', '30m', '15m', '5m', '1m']:
                if timeframe in multi_indicators:
                    data = multi_indicators[timeframe]
                    print(f"\n⏰ {timeframe} 周期:")
                    print(f"  💰 价格: {data.get('close', 0):.2f}")
                    print(f"  📈 RSI: {data.get('rsi', 0):.1f}")
                    print(f"  📊 MACD: {data.get('macd', 0):.4f}")
                    print(f"  🎯 ADX: {data.get('adx', 0):.1f}")
                    print(f"  🌊 BB位置: {data.get('bb_position', 0):.2f}")

            # 输出简要摘要到日志
            timeframes = list(multi_indicators.keys())
            primary_timeframe = '15m' if '15m' in timeframes else timeframes[0]

            if primary_timeframe in multi_indicators:
                primary_data = multi_indicators[primary_timeframe]
                price = primary_data.get('close', 0)
                rsi = primary_data.get('rsi', 0)

                self.logger.info(f"{symbol} 多时间周期指标计算完成 - 主周期{primary_timeframe}: 价格{price:.2f}, RSI{rsi:.1f}, 覆盖{len(timeframes)}个周期")
            else:
                self.logger.info(f"{symbol} 多时间周期指标计算完成 - 覆盖{len(timeframes)}个周期")

            print(f"=" * 50)

        except Exception as e:
            self.logger.error(f"输出多时间周期摘要失败: {e}")

    def _print_indicator_results(self, symbol: str, df):
        """输出详细的指标计算结果"""
        try:
            if df.empty:
                print(f"\n❌ {symbol} - 指标数据为空")
                return

            latest = df.iloc[-1]  # 最新一行数据

            # 详细指标输出已取消，专注显示AI输入数据
            # print(f"\n📊 ===== {symbol} 指标计算结果 =====")
            pass

        except Exception as e:
            self.logger.error(f"输出指标结果失败: {e}")
            print(f"\n❌ {symbol} - 指标结果输出失败: {e}")
