# 🤖 DeepSeek驱动的全自动加密货币合约交易系统

基于AI的24x7无人值守量化交易系统，集成DeepSeek大模型进行智能决策。

## ✨ 核心特性

- 🧠 **AI智能决策**: 集成DeepSeek大模型，基于技术指标进行智能交易决策
- 📊 **多合约并行**: 支持多个交易对同时运行，多线程并发处理
- 🛡️ **实时风控**: 全方位风险管理，包括仓位控制、止盈止损、回撤监控
- 📈 **技术分析**: 集成TA-Lib库，支持358个技术指标计算
- 🌐 **Web监控**: 实时Web仪表盘，可视化监控系统状态
- 📝 **智能日志**: 彩色日志输出，实时监控和报警
- ⚡ **高性能**: 异步处理，缓存优化，毫秒级响应

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web仪表盘     │    │   配置管理      │    │   日志监控      │
│  (Flask)        │    │  (SQLite3)      │    │  (colorlog)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   主控制器      │
                    │  (多线程调度)    │
                    └─────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   数据获取      │    │   指标计算      │    │   决策引擎      │
│   (OKX API)     │    │   (TA-Lib)      │    │  (DeepSeek)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   交易执行      │
                    │   风控管理      │
                    └─────────────────┘
```

## 🛠️ 技术栈

- **后端**: Python 3.11+, SQLite3, TA-Lib
- **API**: OKX交易所API, DeepSeek AI API
- **Web**: Flask, Jinja2, HTML/CSS/JavaScript
- **并发**: Threading, AsyncIO, Schedule
- **监控**: Colorlog, 自定义监控系统

## 📦 安装部署

### 1. 环境要求

- Python 3.11+
- Windows 10/11 (已测试)
- 8GB+ RAM
- 稳定的网络连接

### 2. 克隆项目

```bash
git clone <repository-url>
cd deepseek-crypto-trading-system
```

### 3. 安装依赖

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
venv\Scripts\activate  # Windows
# source venv/bin/activate  # Linux/Mac

# 安装依赖
pip install -r requirements.txt

# 安装TA-Lib (使用提供的wheel文件)
pip install ta_lib-0.6.3-cp311-cp311-win_amd64.whl
```

### 4. 配置API密钥

创建 `key.txt` 文件：
```
OKX_API_KEY=your_okx_api_key
OKX_SECRET_KEY=your_okx_secret_key
OKX_PASSPHRASE=your_okx_passphrase
DEEPSEEK_API_KEY=your_deepseek_api_key
```

创建 `secrets.json` 文件：
```json
{
  "okx": {
    "api_key": "your_okx_api_key",
    "api_secret": "your_okx_secret_key",
    "passphrase": "your_okx_passphrase"
  },
  "deepseek": {
    "api_key": "your_deepseek_api_key"
  }
}
```

### 5. 启动系统

```bash
python main.py
```

## 🎮 使用指南

### Web界面访问

启动后访问: http://127.0.0.1:5000

### 主要功能

1. **系统监控**: 实时查看系统运行状态
2. **交易对管理**: 添加/删除/配置交易对
3. **风险监控**: 查看风险指标和报警
4. **日志查看**: 实时查看系统日志
5. **性能统计**: 查看交易统计和性能指标

### 配置交易对

```python
# 通过Web界面或直接配置
trading_pairs = {
    "BTC-USDT-SWAP": {
        "is_active": True,
        "position_side": "long",
        "leverage": 2,
        "max_position_size": 1000.0,
        "stop_loss_pct": 0.02,
        "take_profit_pct": 0.04
    }
}
```

## 📊 核心模块

### 1. 配置管理 (ConfigManager)
- SQLite3数据库存储
- API密钥管理
- 交易参数配置
- 动态配置更新

### 2. 数据获取 (DataFetcher)
- OKX API集成
- K线数据获取
- 账户信息查询
- 数据缓存优化

### 3. 指标计算 (IndicatorCalculator)
- TA-Lib集成
- 358个技术指标
- 自定义指标支持
- 数据预处理

### 4. 决策引擎 (DecisionEngine)
- DeepSeek AI集成
- 智能决策生成
- 置信度评估
- 历史决策追踪

### 5. 交易执行 (TradeExecutor)
- 自动下单执行
- 止盈止损管理
- 订单状态跟踪
- 错误处理重试

### 6. 风险管理 (RiskManager)
- 实时风险监控
- 仓位控制
- 回撤管理
- 自动报警

### 7. 多合约管理 (MultiContractManager)
- 多线程并发
- 任务调度
- 状态同步
- 性能监控

### 8. 日志监控 (LoggerMonitor)
- 彩色日志输出
- 实时监控
- 自动报警
- 性能统计

### 9. Web仪表盘 (WebDashboard)
- 实时数据展示
- 交互式控制
- 图表可视化
- 移动端适配

## ⚠️ 风险提示

1. **投资风险**: 量化交易存在亏损风险，请谨慎使用
2. **技术风险**: 系统可能存在bug，建议先在模拟环境测试
3. **API风险**: 请保护好API密钥，避免泄露
4. **网络风险**: 网络中断可能影响交易执行
5. **市场风险**: 极端市场条件下系统可能失效

## 🔧 开发指南

### 项目结构

```
deepseek-crypto-trading-system/
├── src/                          # 源代码目录
│   ├── __init__.py              # 包初始化
│   ├── config_manager.py        # 配置管理
│   ├── data_fetcher.py          # 数据获取
│   ├── indicator_calculator.py  # 指标计算
│   ├── decision_engine.py       # 决策引擎
│   ├── trade_executor.py        # 交易执行
│   ├── risk_manager.py          # 风险管理
│   ├── multi_contract_manager.py # 多合约管理
│   ├── logger_monitor.py        # 日志监控
│   ├── web_dashboard.py         # Web仪表盘
│   └── main_controller.py       # 主控制器
├── templates/                    # Web模板
├── static/                       # 静态资源
├── logs/                         # 日志文件
├── main.py                       # 主启动文件
├── requirements.txt              # 依赖列表
├── key.txt                       # API密钥
├── secrets.json                  # 备用配置
└── README.md                     # 说明文档
```

### 扩展开发

1. **添加新指标**: 在 `IndicatorCalculator` 中扩展
2. **自定义策略**: 修改 `DecisionEngine` 的决策逻辑
3. **新交易所**: 扩展 `DataFetcher` 和 `TradeExecutor`
4. **报警通知**: 在 `LoggerMonitor` 中添加通知方式

## 📚 AI助手专用文档

为了帮助AI助手更好地理解和维护本系统，我们提供了以下专门的指导文档：

### 🎯 核心指导文档
1. **[项目结构目录说明提示词规则](./项目结构目录说明提示词规则.md)**
   - 详细的项目结构理解框架
   - 模块分层架构和依赖关系
   - 文件定位和功能映射规则

2. **[AI助手快速参考手册](./AI助手快速参考手册.md)**
   - 常见操作的快速定位指南
   - 模块功能对照表
   - 标准工作流程和代码片段

3. **[错误处理与故障排除指南](./错误处理与故障排除指南.md)**
   - 系统性的错误分类和诊断方法
   - 常见问题的解决方案
   - 故障排除检查清单

4. **[AI助手代码分析修改规范](./AI助手代码分析修改规范.md)**
   - 标准化的代码分析流程
   - 代码修改和质量标准
   - 测试和审查规范

### 🔧 使用指南

#### 对于AI助手
1. **开始工作前**: 先阅读"项目结构目录说明提示词规则"了解整体架构
2. **快速定位**: 使用"AI助手快速参考手册"快速找到相关文件和方法
3. **遇到问题**: 参考"错误处理与故障排除指南"进行系统性分析
4. **修改代码**: 严格遵循"AI助手代码分析修改规范"的流程和标准

#### 对于开发者
- 这些文档同样适用于人类开发者，可以作为系统维护和扩展的参考
- 建议在修改系统前先阅读相关文档，确保遵循既定的架构和规范

### 📋 重要提醒

**🚨 必须遵循的核心原则**:
1. **理解优先**: 在修改任何代码前，必须深入理解现有实现
2. **架构一致**: 所有修改必须符合现有的系统架构设计
3. **测试验证**: 任何代码修改都必须经过充分的测试验证
4. **文档同步**: 重要修改需要同步更新相关文档

**❌ 严格禁止的操作**:
- 直接修改数据库文件 `credentials.db`
- 绕过 `config_manager` 直接读取配置
- 跨层级调用（违反架构设计）
- 在日志中记录敏感信息

## 📞 支持与反馈

- **问题反馈**: 请提交Issue
- **功能建议**: 欢迎Pull Request
- **技术交流**: 请联系开发团队

## 📄 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

---

**⚠️ 免责声明**: 本系统仅供学习和研究使用，使用者需自行承担交易风险。开发者不对任何投资损失负责。
