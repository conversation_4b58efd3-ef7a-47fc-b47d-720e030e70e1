"""
日志监控模块 - Logger Monitor
负责系统日志记录、监控和报警
使用colorlog提供彩色日志输出
"""

import logging
import logging.handlers
import os
import json
import time
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
import threading
import queue
import colorlog
import threading # 导入threading模块
from .config_manager import ConfigManager

class LoggerMonitor:
    """日志监控器 - 统一管理系统日志和监控"""
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化日志监控器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager
        
        # 日志配置
        self.log_config = self._load_log_config()
        
        # 设置日志系统
        self._setup_logging()
        
        # 获取logger
        self.logger = logging.getLogger(__name__)
        
        # 监控数据
        self.log_stats = {
            'total_logs': 0,
            'error_count': 0,
            'warning_count': 0,
            'info_count': 0,
            'debug_count': 0,
            'last_error': None,
            'last_warning': None,
            'start_time': datetime.now()
        }
        
        # 报警配置
        self.alert_config = {
            'error_threshold': 10,      # 错误数量阈值
            'warning_threshold': 50,    # 警告数量阈值
            'time_window': 300,         # 时间窗口（秒）
            'alert_cooldown': 600       # 报警冷却时间（秒）
        }
        
        # 报警状态
        self.alert_status = {
            'last_error_alert': None,
            'last_warning_alert': None,
            'alert_count': 0
        }
        
        # 日志缓存
        self.log_buffer = queue.Queue(maxsize=1000)
        self.recent_logs = []
        self.max_recent_logs = 100
        
        # 监控线程
        self.monitor_thread = None
        self.is_monitoring = False
        
        # 性能监控
        self.performance_logs = []

        # 线程锁
        self.lock = threading.Lock() # 初始化线程锁
        
        self.logger.info("日志监控器初始化完成")
    
    def _load_log_config(self) -> Dict[str, Any]:
        """加载日志配置"""
        try:
            default_config = {
                'log_level': 'INFO',
                'log_format': '%(asctime)s.%(msecs)03d-%(levelname)s-%(name)s-%(message)s',
                'color_format': '%(log_color)s%(asctime)s.%(msecs)03d-%(levelname)s-%(name)s-%(message)s',
                'log_file': 'logs/trading_system.log',
                'max_file_size': 10 * 1024 * 1024,  # 10MB
                'backup_count': 5,
                'enable_console': True,
                'enable_file': True,
                'enable_color': True
            }
            
            # 从配置加载自定义设置
            custom_config = self.config_manager.get('logging', {})
            default_config.update(custom_config)
            
            return default_config
            
        except Exception as e:
            # 在日志系统完全初始化之前，只能用print或sys.stderr.write
            # 这里的打印是为了确保即使日志配置加载失败也能有提示
            print(f"加载日志配置失败，使用默认配置: {e}")
            return {
                'log_level': 'INFO',
                'log_format': '%(asctime)s.%(msecs)03d-%(levelname)s-%(name)s-%(message)s',
                'log_file': 'logs/trading_system.log',
                'enable_console': True,
                'enable_file': True
            }
    
    def _setup_logging(self) -> None:
        """设置日志系统"""
        try:
            # 创建日志目录
            log_dir = os.path.dirname(self.log_config['log_file'])
            if log_dir and not os.path.exists(log_dir):
                try:
                    os.makedirs(log_dir)
                except OSError as e:
                    # 使用root_logger记录错误，确保即使文件日志被禁用，控制台也能看到
                    root_logger = logging.getLogger() # 提前获取root_logger
                    root_logger.error(f"创建日志目录 '{log_dir}' 失败: {e}. 请检查目录权限。")
                    self.log_config['enable_file'] = False # 禁用文件日志以防止进一步错误
            
            # 获取根logger
            root_logger = logging.getLogger()
            root_logger.setLevel(getattr(logging, self.log_config['log_level']))
            
            # 清除现有handlers
            for handler in root_logger.handlers[:]:
                root_logger.removeHandler(handler)
            
            # 设置控制台输出
            if self.log_config.get('enable_console', True):
                console_handler = logging.StreamHandler()
                
                if self.log_config.get('enable_color', True):
                    # 彩色日志格式
                    color_formatter = colorlog.ColoredFormatter(
                        self.log_config.get('color_format',
                            '%(log_color)s%(asctime)s.%(msecs)03d-%(levelname)s-%(name)s-%(message)s'),
                        log_colors={
                            'DEBUG': 'cyan',
                            'INFO': 'green',
                            'WARNING': 'yellow',
                            'ERROR': 'red',
                            'CRITICAL': 'red,bg_white',
                        }
                    )
                    console_handler.setFormatter(color_formatter)
                else:
                    # 普通格式
                    formatter = logging.Formatter(self.log_config['log_format'])
                    console_handler.setFormatter(formatter)
                
                root_logger.addHandler(console_handler)
            
            # 设置文件输出
            if self.log_config.get('enable_file', True):
                try:
                    file_handler = logging.handlers.RotatingFileHandler(
                        self.log_config['log_file'],
                        maxBytes=self.log_config.get('max_file_size', 10*1024*1024),
                        backupCount=self.log_config.get('backup_count', 5),
                        encoding='utf-8'
                    )
                    
                    file_formatter = logging.Formatter(self.log_config['log_format'])
                    file_handler.setFormatter(file_formatter)
                    root_logger.addHandler(file_handler)
                except OSError as e:
                    root_logger = logging.getLogger() # 提前获取root_logger
                    root_logger.error(f"初始化文件日志处理器失败: {e}. 日志文件可能被锁定或权限不足。请检查文件权限或是否有其他程序占用日志文件。")
                    self.log_config['enable_file'] = False # 禁用文件日志以防止进一步错误
            
            # 添加自定义handler用于监控
            monitor_handler = LogMonitorHandler(self)
            root_logger.addHandler(monitor_handler)
            
        except Exception as e:
            root_logger = logging.getLogger() # 提前获取root_logger
            root_logger.critical(f"设置日志系统失败: {e}", exc_info=True)
    
    def start_monitoring(self) -> None:
        """启动日志监控"""
        try:
            if self.is_monitoring:
                self.logger.warning("日志监控已在运行")
                return
            
            self.is_monitoring = True
            
            # 启动监控线程
            self.monitor_thread = threading.Thread(
                target=self._monitor_loop,
                name="LogMonitor",
                daemon=True
            )
            self.monitor_thread.start()
            
            self.logger.info("日志监控启动成功")
            
        except Exception as e:
            self.logger.error(f"启动日志监控失败: {e}")
            self.is_monitoring = False
    
    def stop_monitoring(self) -> None:
        """停止日志监控"""
        try:
            self.is_monitoring = False
            
            if self.monitor_thread and self.monitor_thread.is_alive():
                self.monitor_thread.join(timeout=5)
            
            self.logger.info("日志监控已停止")
            
        except Exception as e:
            self.logger.error(f"停止日志监控失败: {e}")
    
    def _monitor_loop(self) -> None:
        """监控循环"""
        try:
            while self.is_monitoring:
                try:
                    # 检查报警条件
                    self._check_alert_conditions()
                    
                    # 清理旧日志
                    self._cleanup_old_logs()
                    
                    # 生成监控报告
                    self._generate_monitoring_report()
                    
                    # 休眠
                    time.sleep(60)  # 每分钟检查一次
                    
                except Exception as e:
                    self.logger.error(f"监控循环异常: {e}", exc_info=True)
                    time.sleep(10)
                    
        except Exception as e:
            self.logger.critical(f"监控循环失败: {e}", exc_info=True)
    
    def _check_alert_conditions(self) -> None:
        """检查报警条件"""
        try:
            current_time = datetime.now()
            time_window = timedelta(seconds=self.alert_config['time_window'])
            
            # 统计时间窗口内的错误和警告
            recent_errors = 0
            recent_warnings = 0
            
            cutoff_time = current_time - time_window
            cutoff_time_str = cutoff_time.isoformat()
 
            with self.lock: # 保护对 self.recent_logs 的访问
                for log_entry in self.recent_logs:
                    if log_entry['timestamp'] > cutoff_time_str:
                        if log_entry['level'] == 'ERROR':
                            recent_errors += 1
                        elif log_entry['level'] == 'WARNING':
                            recent_warnings += 1
            
            # 检查错误报警
            if recent_errors >= self.alert_config['error_threshold']:
                self._trigger_alert('ERROR', f"错误数量过多: {recent_errors}个错误在{self.alert_config['time_window']}秒内")
            
            # 检查警告报警
            if recent_warnings >= self.alert_config['warning_threshold']:
                self._trigger_alert('WARNING', f"警告数量过多: {recent_warnings}个警告在{self.alert_config['time_window']}秒内")
                
        except Exception as e:
            self.logger.error(f"检查报警条件失败: {e}", exc_info=True)
    
    def _trigger_alert(self, alert_type: str, message: str) -> None:
        """触发报警"""
        try:
            current_time = datetime.now()
            cooldown = timedelta(seconds=self.alert_config['alert_cooldown'])
            
            with self.lock: # 保护对 self.alert_status 和 self.log_stats 的访问
                # 检查冷却时间
                last_alert_key = f'last_{alert_type.lower()}_alert'
                last_alert_time = self.alert_status.get(last_alert_key)
                
                if last_alert_time and current_time - last_alert_time < cooldown:
                    return  # 在冷却时间内，不发送报警
                
                # 记录报警（确保所有datetime对象都转换为字符串）
                log_stats_serializable = self.log_stats.copy() # 复制一份，因为log_stats会在其他地方更新
                if 'start_time' in log_stats_serializable:
                    log_stats_serializable['start_time'] = log_stats_serializable['start_time'].isoformat()
    
                alert_record = {
                    'timestamp': current_time.isoformat(),
                    'type': alert_type,
                    'message': message,
                    'stats': log_stats_serializable
                }
                
                # 更新报警状态
                self.alert_status[last_alert_key] = current_time
                self.alert_status['alert_count'] += 1
            
            # 发送报警（这里可以扩展为邮件、短信等）
            self._send_alert(alert_record) # _send_alert 内部会再次获取锁，避免死锁
            
        except Exception as e:
            self.logger.error(f"触发报警失败: {e}", exc_info=True)
    
    def _send_alert(self, alert_record: Dict[str, Any]) -> None:
        """发送报警"""
        try:
            # 记录到日志
            self.logger.critical(f"系统报警: {alert_record['message']}")
            
            # 这里可以扩展其他报警方式
            # 例如：发送邮件、短信、钉钉通知等
            
            # 保存报警记录到文件
            alert_file = 'logs/alerts.json'
            try:
                if os.path.exists(alert_file):
                    with open(alert_file, 'r', encoding='utf-8') as f:
                        alerts = json.load(f)
                else:
                    alerts = []
                
                alerts.append(alert_record)
                
                # 保持最近100条报警记录
                if len(alerts) > 100:
                    alerts = alerts[-100:]
                
                with open(alert_file, 'w', encoding='utf-8') as f:
                    json.dump(alerts, f, ensure_ascii=False, indent=2)
                    
            except Exception as e:
                self.logger.error(f"保存报警记录失败: {e}", exc_info=True)
                
        except Exception as e:
            self.logger.error(f"发送报警失败: {e}", exc_info=True)
    
    def _cleanup_old_logs(self) -> None:
        """清理旧日志"""
        try:
            # 清理内存中的日志
            cutoff_time = datetime.now() - timedelta(hours=1)
            cutoff_time_str = cutoff_time.isoformat()

            with self.lock: # 保护对 self.recent_logs 和 self.performance_logs 的访问
                self.recent_logs = [
                    log for log in self.recent_logs
                    if log['timestamp'] > cutoff_time_str
                ]
    
                # 清理性能日志
                self.performance_logs = [
                    log for log in self.performance_logs
                    if log['timestamp'] > cutoff_time_str
                ]
    
        except Exception as e:
            self.logger.error(f"清理旧日志失败: {e}", exc_info=True)
    
    def _generate_monitoring_report(self) -> None:
        """生成监控报告"""
        try:
            current_time = datetime.now()
            
            with self.lock: # 保护对 self.log_stats, self.alert_status, self.recent_logs, self.performance_logs 的访问
                # 计算运行时间
                uptime = current_time - self.log_stats['start_time']
                
                # 生成报告（确保所有datetime对象都转换为字符串）
                log_stats_serializable = self.log_stats.copy()
                if 'start_time' in log_stats_serializable:
                    log_stats_serializable['start_time'] = log_stats_serializable['start_time'].isoformat()
    
                alert_stats_serializable = self.alert_status.copy()
                # 转换alert_status中可能的datetime对象
                for key, value in alert_stats_serializable.items():
                    if isinstance(value, datetime):
                        alert_stats_serializable[key] = value.isoformat()
    
                report = {
                    'timestamp': current_time.isoformat(),
                    'uptime_seconds': uptime.total_seconds(),
                    'log_stats': log_stats_serializable,
                    'alert_stats': alert_stats_serializable,
                    'recent_log_count': len(self.recent_logs),
                    'performance_log_count': len(self.performance_logs)
                }
            
            # 保存报告 (文件操作本身不需要锁，因为 RotatingFileHandler 已经处理)
            report_file = 'logs/monitoring_report.json'
            try:
                with open(report_file, 'w', encoding='utf-8') as f:
                    json.dump(report, f, ensure_ascii=False, indent=2)
            except Exception as e:
                self.logger.error(f"保存监控报告失败: {e}", exc_info=True)
                
        except Exception as e:
            self.logger.error(f"生成监控报告失败: {e}", exc_info=True)
    
    def log_performance(self, operation: str, duration: float, success: bool = True, **kwargs) -> None:
        """记录性能日志"""
        try:
            with self.lock: # 保护对 self.performance_logs 的访问
                performance_log = {
                    'timestamp': datetime.now().isoformat(),
                    'operation': operation,
                    'duration': duration,
                    'success': success,
                    'details': kwargs
                }
                
                self.performance_logs.append(performance_log)
                
                # 保持最近1000条性能日志
                if len(self.performance_logs) > 1000:
                    self.performance_logs = self.performance_logs[-1000:]
            
            # 记录到普通日志
            status = "成功" if success else "失败"
            self.logger.info(f"性能日志: {operation} {status}, 耗时: {duration:.3f}秒")
            
        except Exception as e:
            self.logger.error(f"记录性能日志失败: {e}", exc_info=True)
    
    def log_trade_event(self, event_type: str, symbol: str, details: Dict[str, Any]) -> None:
        """记录交易事件"""
        try:
            # 记录到日志 (这里不需要锁，因为logger本身是线程安全的)
            self.logger.info(f"交易事件: {event_type} - {symbol} - {json.dumps(details, ensure_ascii=False)}")
            
            # 保存到交易日志文件
            trade_log_file = 'logs/trade_events.json'
            # 这里的读写文件操作，如果多个线程同时操作同一个文件，仍然可能存在问题。
            # 但由于文件是 JSON 格式，每次都是完整读入内存，修改，再完整写入，
            # 且 trade_events.json 不是 LoggerMonitor 的核心共享状态，
            # 而是独立的持久化存储，通常由调用方控制并发。
            # 为了简单起见，且避免引入不必要的复杂性（如文件锁），暂时不在这里加锁。
            # 如果实际运行中出现文件损坏或并发写入问题，再考虑引入文件锁。
            try:
                if os.path.exists(trade_log_file):
                    with open(trade_log_file, 'r', encoding='utf-8') as f:
                        trade_logs = json.load(f)
                else:
                    trade_logs = []
                
                # 创建交易日志条目
                trade_entry = {
                    'timestamp': datetime.now().isoformat(),
                    'event_type': event_type,
                    'symbol': symbol,
                    'details': details
                }
                trade_logs.append(trade_entry) # 修正: 将 trade_log 改为 trade_entry
                
                # 保持最近1000条交易日志
                if len(trade_logs) > 1000:
                    trade_logs = trade_logs[-1000:]
                
                with open(trade_log_file, 'w', encoding='utf-8') as f:
                    json.dump(trade_logs, f, ensure_ascii=False, indent=2)
                    
            except Exception as e:
                self.logger.error(f"保存交易日志失败: {e}", exc_info=True)
                
        except Exception as e:
            self.logger.error(f"记录交易事件失败: {e}", exc_info=True)
    
    def get_log_statistics(self) -> Dict[str, Any]:
        """获取日志统计信息"""
        try:
            current_time = datetime.now()
            with self.lock: # 保护对 self.log_stats, self.alert_status, self.recent_logs, self.performance_logs 的访问
                uptime = current_time - self.log_stats['start_time']
                
                stats = self.log_stats.copy()
                stats['uptime_seconds'] = uptime.total_seconds()
                stats['uptime_formatted'] = str(uptime)
                stats['recent_log_count'] = len(self.recent_logs)
                stats['performance_log_count'] = len(self.performance_logs)
                stats['alert_count'] = self.alert_status['alert_count']
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取日志统计失败: {e}", exc_info=True)
            return {}
    
    def get_recent_logs(self, count: int = 50, level: str = None) -> List[Dict[str, Any]]:
        """获取最近的日志"""
        try:
            with self.lock: # 保护对 self.recent_logs 的访问
                logs = self.recent_logs.copy()
            
            # 按级别过滤
            if level:
                logs = [log for log in logs if log['level'] == level.upper()]
            
            # 按时间排序并返回最近的
            logs.sort(key=lambda x: x['timestamp'], reverse=True)
            return logs[:count]
            
        except Exception as e:
            self.logger.error(f"获取最近日志失败: {e}", exc_info=True)
            return []


class LogMonitorHandler(logging.Handler):
    """自定义日志处理器，用于监控日志"""
    
    def __init__(self, monitor: LoggerMonitor):
        super().__init__()
        self.monitor = monitor
    
    def emit(self, record: logging.LogRecord) -> None:
        """处理日志记录"""
        try:
            with self.monitor.lock: # 使用锁保护共享数据
                # 更新统计
                self.monitor.log_stats['total_logs'] += 1
                
                level = record.levelname
                if level == 'ERROR':
                    self.monitor.log_stats['error_count'] += 1
                    self.monitor.log_stats['last_error'] = {
                        'timestamp': datetime.now().isoformat(),
                        'message': record.getMessage(),
                        'module': record.module
                    }
                elif level == 'WARNING':
                    self.monitor.log_stats['warning_count'] += 1
                    self.monitor.log_stats['last_warning'] = {
                        'timestamp': datetime.now().isoformat(),
                        'message': record.getMessage(),
                        'module': record.module
                    }
                elif level == 'INFO':
                    self.monitor.log_stats['info_count'] += 1
                elif level == 'DEBUG':
                    self.monitor.log_stats['debug_count'] += 1
                
                # 添加到最近日志
                log_entry = {
                    'timestamp': datetime.fromtimestamp(record.created).isoformat(),
                    'level': level,
                    'module': record.module,
                    'message': record.getMessage(),
                    'filename': record.filename,
                    'lineno': record.lineno
                }
                
                self.monitor.recent_logs.append(log_entry)
                
                # 保持最近日志数量限制
                if len(self.monitor.recent_logs) > self.monitor.max_recent_logs:
                    self.monitor.recent_logs = self.monitor.recent_logs[-self.monitor.max_recent_logs:]
                
                # 添加到缓冲区
                try:
                    self.monitor.log_buffer.put_nowait(log_entry)
                except queue.Full:
                    pass  # 缓冲区满了就忽略
                    
        except Exception as e:
            # 将 print 语句改为 logger.error
            self.monitor.logger.error(f"日志监控处理失败: {e}", exc_info=True)


# 全局统一日志设置标记
_unified_logging_setup = False

# 全局统一日志设置函数
def setup_unified_logging(config_manager=None):
    """
    统一日志设置函数 - 确保整个系统使用统一的日志格式
    
    这个函数应该在系统启动时调用一次，之后所有模块的logger都会自动使用统一格式
    
    Args:
        config_manager: 配置管理器实例（可选）
    
    Returns:
        LoggerMonitor实例
    """
    global _unified_logging_setup
    import logging
    import sys
    
    # 如果已经设置过，避免重复配置
    if _unified_logging_setup:
        return None
    
    # 获取根logger
    root_logger = logging.getLogger()
    
    # 清除现有handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 设置日志级别
    root_logger.setLevel(logging.INFO)
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    
    # 设置统一的格式
    formatter = logging.Formatter(
        '%(asctime)s.%(msecs)03d-%(levelname)s-%(name)s-%(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    console_handler.setFormatter(formatter)
    
    # 添加处理器到根日志记录器
    root_logger.addHandler(console_handler)
    
    # 标记已设置
    console_handler._unified_logging_marker = True
    _unified_logging_setup = True
    
    # 记录初始化完成
    logger = logging.getLogger(__name__)
    logger.info("统一日志系统初始化完成")
    
    # 如果需要LoggerMonitor，可以另外创建
    if config_manager is not None:
        try:
            logger_monitor = LoggerMonitor(config_manager)
            return logger_monitor
        except Exception as e:
            logger.error(f"创建LoggerMonitor失败: {e}")
            return None
    
    return None


def get_unified_logger(name):
    """
    获取统一格式的logger
    
    Args:
        name: logger名称（通常使用 __name__）
    
    Returns:
        配置好的logger实例
    """
    return logging.getLogger(name)
