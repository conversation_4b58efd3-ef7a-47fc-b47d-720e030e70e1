#!/usr/bin/env python3
"""
添加API凭证到数据库
"""

import sys
import os
import sqlite3
from datetime import datetime

def add_okx_credentials():
    """添加OKX API凭证"""
    try:
        print("=== 添加OKX API凭证 ===")
        
        # 获取用户输入
        print("请输入您的OKX API凭证:")
        api_key = input("API Key: ").strip()
        api_secret = input("API Secret: ").strip()
        passphrase = input("Passphrase: ").strip()
        
        if not all([api_key, api_secret, passphrase]):
            print("❌ 所有字段都是必需的！")
            return False
        
        # 连接数据库
        db_path = 'credentials.db'
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查是否已存在OKX凭证
        cursor.execute("SELECT id FROM credentials WHERE service_name = ?", ('okx',))
        existing = cursor.fetchone()
        
        current_time = datetime.now().isoformat()
        
        if existing:
            # 更新现有凭证
            cursor.execute("""
                UPDATE credentials 
                SET api_key = ?, api_secret = ?, passphrase = ?, updated_at = ?
                WHERE service_name = ?
            """, (api_key, api_secret, passphrase, current_time, 'okx'))
            print("✅ OKX凭证已更新")
        else:
            # 插入新凭证
            cursor.execute("""
                INSERT INTO credentials (service_name, api_key, api_secret, passphrase, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?)
            """, ('okx', api_key, api_secret, passphrase, current_time, current_time))
            print("✅ OKX凭证已添加")
        
        conn.commit()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 添加凭证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_credentials():
    """测试凭证读取"""
    try:
        print("\n=== 测试凭证读取 ===")
        
        sys.path.insert(0, 'src')
        from src.config_manager import ConfigManager
        
        config = ConfigManager()
        
        # 测试读取凭证
        api_key = config.get_credential('okx', 'api_key')
        api_secret = config.get_credential('okx', 'api_secret')
        passphrase = config.get_credential('okx', 'passphrase')
        
        print(f"API Key: {'✅ 已读取' if api_key else '❌ 未读取'}")
        print(f"API Secret: {'✅ 已读取' if api_secret else '❌ 未读取'}")
        print(f"Passphrase: {'✅ 已读取' if passphrase else '❌ 未读取'}")
        
        if all([api_key, api_secret, passphrase]):
            print("🎉 所有凭证读取成功！")
            return True
        else:
            print("❌ 部分凭证读取失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试凭证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_okx_init():
    """测试OKX初始化"""
    try:
        print("\n=== 测试OKX初始化 ===")
        
        sys.path.insert(0, 'src')
        from src.core.unified_trading_service import UnifiedTradingService
        from src.config_manager import ConfigManager
        
        config = ConfigManager()
        service = UnifiedTradingService(config, test_mode=False)
        
        # 检查OKX客户端状态
        print(f"Market API: {'✅ 已初始化' if hasattr(service, 'market_api') and service.market_api else '❌ 未初始化'}")
        print(f"Account API: {'✅ 已初始化' if hasattr(service, 'account_api') and service.account_api else '❌ 未初始化'}")
        print(f"Trade API: {'✅ 已初始化' if hasattr(service, 'trade_api') and service.trade_api else '❌ 未初始化'}")
        
        if (hasattr(service, 'account_api') and service.account_api and
            hasattr(service, 'trade_api') and service.trade_api):
            print("🎉 OKX API初始化成功！")
            return True
        else:
            print("❌ OKX API初始化失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试OKX初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔑 OKX API凭证配置工具")
    print("=" * 50)
    
    while True:
        print("\n请选择操作:")
        print("1. 添加/更新OKX API凭证")
        print("2. 测试凭证读取")
        print("3. 测试OKX初始化")
        print("4. 退出")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == '1':
            add_okx_credentials()
        elif choice == '2':
            test_credentials()
        elif choice == '3':
            test_okx_init()
        elif choice == '4':
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重试")

if __name__ == "__main__":
    main()
