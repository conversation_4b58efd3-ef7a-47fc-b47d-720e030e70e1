<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepSeek加密货币量化交易系统</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        
        /* 导航栏 */
        .navbar { background: rgba(255,255,255,0.95); backdrop-filter: blur(10px); border-radius: 15px; padding: 15px 30px; margin-bottom: 30px; box-shadow: 0 8px 32px rgba(0,0,0,0.1); }
        .navbar h1 { color: #2c3e50; margin-bottom: 10px; font-size: 24px; }
        .navbar p { color: #7f8c8d; margin-bottom: 15px; }
        .nav-tabs { display: flex; gap: 10px; flex-wrap: wrap; }
        .nav-tab { padding: 10px 20px; border: none; border-radius: 25px; cursor: pointer; font-weight: 500; transition: all 0.3s; }
        .nav-tab.active { background: #3498db; color: white; }
        .nav-tab:not(.active) { background: #ecf0f1; color: #2c3e50; }
        .nav-tab:hover:not(.active) { background: #bdc3c7; }
        
        /* 内容区域 */
        .content { display: none; }
        .content.active { display: block; }
        .card { background: rgba(255,255,255,0.95); backdrop-filter: blur(10px); border-radius: 15px; padding: 25px; margin-bottom: 20px; box-shadow: 0 8px 32px rgba(0,0,0,0.1); }
        .card h3 { color: #2c3e50; margin-bottom: 20px; font-size: 18px; }
        
        /* 表单样式 */
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 8px; color: #2c3e50; font-weight: 500; }
        .form-group input, .form-group select { width: 100%; padding: 12px; border: 2px solid #ecf0f1; border-radius: 8px; font-size: 14px; transition: border-color 0.3s; }
        .form-group input:focus, .form-group select:focus { outline: none; border-color: #3498db; }
        
        /* 按钮样式 */
        .btn { padding: 12px 24px; border: none; border-radius: 8px; cursor: pointer; font-weight: 500; transition: all 0.3s; margin: 5px; }
        .btn.primary { background: #3498db; color: white; }
        .btn.success { background: #27ae60; color: white; }
        .btn.danger { background: #e74c3c; color: white; }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 4px 12px rgba(0,0,0,0.2); }
        
        /* 状态指示器 */
        .status { display: inline-block; padding: 6px 12px; border-radius: 20px; color: white; font-size: 12px; font-weight: 500; }
        .status.running { background: #27ae60; }
        .status.stopped { background: #e74c3c; }
        .status.warning { background: #f39c12; }
        
        /* 网格布局 */
        .grid-2 { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        
        /* 消息提示 */
        .message { padding: 15px; border-radius: 8px; margin: 15px 0; }
        .message.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .message.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .message.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        
        /* 新增样式 */
        .loading {
            text-align: center;
            color: #7f8c8d;
            padding: 20px;
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .btn.btn-secondary {
            background: #95a5a6;
            color: white;
            padding: 8px 16px;
            font-size: 12px;
        }

        .instruments-table, .positions-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        .instruments-table th, .instruments-table td,
        .positions-table th, .positions-table td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .instruments-table th, .positions-table th {
            background: #f8f9fa;
            font-weight: 600;
            font-size: 12px;
            color: #666;
        }

        .instruments-table td, .positions-table td {
            font-size: 13px;
        }

        .position-long {
            color: #27ae60;
            font-weight: 600;
        }

        .position-short {
            color: #e74c3c;
            font-weight: 600;
        }

        .pnl-positive {
            color: #27ae60;
            font-weight: 600;
        }

        .pnl-negative {
            color: #e74c3c;
            font-weight: 600;
        }

        .instrument-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .instrument-symbol {
            font-weight: 600;
            color: #2c3e50;
        }

        .instrument-type {
            font-size: 11px;
            color: #7f8c8d;
            background: #ecf0f1;
            padding: 2px 6px;
            border-radius: 3px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container { padding: 10px; }
            .navbar { padding: 15px; }
            .nav-tabs { justify-content: center; }
            .grid-2 { grid-template-columns: 1fr; }
        }

        /* 模态框样式 */
        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background-color: #fff;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            max-width: 400px;
            width: 90%;
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-header {
            padding: 20px 20px 10px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            color: #2c3e50;
            font-size: 18px;
        }

        .modal-header .close {
            font-size: 24px;
            font-weight: bold;
            color: #aaa;
            cursor: pointer;
            line-height: 1;
            transition: color 0.3s;
        }

        .modal-header .close:hover {
            color: #e74c3c;
        }

        .modal-body {
            padding: 20px;
        }

        .modal-body p {
            margin: 0;
            color: #555;
            line-height: 1.5;
            font-size: 16px;
        }

        .modal-footer {
            padding: 10px 20px 20px;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .modal .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
        }

        .modal .btn-secondary {
            background-color: #95a5a6;
            color: white;
        }

        .modal .btn-secondary:hover {
            background-color: #7f8c8d;
            transform: translateY(-1px);
        }

        .modal .btn-primary {
            background-color: #3498db;
            color: white;
        }

        .modal .btn-primary:hover {
            background-color: #2980b9;
            transform: translateY(-1px);
        }

        .modal .btn-danger {
            background-color: #e74c3c;
            color: white;
        }

        .modal .btn-danger:hover {
            background-color: #c0392b;
            transform: translateY(-1px);
        }

        /* 消息提示动画 */
        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100%);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideOutRight {
            from {
                opacity: 1;
                transform: translateX(0);
            }
            to {
                opacity: 0;
                transform: translateX(100%);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 导航栏 -->
        <div class="navbar">
            <h1>🤖 DeepSeek加密货币量化交易系统</h1>
            <p>AI驱动的全自动合约交易平台 - 24x7无人值守量化交易</p>
            <div class="nav-tabs">
                <button class="nav-tab active" onclick="showTab('dashboard')">📊 交易仪表盘</button>
                <button class="nav-tab" onclick="showTab('trading')">💰 交易监控</button>
                <button class="nav-tab" onclick="showTab('strategy')">⚙️ 策略配置</button>
                <button class="nav-tab" onclick="showTab('history')">📋 交易历史</button>
                <button class="nav-tab" onclick="showTab('logs')">📝 系统日志</button>
                <button class="nav-tab" onclick="showTab('config')">🔑 API配置</button>
            </div>
        </div>

        <!-- 交易仪表盘 -->
        <div id="dashboard" class="content active">
            <div class="grid-2">
                <div class="card">
                    <h3>📊 系统概览</h3>
                    <div id="system-overview">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 15px;">
                            <div>
                                <div>运行状态: <span id="system-status" class="status running">运行中</span></div>
                                <div>运行时间: <span id="uptime">--</span></div>
                            </div>
                            <div>
                                <button class="btn success" onclick="startTrading()">▶️ 启动交易</button>
                                <button class="btn danger" onclick="stopTrading()">⏹️ 停止交易</button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <h3>💰 账户概览</h3>
                    <div id="account-overview">
                        <div>总资产: <span id="total-balance">--</span> USDT</div>
                        <div>可用余额: <span id="available-balance">--</span> USDT</div>
                        <div>今日盈亏: <span id="daily-pnl">--</span> USDT</div>
                        <div>总盈亏: <span id="total-pnl">--</span> USDT</div>
                    </div>
                </div>
            </div>

            <!-- 移除热门交易对和当前持仓，简化首页 -->

            <div class="card">
                <div class="card-header" style="display: flex; justify-content: space-between; align-items: center;">
                    <h3>💼 当前持仓</h3>
                    <div style="display: flex; gap: 10px;">
                        <button onclick="loadPositions()" class="btn btn-secondary">刷新</button>
                        <button onclick="closeAllPositions()" class="btn" style="background: #e74c3c; color: white; border: none;">🔄 一键平仓</button>
                    </div>
                </div>
                <div style="overflow-x: auto;">
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead>
                            <tr style="background-color: #f8f9fa;">
                                <th style="padding: 10px; text-align: left;">交易对</th>
                                <th style="padding: 10px; text-align: left;">方向</th>
                                <th style="padding: 10px; text-align: left;">数量</th>
                                <th style="padding: 10px; text-align: left;">杠杆</th>
                                <th style="padding: 10px; text-align: left;">入场价</th>
                                <th style="padding: 10px; text-align: left;">当前价</th>
                                <th style="padding: 10px; text-align: left;">盈亏</th>
                                <th style="padding: 10px; text-align: left;">保证金</th>
                            </tr>
                        </thead>
                        <tbody id="positions-table-body">
                            <tr>
                                <td colspan="8" style="padding: 20px; text-align: center; color: #7f8c8d;">暂无持仓</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 交易监控页面 -->
        <div id="trading" class="content">
            <div class="card">
                <h3>💰 实时交易监控</h3>
                <div id="trading-monitor">
                    <div class="grid-2">
                        <div>
                            <h4>📊 交易统计</h4>
                            <div>总交易次数: <span id="total-trades">--</span></div>
                            <div>成功交易: <span id="successful-trades">--</span></div>
                            <div>成功率: <span id="success-rate">--</span>%</div>
                            <div>平均收益: <span id="avg-profit">--</span> USDT</div>
                        </div>
                        <div>
                            <h4>⚠️ 风险监控</h4>
                            <div>风险等级: <span id="risk-level" class="status">--</span></div>
                            <div>最大回撤: <span id="max-drawdown">--</span>%</div>
                            <div>当前杠杆: <span id="current-leverage">--</span>x</div>
                            <div>保证金率: <span id="margin-ratio">--</span>%</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <h3>📊 最近交易信号</h3>
                <div id="recent-signals">
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead>
                            <tr style="background: #f8f9fa;">
                                <th style="padding: 10px; text-align: left;">时间</th>
                                <th style="padding: 10px; text-align: left;">交易对</th>
                                <th style="padding: 10px; text-align: left;">信号类型</th>
                                <th style="padding: 10px; text-align: left;">价格</th>
                                <th style="padding: 10px; text-align: left;">数量</th>
                                <th style="padding: 10px; text-align: left;">状态</th>
                            </tr>
                        </thead>
                        <tbody id="signals-table-body">
                            <tr>
                                <td colspan="6" style="padding: 20px; text-align: center; color: #7f8c8d;">暂无交易信号</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 策略配置页面 -->
        <div id="strategy" class="content">
            <div class="grid-2">
                <div class="card">
                    <h3>⚙️ 交易策略配置</h3>
                    <div class="form-group">
                        <label>交易对选择 <span style="color: #7f8c8d; font-size: 12px;">(可多选，Ctrl+点击)</span></label>
                        <div style="position: relative;">
                            <select id="trading-pairs" multiple style="height: 120px; width: 100%;">
                                <option disabled style="color: #7f8c8d;">正在加载交易对...</option>
                            </select>
                            <button type="button" onclick="loadTradingPairs()" class="btn btn-secondary" style="position: absolute; top: 5px; right: 35px; padding: 5px 10px; font-size: 12px;">🔄 刷新</button>
                            <button type="button" onclick="clearSelectedPairs()" class="btn btn-secondary" style="position: absolute; top: 5px; right: 5px; padding: 5px 10px; font-size: 12px;">🗑️ 清空</button>
                        </div>
                        <div style="margin-top: 5px; font-size: 12px; color: #7f8c8d;">
                            已选择: <span id="selected-pairs-count">0</span> 个交易对
                            <span style="margin-left: 10px; color: #3498db;">💡 提示：按住Ctrl键可多选</span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>最大投资比例 (%)</label>
                        <input type="number" id="investment-ratio" value="5" min="1" max="50" step="0.1">
                        <small style="color: #7f8c8d;">占总可用资金的百分比，如5%表示最多使用5%的资金进行单次交易</small>
                    </div>
                    <div class="form-group">
                        <label>最大杠杆倍数</label>
                        <input type="number" id="max-leverage" value="3" min="1" max="10">
                    </div>
                    <button class="btn primary" onclick="saveStrategyConfig()">💾 保存策略配置</button>
                </div>

                <div class="card">
                    <h3>🛡️ 风险管理配置</h3>
                    <div class="form-group">
                        <label>止损比例 (%)</label>
                        <input type="number" id="stop-loss" value="5" min="1" max="20" step="0.1">
                    </div>
                    <div class="form-group">
                        <label>止盈比例 (%)</label>
                        <input type="number" id="take-profit" value="10" min="1" max="50" step="0.1">
                    </div>
                    <div class="form-group">
                        <label>最大持仓数量</label>
                        <input type="number" id="max-positions" value="3" min="1" max="10">
                    </div>
                    <div class="form-group">
                        <label>日最大亏损 (USDT)</label>
                        <input type="number" id="daily-loss-limit" value="500" min="50">
                    </div>
                    <button class="btn primary" onclick="saveRiskConfig()">🛡️ 保存风险配置</button>
                </div>
            </div>
        </div>

        <!-- 交易历史页面 -->
        <div id="history" class="content">
            <div class="card">
                <h3>📋 交易历史记录</h3>
                <div style="margin-bottom: 20px;">
                    <button class="btn primary" onclick="loadTradeHistory()">🔄 刷新数据</button>
                    <button class="btn" onclick="exportTradeHistory()">📤 导出Excel</button>
                </div>
                <div id="trade-history">
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead>
                            <tr style="background: #f8f9fa;">
                                <th style="padding: 10px; text-align: left;">时间</th>
                                <th style="padding: 10px; text-align: left;">交易对</th>
                                <th style="padding: 10px; text-align: left;">方向</th>
                                <th style="padding: 10px; text-align: left;">开仓价</th>
                                <th style="padding: 10px; text-align: left;">平仓价</th>
                                <th style="padding: 10px; text-align: left;">数量</th>
                                <th style="padding: 10px; text-align: left;">盈亏</th>
                                <th style="padding: 10px; text-align: left;">手续费</th>
                            </tr>
                        </thead>
                        <tbody id="history-table-body">
                            <tr>
                                <td colspan="8" style="padding: 20px; text-align: center; color: #7f8c8d;">暂无交易历史</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 系统日志页面 -->
        <div id="logs" class="content">
            <div class="card">
                <h3>📝 系统运行日志</h3>
                <div style="margin-bottom: 20px;">
                    <button class="btn primary" onclick="loadSystemLogs()">🔄 刷新日志</button>
                    <button class="btn" onclick="clearLogs()">🗑️ 清空日志</button>
                    <select id="log-level">
                        <option value="ALL">所有级别</option>
                        <option value="ERROR">错误</option>
                        <option value="WARNING">警告</option>
                        <option value="INFO">信息</option>
                    </select>
                </div>
                <div id="system-logs" style="background: #2c3e50; color: #ecf0f1; padding: 15px; border-radius: 8px; height: 400px; overflow-y: auto; font-family: 'Courier New', monospace; font-size: 12px;">
                    <div>等待日志数据...</div>
                </div>
            </div>
        </div>

        <!-- API配置页面 -->
        <div id="config" class="content">
            <div class="card">
                <h3>🔑 API密钥配置</h3>
                <p style="color: #7f8c8d; margin-bottom: 20px;">请配置您的OKX和DeepSeek API密钥，这是系统运行的必要条件。</p>
                
                <div class="grid-2">
                    <div>
                        <div class="form-group">
                            <label for="okx-api-key">OKX API Key</label>
                            <input type="text" id="okx-api-key" placeholder="请输入OKX API Key">
                        </div>
                        <div class="form-group">
                            <label for="okx-secret-key">OKX Secret Key</label>
                            <input type="password" id="okx-secret-key" placeholder="请输入OKX Secret Key">
                        </div>
                        <div class="form-group">
                            <label for="okx-passphrase">OKX Passphrase</label>
                            <input type="password" id="okx-passphrase" placeholder="请输入OKX Passphrase">
                        </div>
                    </div>
                    <div>
                        <div class="form-group">
                            <label for="deepseek-api-key">DeepSeek API Key</label>
                            <input type="password" id="deepseek-api-key" placeholder="请输入DeepSeek API Key">
                        </div>
                        <div class="form-group">
                            <label for="trading-mode">交易模式</label>
                            <select id="trading-mode">
                                <option value="1">模拟交易</option>
                                <option value="0">实盘交易</option>
                            </select>
                        </div>
                        <div style="margin-top: 20px;">
                            <button class="btn success" onclick="saveApiConfig()">💾 保存配置</button>
                            <button class="btn primary" onclick="testApiConnection()">🔗 测试连接</button>
                        </div>
                    </div>
                </div>
                
                <!-- 状态显示区域 -->
                <div id="api-status"></div>
            </div>
        </div>
        
        <!-- 系统监控页面 -->
        <div id="monitor" class="content">
            <div class="card">
                <h3>📈 系统状态</h3>
                <div id="system-status">加载中...</div>
                <div style="margin-top: 15px;">
                    <button class="btn success" onclick="startSystem()">▶️ 启动系统</button>
                    <button class="btn danger" onclick="stopSystem()">⏹️ 停止系统</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 确认弹窗模态框 -->
    <div id="confirmModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="confirmTitle">确认操作</h3>
                <span class="close" onclick="closeConfirmModal()">&times;</span>
            </div>
            <div class="modal-body">
                <p id="confirmMessage">确定要执行此操作吗？</p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeConfirmModal()">取消</button>
                <button class="btn btn-primary" id="confirmBtn" onclick="confirmAction()">确定</button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let confirmCallback = null;

        // 模态框函数
        function showConfirm(title, message, callback, buttonText = '确定', buttonClass = 'btn-primary') {
            document.getElementById('confirmTitle').textContent = title;
            document.getElementById('confirmMessage').textContent = message;
            document.getElementById('confirmBtn').textContent = buttonText;
            document.getElementById('confirmBtn').className = 'btn ' + buttonClass;
            confirmCallback = callback;
            document.getElementById('confirmModal').style.display = 'flex';
        }

        function closeConfirmModal() {
            document.getElementById('confirmModal').style.display = 'none';
            confirmCallback = null;
        }

        function confirmAction() {
            if (confirmCallback) {
                confirmCallback();
            }
            closeConfirmModal();
        }

        // 点击模态框外部关闭
        document.addEventListener('click', function(event) {
            const modal = document.getElementById('confirmModal');
            if (event.target === modal) {
                closeConfirmModal();
            }
        });

        // ESC键关闭模态框
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeConfirmModal();
            }
        });

        // 消息提示函数
        function showMessage(message, type = 'info') {
            // 创建消息元素
            const messageDiv = document.createElement('div');
            messageDiv.className = `message-toast message-${type}`;
            messageDiv.textContent = message;

            // 添加样式
            messageDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 8px;
                color: white;
                font-weight: 500;
                z-index: 1001;
                max-width: 400px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                animation: slideInRight 0.3s ease-out;
            `;

            // 根据类型设置背景色
            switch(type) {
                case 'success':
                    messageDiv.style.backgroundColor = '#27ae60';
                    break;
                case 'error':
                    messageDiv.style.backgroundColor = '#e74c3c';
                    break;
                case 'warning':
                    messageDiv.style.backgroundColor = '#f39c12';
                    break;
                default:
                    messageDiv.style.backgroundColor = '#3498db';
            }

            // 添加到页面
            document.body.appendChild(messageDiv);

            // 3秒后自动移除
            setTimeout(() => {
                messageDiv.style.animation = 'slideOutRight 0.3s ease-in';
                setTimeout(() => {
                    if (messageDiv.parentNode) {
                        messageDiv.parentNode.removeChild(messageDiv);
                    }
                }, 300);
            }, 3000);
        }

        // 加载持仓信息
        function loadPositions() {
            fetch('/api/positions')
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById('positions-container');
                    if (!container) {
                        console.warn('positions-container元素不存在，跳过更新');
                        return;
                    }

                    if (data.positions && data.positions.length > 0) {
                        let html = `
                            <table class="positions-table">
                                <thead>
                                    <tr>
                                        <th>交易对</th>
                                        <th>方向</th>
                                        <th>数量</th>
                                        <th>均价</th>
                                        <th>标记价</th>
                                        <th>未实现盈亏</th>
                                        <th>杠杆</th>
                                    </tr>
                                </thead>
                                <tbody>
                        `;

                        data.positions.forEach(pos => {
                            const sideClass = pos.side === 'long' ? 'position-long' : 'position-short';
                            const pnlClass = parseFloat(pos.unrealized_pnl) >= 0 ? 'pnl-positive' : 'pnl-negative';

                            html += `
                                <tr>
                                    <td>${pos.symbol}</td>
                                    <td class="${sideClass}">${pos.side.toUpperCase()}</td>
                                    <td>${pos.size}</td>
                                    <td>$${pos.avg_price}</td>
                                    <td>$${pos.mark_price}</td>
                                    <td class="${pnlClass}">$${pos.unrealized_pnl} (${pos.unrealized_pnl_ratio})</td>
                                    <td>${pos.leverage}</td>
                                </tr>
                            `;
                        });

                        html += '</tbody></table>';
                        container.innerHTML = html;
                    } else {
                        container.innerHTML = '<div style="text-align: center; color: #7f8c8d; padding: 20px;">暂无持仓</div>';
                    }
                })
                .catch(error => {
                    console.error('加载持仓信息失败:', error);
                    const container = document.getElementById('positions-container');
                    if (container) {
                        container.innerHTML = '<div style="text-align: center; color: #e74c3c; padding: 20px;">加载失败</div>';
                    }
                });
        }

        // 加载交易对列表
        function loadInstruments() {
            fetch('/api/instruments?type=popular')
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById('instruments-container');
                    if (!container) {
                        console.warn('instruments-container元素不存在，跳过更新');
                        return;
                    }

                    if (data.instruments && data.instruments.length > 0) {
                        let html = `
                            <table class="instruments-table">
                                <thead>
                                    <tr>
                                        <th>交易对</th>
                                        <th>类型</th>
                                        <th>基础币种</th>
                                        <th>计价币种</th>
                                        <th>状态</th>
                                    </tr>
                                </thead>
                                <tbody>
                        `;

                        // 只显示前15个热门交易对
                        data.instruments.slice(0, 15).forEach(inst => {
                            const stateColor = inst.state === 'live' ? '#27ae60' : '#e74c3c';

                            html += `
                                <tr>
                                    <td class="instrument-symbol">${inst.symbol}</td>
                                    <td><span class="instrument-type">${inst.type}</span></td>
                                    <td>${inst.base_currency}</td>
                                    <td>${inst.quote_currency}</td>
                                    <td style="color: ${stateColor};">${inst.state}</td>
                                </tr>
                            `;
                        });

                        html += '</tbody></table>';
                        container.innerHTML = html;
                    } else {
                        container.innerHTML = '<div style="text-align: center; color: #7f8c8d; padding: 20px;">暂无交易对数据</div>';
                    }
                })
                .catch(error => {
                    console.error('加载交易对列表失败:', error);
                    const container = document.getElementById('instruments-container');
                    if (container) {
                        container.innerHTML = '<div style="text-align: center; color: #e74c3c; padding: 20px;">加载失败</div>';
                    }
                });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，初始化JavaScript');
            console.log('🔧 开发模式：启用热重载功能');

            // 重新启用数据加载
            loadDashboardData();  // 包含持仓信息更新
            loadApiConfig();

            // 监听交易对选择变化
            const tradingPairsSelect = document.getElementById('trading-pairs');
            if (tradingPairsSelect) {
                tradingPairsSelect.addEventListener('change', updateSelectedPairsCount);
            }

            // 启用自动刷新（每3秒刷新一次）
            setInterval(loadDashboardData, 3000);  // 包含持仓信息的仪表盘数据

            // 开发模式：检测页面变化并自动刷新
            if (window.location.hostname === '127.0.0.1' || window.location.hostname === 'localhost') {
                console.log('🔧 开发模式：启用前端热重载检测');
                startHotReloadDetection();
            }
        });
        
        // 标签页切换
        function showTab(tabName) {
            console.log('切换到标签页:', tabName);
            
            // 隐藏所有内容
            document.querySelectorAll('.content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有标签的active状态
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中的内容
            document.getElementById(tabName).classList.add('active');
            
            // 设置对应标签为active
            event.target.classList.add('active');
            
            // 根据标签页加载对应数据
            if (tabName === 'dashboard') {
                loadDashboardData();
            } else if (tabName === 'trading') {
                loadTradingData();
            } else if (tabName === 'strategy') {
                loadStrategyConfig();
            } else if (tabName === 'history') {
                loadTradeHistory();
            } else if (tabName === 'logs') {
                loadSystemLogs();
            }
        }
        
        // API配置相关功能
        function loadApiConfig() {
            console.log('加载API配置状态');
            fetch('/api/config')
                .then(response => response.json())
                .then(data => {
                    console.log('API配置数据:', data);
                    const okxConfigured = data.credentials && data.credentials.okx;
                    const deepseekConfigured = data.credentials && data.credentials.deepseek;
                    updateApiStatus(okxConfigured, deepseekConfigured);
                })
                .catch(error => {
                    console.error('加载API配置失败:', error);
                    showMessage('加载配置失败: ' + error.message, 'error');
                });
        }
        
        function saveApiConfig() {
            console.log('=== 开始保存API配置 ===');

            const okxApiKey = document.getElementById('okx-api-key').value.trim();
            const okxSecretKey = document.getElementById('okx-secret-key').value.trim();
            const okxPassphrase = document.getElementById('okx-passphrase').value.trim();
            const deepseekApiKey = document.getElementById('deepseek-api-key').value.trim();
            const tradingMode = document.getElementById('trading-mode').value;

            console.log('获取到的表单数据:', {
                okxApiKey: okxApiKey ? '***已填写***' : '空',
                okxSecretKey: okxSecretKey ? '***已填写***' : '空',
                okxPassphrase: okxPassphrase ? '***已填写***' : '空',
                deepseekApiKey: deepseekApiKey ? '***已填写***' : '空',
                tradingMode: tradingMode
            });

            // 验证必填字段
            if (!okxApiKey || !okxSecretKey || !okxPassphrase || !deepseekApiKey) {
                console.log('验证失败：存在空字段');
                showMessage('请填写所有必填字段', 'error');
                return;
            }

            console.log('字段验证通过，准备构建请求数据');
            
            const configData = {
                credentials: {
                    okx: {
                        api_key: okxApiKey,
                        api_secret: okxSecretKey,
                        passphrase: okxPassphrase,
                        flag: tradingMode
                    },
                    deepseek: {
                        api_key: deepseekApiKey
                    }
                }
            };
            
            console.log('准备发送POST请求到 /api/config');
            console.log('请求数据:', JSON.stringify(configData, null, 2));
            showMessage('正在保存配置...', 'info');

            console.log('开始发送fetch请求...');
            fetch('/api/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(configData)
            })
            .then(response => {
                console.log('收到响应:', response);
                console.log('响应状态:', response.status);
                console.log('响应状态文本:', response.statusText);

                if (!response.ok) {
                    throw new Error(`HTTP错误! 状态: ${response.status}`);
                }

                return response.json();
            })
            .then(data => {
                console.log('解析后的响应数据:', data);
                if (data.success) {
                    console.log('保存成功！');
                    showMessage('API配置保存成功！', 'success');
                    // 清空输入框（安全考虑）
                    document.getElementById('okx-api-key').value = '';
                    document.getElementById('okx-secret-key').value = '';
                    document.getElementById('okx-passphrase').value = '';
                    document.getElementById('deepseek-api-key').value = '';

                    updateApiStatus(true, true);
                } else {
                    console.log('保存失败:', data.message);
                    showMessage('保存失败: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('=== 请求失败 ===');
                console.error('错误类型:', error.name);
                console.error('错误消息:', error.message);
                console.error('完整错误:', error);
                showMessage('保存失败，请检查网络连接: ' + error.message, 'error');
            });
        }
        
        function testApiConnection() {
            console.log('开始测试API连接');
            showMessage('正在测试API连接，请稍候...', 'info');

            fetch('/api/test-connection', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => {
                console.log('测试响应:', response);
                if (!response.ok) {
                    throw new Error(`HTTP错误! 状态: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('测试结果:', data);
                displayTestResults(data);
            })
            .catch(error => {
                console.error('API连接测试失败:', error);
                showMessage('连接测试失败: ' + error.message, 'error');
            });
        }

        function displayTestResults(results) {
            let message = '🔍 API连接测试结果:\n\n';

            // OKX API测试结果
            if (results.okx) {
                if (results.okx.success) {
                    message += '✅ OKX API: 连接成功\n';
                    if (results.okx.details) {
                        message += `   服务器时间: ${results.okx.details}\n`;
                    }
                } else {
                    message += '❌ OKX API: 连接失败\n';
                    message += `   错误: ${results.okx.error}\n`;
                }
            }

            message += '\n';

            // DeepSeek API测试结果
            if (results.deepseek) {
                if (results.deepseek.success) {
                    message += '✅ DeepSeek API: 连接成功\n';
                    if (results.deepseek.details) {
                        message += `   响应: ${results.deepseek.details}\n`;
                    }
                } else {
                    message += '❌ DeepSeek API: 连接失败\n';
                    message += `   错误: ${results.deepseek.error}\n`;
                }
            }

            // 显示结果
            const success = (results.okx && results.okx.success) && (results.deepseek && results.deepseek.success);
            showMessage(message, success ? 'success' : 'error');
        }
        
        function updateApiStatus(okxConfigured, deepseekConfigured) {
            const statusDiv = document.getElementById('api-status');
            let statusHtml = '<div style="margin-top: 20px;">';
            statusHtml += '<h4>配置状态：</h4>';
            statusHtml += '<div style="display: flex; gap: 20px; margin-top: 10px;">';
            statusHtml += `<div>OKX API: <span class="status ${okxConfigured ? 'running' : 'stopped'}">${okxConfigured ? '已配置' : '未配置'}</span></div>`;
            statusHtml += `<div>DeepSeek API: <span class="status ${deepseekConfigured ? 'running' : 'stopped'}">${deepseekConfigured ? '已配置' : '未配置'}</span></div>`;
            statusHtml += '</div></div>';
            statusDiv.innerHTML = statusHtml;
        }
        
        function showMessage(message, type) {
            const statusDiv = document.getElementById('api-status');
            const messageClass = type === 'success' ? 'success' : type === 'error' ? 'error' : 'info';
            statusDiv.innerHTML = `<div class="message ${messageClass}">${message}</div>`;
            
            // 如果是成功或错误消息，3秒后恢复状态显示
            if (type === 'success' || type === 'error') {
                setTimeout(() => {
                    loadApiConfig();
                }, 3000);
            }
        }
        
        function loadSystemStatus() {
            console.log('加载系统状态');
            fetch('/api/system/status')
                .then(response => response.json())
                .then(data => {
                    const statusHtml = `
                        <p>运行状态: <span class="status ${data.system_running ? 'running' : 'stopped'}">${data.system_running ? '运行中' : '已停止'}</span></p>
                        <p>活跃交易对: ${data.active_contracts}</p>
                        <p>风险等级: <span class="status ${data.risk_level.toLowerCase()}">${data.risk_level}</span></p>
                        <p>总交易次数: ${data.total_trades}</p>
                        <p>成功率: ${(data.success_rate * 100).toFixed(1)}%</p>
                    `;
                    document.getElementById('system-status').innerHTML = statusHtml;
                })
                .catch(error => {
                    console.error('加载系统状态失败:', error);
                    document.getElementById('system-status').innerHTML = '<p class="message error">加载失败</p>';
                });
        }
        
        function startSystem() {
            console.log('启动系统');
            fetch('/api/system/start', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    alert(data.message);
                    loadSystemStatus();
                })
                .catch(error => {
                    console.error('启动系统失败:', error);
                    alert('启动失败: ' + error.message);
                });
        }
        
        function stopSystem() {
            console.log('停止系统');
            fetch('/api/system/stop', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    alert(data.message);
                    loadSystemStatus();
                })
                .catch(error => {
                    console.error('停止系统失败:', error);
                    alert('停止失败: ' + error.message);
                });
        }

        // 交易仪表盘相关函数
        function loadDashboardData() {
            console.log('加载仪表盘数据');
            Promise.all([
                fetch('/api/system/status').then(r => r.json()),
                fetch('/api/account/balance').then(r => r.json()),
                fetch('/api/positions').then(r => r.json())
            ]).then(([systemData, accountData, positionsData]) => {
                updateSystemOverview(systemData);
                updateAccountOverview(accountData);
                updatePositionsTable(positionsData.positions || []);
            }).catch(error => {
                console.error('加载仪表盘数据失败:', error);
            });
        }

        function updateSystemOverview(data) {
            document.getElementById('system-status').textContent = data.system_running ? '运行中' : '已停止';
            document.getElementById('system-status').className = `status ${data.system_running ? 'running' : 'stopped'}`;
            document.getElementById('uptime').textContent = data.uptime || '--';
        }

        function updateAccountOverview(data) {
            document.getElementById('total-balance').textContent = data.total_balance || '--';
            document.getElementById('available-balance').textContent = data.available_balance || '--';
            document.getElementById('daily-pnl').textContent = data.daily_pnl || '--';
            document.getElementById('total-pnl').textContent = data.total_pnl || '--';
        }

        function updatePositionsTable(positions) {
            const tbody = document.getElementById('positions-table-body');
            if (!tbody) {
                console.warn('持仓表格元素不存在，跳过更新');
                return;
            }

            if (positions && Array.isArray(positions) && positions.length > 0) {
                tbody.innerHTML = positions.map(position => {
                    const side = position.side === 'long' ? '多头' : '空头';
                    const sideColor = position.side === 'long' ? '#27ae60' : '#e74c3c';
                    const pnlValue = parseFloat(position.unrealized_pnl) || 0;
                    const pnlColor = pnlValue >= 0 ? '#27ae60' : '#e74c3c';

                    return `
                        <tr>
                            <td style="padding: 10px;">${position.symbol}</td>
                            <td style="padding: 10px; color: ${sideColor};">${side}</td>
                            <td style="padding: 10px;">${position.size}</td>
                            <td style="padding: 10px; color: #3498db; font-weight: 600;">${position.leverage}</td>
                            <td style="padding: 10px;">$${position.avg_price}</td>
                            <td style="padding: 10px;">$${position.mark_price}</td>
                            <td style="padding: 10px; color: ${pnlColor};">$${position.unrealized_pnl}</td>
                            <td style="padding: 10px;">$${position.margin}</td>
                        </tr>
                    `;
                }).join('');
            } else {
                tbody.innerHTML = '<tr><td colspan="8" style="padding: 20px; text-align: center; color: #7f8c8d;">暂无持仓</td></tr>';
            }
        }

        // 一键平仓功能
        function closeAllPositions() {
            showConfirm(
                '⚠️ 一键平仓确认',
                '确定要平掉所有持仓吗？此操作不可撤销！',
                function() {
                    // 显示加载状态
                    showMessage('正在执行一键平仓...', 'info');

                    fetch('/api/trade/close-all-positions', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showMessage(data.message, 'success');

                            // 显示详细结果
                            if (data.data && data.data.total_positions > 0) {
                                const details = data.data;
                                let detailMessage = `
                                    📊 平仓详情：
                                    • 总持仓数：${details.total_positions}
                                    • 成功平仓：${details.successful_closes}
                                    • 失败平仓：${details.failed_closes}
                                `;

                                if (details.failed_closes > 0) {
                                    detailMessage += '\n\n❌ 失败的持仓：';
                                    details.failed_positions.forEach(pos => {
                                        detailMessage += `\n• ${pos.symbol}: ${pos.error}`;
                                    });
                                }

                                showMessage(detailMessage, details.failed_closes > 0 ? 'warning' : 'success');
                            }

                            // 刷新持仓数据
                            setTimeout(() => {
                                loadDashboardData();
                            }, 1000);
                        } else {
                            showMessage(data.message || '一键平仓失败', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('一键平仓请求失败:', error);
                        showMessage('一键平仓请求失败，请检查网络连接', 'error');
                    });
                }
            );
        }

        function startTrading() {
            showConfirm(
                '启动自动交易',
                '确定要启动自动交易吗？系统将开始执行AI驱动的量化交易策略。',
                function() {
                    fetch('/api/trading/start', { method: 'POST' })
                        .then(response => response.json())
                        .then(data => {
                            showMessage(data.message, data.success ? 'success' : 'error');
                            loadDashboardData();
                        })
                        .catch(error => {
                            console.error('启动交易失败:', error);
                            showMessage('启动失败: ' + error.message, 'error');
                        });
                },
                '启动交易',
                'btn-primary'
            );
        }

        function stopTrading() {
            showConfirm(
                '停止自动交易',
                '确定要停止自动交易吗？当前的交易策略将被暂停。',
                function() {
                    fetch('/api/trading/stop', { method: 'POST' })
                        .then(response => response.json())
                        .then(data => {
                            showMessage(data.message, data.success ? 'success' : 'error');
                            loadDashboardData();
                        })
                        .catch(error => {
                            console.error('停止交易失败:', error);
                            showMessage('停止失败: ' + error.message, 'error');
                        });
                },
                '停止交易',
                'btn-danger'
            );
        }

        function loadTradingData() {
            console.log('加载交易监控数据');
            Promise.all([
                fetch('/api/trading/statistics').then(r => r.json()),
                fetch('/api/trading/signals').then(r => r.json())
            ]).then(([statsData, signalsData]) => {
                updateTradingStats(statsData);
                updateRecentSignals(signalsData);
            }).catch(error => {
                console.error('加载交易数据失败:', error);
            });
        }

        function updateTradingStats(data) {
            document.getElementById('total-trades').textContent = data.total_trades || '--';
            document.getElementById('successful-trades').textContent = data.successful_trades || '--';
            document.getElementById('success-rate').textContent = data.success_rate || '--';
            document.getElementById('avg-profit').textContent = data.avg_profit || '--';
            document.getElementById('risk-level').textContent = data.risk_level || '--';
            document.getElementById('max-drawdown').textContent = data.max_drawdown || '--';
            document.getElementById('current-leverage').textContent = data.current_leverage || '--';
            document.getElementById('margin-ratio').textContent = data.margin_ratio || '--';
        }

        function updateRecentSignals(data) {
            const tbody = document.getElementById('signals-table-body');
            if (!tbody) {
                console.warn('交易信号表格元素不存在，跳过更新');
                return;
            }

            if (data.signals && data.signals.length > 0) {
                tbody.innerHTML = data.signals.map(signal => `
                    <tr>
                        <td style="padding: 10px;">${signal.time}</td>
                        <td style="padding: 10px;">${signal.symbol}</td>
                        <td style="padding: 10px;">${signal.type}</td>
                        <td style="padding: 10px;">${signal.price}</td>
                        <td style="padding: 10px;">${signal.quantity}</td>
                        <td style="padding: 10px;"><span class="status ${signal.status.toLowerCase()}">${signal.status}</span></td>
                    </tr>
                `).join('');
            } else {
                tbody.innerHTML = '<tr><td colspan="6" style="padding: 20px; text-align: center; color: #7f8c8d;">暂无交易信号</td></tr>';
            }
        }

        // 加载可用的交易对
        function loadTradingPairs() {
            console.log('加载交易对列表');
            const select = document.getElementById('trading-pairs');
            select.innerHTML = '<option disabled style="color: #7f8c8d;">正在加载交易对...</option>';

            fetch('/api/instruments')
                .then(response => response.json())
                .then(data => {
                    if (data.instruments && Array.isArray(data.instruments)) {
                        select.innerHTML = '';

                        // 只显示SWAP合约
                        const swapInstruments = data.instruments.filter(inst =>
                            inst.type === 'SWAP' && inst.symbol.includes('USDT')
                        );

                        swapInstruments.forEach(instrument => {
                            const option = document.createElement('option');
                            option.value = instrument.symbol;
                            option.textContent = `${instrument.symbol} - ${instrument.base_currency}`;
                            select.appendChild(option);
                        });

                        console.log(`加载了 ${swapInstruments.length} 个SWAP交易对`);

                        // 恢复之前的选择状态
                        setTimeout(() => {
                            restoreSelectedPairs();
                        }, 100);
                    } else {
                        console.error('API返回数据格式错误:', data);
                        select.innerHTML = '<option disabled style="color: #e74c3c;">加载失败，请重试</option>';
                    }
                })
                .catch(error => {
                    console.error('加载交易对失败:', error);
                    select.innerHTML = '<option disabled style="color: #e74c3c;">加载失败，请重试</option>';
                });
        }

        // 更新已选择的交易对数量
        function updateSelectedPairsCount() {
            const select = document.getElementById('trading-pairs');
            const selectedCount = select.selectedOptions.length;
            document.getElementById('selected-pairs-count').textContent = selectedCount;

            // 保存选择状态到localStorage
            const selectedValues = Array.from(select.selectedOptions).map(option => option.value);
            localStorage.setItem('selectedTradingPairs', JSON.stringify(selectedValues));
        }

        // 恢复交易对选择状态
        function restoreSelectedPairs() {
            try {
                const savedPairs = localStorage.getItem('selectedTradingPairs');
                if (savedPairs) {
                    const selectedValues = JSON.parse(savedPairs);
                    const select = document.getElementById('trading-pairs');

                    // 恢复选择状态
                    for (let option of select.options) {
                        if (selectedValues.includes(option.value)) {
                            option.selected = true;
                        }
                    }
                    updateSelectedPairsCount();
                    console.log(`恢复了 ${selectedValues.length} 个交易对的选择状态`);
                }
            } catch (error) {
                console.error('恢复交易对选择状态失败:', error);
            }
        }

        // 清空交易对选择
        function clearSelectedPairs() {
            const select = document.getElementById('trading-pairs');
            for (let option of select.options) {
                option.selected = false;
            }
            updateSelectedPairsCount();
            showMessage('已清空所有交易对选择', 'info');
        }

        function loadStrategyConfig() {
            console.log('加载策略配置');

            fetch('/api/strategy/config')
                .then(response => response.json())
                .then(data => {
                    console.log('策略配置数据:', data);

                    if (data.trading_pairs) {
                        document.getElementById('investment-ratio').value = data.investment_ratio || 5;
                        document.getElementById('max-leverage').value = data.max_leverage || 3;

                        // 保存当前配置的交易对到localStorage，以便恢复选择
                        if (data.trading_pairs.active_pairs) {
                            localStorage.setItem('selectedTradingPairs', JSON.stringify(data.trading_pairs.active_pairs));
                            console.log('保存了活跃交易对到localStorage:', data.trading_pairs.active_pairs);
                        }
                    }
                    if (data.risk_management) {
                        document.getElementById('stop-loss').value = data.stop_loss || 5;
                        document.getElementById('take-profit').value = data.take_profit || 10;
                        document.getElementById('max-positions').value = data.max_positions || 3;
                        document.getElementById('daily-loss-limit').value = data.daily_loss_limit || 500;
                    }

                    // 加载交易对列表（会自动恢复选择状态）
                    loadTradingPairs();
                })
                .catch(error => {
                    console.error('加载策略配置失败:', error);
                    // 即使配置加载失败，也要加载交易对列表
                    loadTradingPairs();
                });
        }

        function saveStrategyConfig() {
            const tradingPairsSelect = document.getElementById('trading-pairs');
            const selectedPairs = Array.from(tradingPairsSelect.selectedOptions).map(o => o.value);

            // 验证是否选择了交易对
            if (selectedPairs.length === 0) {
                alert('⚠️ 请至少选择一个交易对！\n\n如果交易对列表为空，请点击"🔄 刷新"按钮重新加载。');
                return;
            }

            const config = {
                trading_pairs: selectedPairs,
                investment_ratio: parseFloat(document.getElementById('investment-ratio').value),
                max_leverage: parseInt(document.getElementById('max-leverage').value)
            };

            console.log('保存策略配置:', config);

            fetch('/api/strategy/config', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(config)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('✅ ' + data.message);
                } else {
                    alert('❌ ' + data.message);
                }
            })
            .catch(error => {
                console.error('保存策略配置失败:', error);
                alert('保存失败: ' + error.message);
            });
        }

        function saveRiskConfig() {
            const config = {
                stop_loss: parseFloat(document.getElementById('stop-loss').value),
                take_profit: parseFloat(document.getElementById('take-profit').value),
                max_positions: parseInt(document.getElementById('max-positions').value),
                daily_loss_limit: parseFloat(document.getElementById('daily-loss-limit').value)
            };

            console.log('保存风险配置:', config);

            fetch('/api/risk/config', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(config)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('✅ ' + data.message);
                } else {
                    alert('❌ ' + data.message);
                }
            })
            .catch(error => {
                console.error('保存风险配置失败:', error);
                alert('保存失败: ' + error.message);
            });
        }

        function loadTradeHistory() {
            console.log('加载交易历史');
            fetch('/api/trading/history')
                .then(response => response.json())
                .then(data => {
                    const tbody = document.getElementById('history-table-body');
                    if (!tbody) {
                        console.warn('交易历史表格元素不存在，跳过更新');
                        return;
                    }

                    if (data.trades && data.trades.length > 0) {
                        tbody.innerHTML = data.trades.map(trade => `
                            <tr>
                                <td style="padding: 10px;">${trade.time}</td>
                                <td style="padding: 10px;">${trade.symbol}</td>
                                <td style="padding: 10px;">${trade.side}</td>
                                <td style="padding: 10px;">${trade.open_price}</td>
                                <td style="padding: 10px;">${trade.close_price}</td>
                                <td style="padding: 10px;">${trade.quantity}</td>
                                <td style="padding: 10px; color: ${trade.pnl >= 0 ? '#27ae60' : '#e74c3c'};">${trade.pnl}</td>
                                <td style="padding: 10px;">${trade.fee}</td>
                            </tr>
                        `).join('');
                    } else {
                        tbody.innerHTML = '<tr><td colspan="8" style="padding: 20px; text-align: center; color: #7f8c8d;">暂无交易历史</td></tr>';
                    }
                })
                .catch(error => {
                    console.error('加载交易历史失败:', error);
                });
        }

        function exportTradeHistory() {
            window.open('/api/trading/export', '_blank');
        }

        function loadSystemLogs() {
            console.log('加载系统日志');
            const level = document.getElementById('log-level').value;
            fetch(`/api/logs?level=${level}`)
                .then(response => response.json())
                .then(data => {
                    const logsDiv = document.getElementById('system-logs');
                    if (data.logs && data.logs.length > 0) {
                        logsDiv.innerHTML = data.logs.map(log =>
                            `<div style="margin-bottom: 5px;">[${log.time}] ${log.level}: ${log.message}</div>`
                        ).join('');
                        logsDiv.scrollTop = logsDiv.scrollHeight;
                    } else {
                        logsDiv.innerHTML = '<div>暂无日志数据</div>';
                    }
                })
                .catch(error => {
                    console.error('加载系统日志失败:', error);
                });
        }

        function clearLogs() {
            showConfirm(
                '清空系统日志',
                '确定要清空所有日志吗？此操作不可撤销，将删除所有历史日志记录。',
                function() {
                    fetch('/api/logs/clear', { method: 'POST' })
                        .then(response => response.json())
                        .then(data => {
                            showMessage(data.message, data.success ? 'success' : 'error');
                            loadSystemLogs();
                        })
                        .catch(error => {
                            console.error('清空日志失败:', error);
                            showMessage('清空失败: ' + error.message, 'error');
                        });
                },
                '清空日志',
                'btn-danger'
            );
        }

        // 热重载检测功能
        function startHotReloadDetection() {
            let lastModified = null;

            function checkForUpdates() {
                fetch('/api/dev/reload-status')
                    .then(response => response.json())
                    .then(data => {
                        if (lastModified && data.last_modified && lastModified !== data.last_modified) {
                            console.log('🔄 检测到文件变化，准备重新加载页面...');
                            // 显示重载提示
                            const notification = document.createElement('div');
                            notification.style.cssText = `
                                position: fixed; top: 20px; right: 20px; z-index: 9999;
                                background: #4CAF50; color: white; padding: 15px 20px;
                                border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                                font-family: Arial, sans-serif; font-size: 14px;
                            `;
                            notification.textContent = '🔄 检测到代码变化，正在重新加载...';
                            document.body.appendChild(notification);

                            setTimeout(() => {
                                window.location.reload();
                            }, 1500);
                        }
                        lastModified = data.last_modified;
                    })
                    .catch(error => {
                        console.log('热重载检测失败:', error);
                    });
            }

            // 每3秒检测一次文件变化
            setInterval(checkForUpdates, 3000);
            console.log('🔧 前端热重载检测已启动（每3秒检测一次）');
        }
    </script>
</body>
</html>
