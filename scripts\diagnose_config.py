#!/usr/bin/env python3
"""
配置诊断脚本
检查API密钥配置问题
"""

import sys
import os

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, current_dir)
sys.path.insert(0, src_dir)

def diagnose_config():
    """诊断配置问题"""
    try:
        from src.config_manager import ConfigManager
        
        print("=== 配置诊断 ===")
        config = ConfigManager()
        
        # 检查配置文件内容
        print("1. 检查配置文件:")
        print(f"   配置文件路径: {config.db_path}")
        print(f"   配置文件存在: {os.path.exists(config.db_path)}")
        
        # 检查配置内容
        print("2. 检查配置内容:")
        if hasattr(config, 'config'):
            print(f"   配置对象: {type(config.config)}")
            print(f"   配置键: {list(config.config.keys())}")
            
            if 'api_credentials' in config.config:
                creds = config.config['api_credentials']
                print(f"   API凭证: {list(creds.keys()) if creds else '无'}")
                if 'okx' in creds:
                    okx_creds = creds['okx']
                    print(f"   OKX凭证键: {list(okx_creds.keys())}")
                    # 检查值是否存在（不显示实际值）
                    for key in ['api_key', 'api_secret', 'passphrase']:
                        value = okx_creds.get(key)
                        print(f"     {key}: {'已设置' if value else '未设置'}")
            else:
                print("   未找到 api_credentials 配置")
        
        # 检查环境变量
        print("3. 检查环境变量:")
        env_keys = ['OKX_API_KEY', 'OKX_API_SECRET', 'OKX_PASSPHRASE']
        for key in env_keys:
            value = os.getenv(key)
            print(f"   {key}: {'已设置' if value else '未设置'}")
        
        # 测试 get_credential 方法
        print("4. 测试 get_credential 方法:")
        api_key = config.get_credential('okx', 'api_key')
        api_secret = config.get_credential('okx', 'api_secret')
        passphrase = config.get_credential('okx', 'passphrase')
        
        print(f"   API Key: {'已获取' if api_key else '未获取'}")
        print(f"   API Secret: {'已获取' if api_secret else '未获取'}")
        print(f"   Passphrase: {'已获取' if passphrase else '未获取'}")
        
        # 检查.env文件
        print("5. 检查.env文件:")
        env_file = os.path.join(current_dir, '.env')
        print(f"   .env文件存在: {os.path.exists(env_file)}")
        if os.path.exists(env_file):
            with open(env_file, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = [line.strip() for line in content.split('\n') if line.strip() and not line.startswith('#')]
                print(f"   .env文件行数: {len(lines)}")
                for line in lines:
                    if '=' in line:
                        key = line.split('=')[0]
                        print(f"     {key}: 已定义")
        
        return True
        
    except Exception as e:
        print(f"配置诊断失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def diagnose_okx_init():
    """诊断OKX初始化问题"""
    try:
        from src.core.unified_trading_service import UnifiedTradingService
        from src.config_manager import ConfigManager
        
        print("\n=== OKX初始化诊断 ===")
        
        config = ConfigManager()
        
        # 检查UnifiedTradingService的初始化过程
        print("1. 创建UnifiedTradingService...")
        service = UnifiedTradingService(config, test_mode=False)
        
        # 检查OKX客户端状态
        print("2. 检查OKX客户端状态:")
        print(f"   Market API: {'已初始化' if hasattr(service, 'market_api') and service.market_api else '未初始化'}")
        print(f"   Account API: {'已初始化' if hasattr(service, 'account_api') and service.account_api else '未初始化'}")
        print(f"   Trade API: {'已初始化' if hasattr(service, 'trade_api') and service.trade_api else '未初始化'}")
        
        # 检查初始化过程中的错误
        print("3. 检查初始化错误:")
        if hasattr(service, 'account_api') and service.account_api is None:
            print("   Account API初始化失败 - 可能是凭证问题")
        
        return True
        
    except Exception as e:
        print(f"OKX初始化诊断失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success1 = diagnose_config()
    success2 = diagnose_okx_init()
    
    if success1 and success2:
        print("\n=== 诊断完成 ===")
        print("请检查上述输出，找出配置问题所在")
    else:
        print("\n=== 诊断失败 ===")
        print("请检查错误信息")
