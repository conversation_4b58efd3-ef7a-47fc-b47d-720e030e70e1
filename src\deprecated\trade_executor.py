"""
交易执行模块 - Trade Executor
负责执行交易决策，包括下单、平仓、止盈止损等操作
基于python-okx库实现与OKX交易所的交互，支持异步操作
"""

import os

# 强制禁用HTTP/2，解决SSL/TLS协议错误
# 必须在所有网络库（如httpx, requests）被导入之前执行
os.environ['HTTPX_HTTP2'] = '0'

import time
import asyncio
from typing import Dict, List, Optional, Any, Tuple
import logging
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor
from okx.Trade import TradeAPI
from okx.Account import AccountAPI
from okx.PublicData import PublicAPI
from ..config_manager import ConfigManager

class TradeExecutor:
    """交易执行器 - 执行具体的交易操作"""
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化交易执行器

        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)

        # 合约精度缓存
        self._precision_cache = {}

        # 初始化交易API（如果有配置）
        self.trade_api = None
        self.account_api = None
        try:
            self._init_trading_apis()
        except Exception as e:
            self.logger.warning(f"交易API初始化失败，将在配置完成后重新初始化: {e}")

        # 交易记录
        self.trade_history = []
        self.pending_orders = {}

        # 盈亏跟踪
        self.initial_balance = None  # 初始余额，用于计算总盈亏
        self.daily_start_balance = None  # 今日开始余额，用于计算今日盈亏
        self.last_balance_update = None  # 上次余额更新时间

        # 交易配置
        self.max_retries = 3
        self.retry_delay = 1

        # 异步执行器
        self.executor = ThreadPoolExecutor(max_workers=4)

        # 市场API（用于获取价格）
        self.market_api = None
        # 公共数据API（用于获取合约精度）
        self.public_api = None
        try:
            okx_config = self.config_manager.get('credentials.okx')
            if okx_config:
                from okx.MarketData import MarketAPI
                self.market_api = MarketAPI(
                    api_key=okx_config['api_key'],
                    api_secret_key=okx_config['api_secret'],
                    passphrase=okx_config['passphrase'],
                    flag='1',
                    domain='https://www.okx.com'
                )

                # 初始化公共数据API
                self.public_api = PublicAPI(
                    api_key=okx_config['api_key'],
                    api_secret_key=okx_config['api_secret'],
                    passphrase=okx_config['passphrase'],
                    flag='1',
                    domain='https://www.okx.com'
                )
        except Exception as e:
            self.logger.warning(f"API初始化失败: {e}")

        self.logger.info("交易执行器初始化完成")

    def _get_contract_precision(self, symbol: str) -> Dict[str, float]:
        """
        获取合约精度信息

        Args:
            symbol: 合约符号，如 'BTC-USDT-SWAP'

        Returns:
            包含精度信息的字典: {'lot_sz': 0.01, 'tick_sz': 0.1, 'min_sz': 0.01}
        """
        if symbol in self._precision_cache:
            return self._precision_cache[symbol]

        try:
            if not self.public_api:
                self.logger.warning("公共数据API未初始化，使用默认精度")
                return {'lot_sz': 0.01, 'tick_sz': 0.1, 'min_sz': 0.01}

            response = self.public_api.get_instruments(instType='SWAP', instId=symbol)

            if response['code'] == '0' and response['data']:
                instrument = response['data'][0]
                precision = {
                    'lot_sz': float(instrument.get('lotSz', '0.01')),
                    'tick_sz': float(instrument.get('tickSz', '0.1')),
                    'min_sz': float(instrument.get('minSz', '0.01'))
                }

                # 缓存精度信息
                self._precision_cache[symbol] = precision
                self.logger.debug(f"获取合约精度: {symbol} -> {precision}")
                return precision
            else:
                self.logger.warning(f"获取合约精度失败: {symbol}, 使用默认精度")
                return {'lot_sz': 0.01, 'tick_sz': 0.1, 'min_sz': 0.01}

        except Exception as e:
            self.logger.error(f"获取合约精度异常: {symbol}, {e}")
            return {'lot_sz': 0.01, 'tick_sz': 0.1, 'min_sz': 0.01}

    def _format_size_precision(self, size: float, symbol: str) -> str:
        """
        根据合约精度格式化张数

        Args:
            size: 原始张数
            symbol: 合约符号

        Returns:
            格式化后的张数字符串
        """
        precision = self._get_contract_precision(symbol)
        lot_sz = precision['lot_sz']

        if lot_sz >= 1:
            # 整数精度
            formatted_size = int(size)
        else:
            # 小数精度：按照lotSz的倍数进行舍入
            decimal_places = len(str(lot_sz).split('.')[1]) if '.' in str(lot_sz) else 0
            formatted_size = round(size / lot_sz) * lot_sz
            formatted_size = round(formatted_size, decimal_places)

        return str(formatted_size)

    def _safe_float(self, value, default=0.0):
        """安全转换为float，处理空字符串和None"""
        try:
            if value is None or value == '':
                return default
            return float(value)
        except (ValueError, TypeError):
            self.logger.warning(f"无法转换为float: {value}, 使用默认值: {default}")
            return default

    def _round_price_precision(self, price: float, symbol: str) -> float:
        """根据交易对的实际tickSz精度调整价格"""
        try:
            # 获取交易对的精度信息
            tick_sz = self._get_instrument_tick_size(symbol)
            if tick_sz:
                # 根据tickSz计算小数位数
                decimal_places = self._get_decimal_places(tick_sz)
                return round(price, decimal_places)
            else:
                # 如果无法获取tickSz，使用价格范围确定合适的精度
                if price >= 10000:  # BTC等高价币种
                    return round(price, 2)  # 保留2位小数
                elif price >= 1000:  # ETH等中价币种
                    return round(price, 2)  # 保留2位小数
                elif price >= 1:  # 一般币种
                    return round(price, 4)  # 保留4位小数
                elif price >= 0.1:  # DOGE等低价币种
                    return round(price, 5)  # 保留5位小数
                else:  # 极低价币种
                    return round(price, 6)  # 保留6位小数
        except Exception as e:
            self.logger.warning(f"价格精度调整失败: {price}, {symbol} - {e}")
            return round(price, 5)  # 默认保留5位小数

    def _get_instrument_tick_size(self, symbol: str) -> float:
        """获取交易对的tickSz"""
        try:
            # 直接使用OKX API获取交易对信息
            from okx.PublicData import PublicAPI
            public_api = PublicAPI(flag='0')  # 实盘

            # 获取交易对信息
            result = public_api.get_instruments(instType='SWAP')
            if result['code'] == '0':
                for inst in result['data']:
                    if inst['instId'] == symbol:
                        tick_sz = inst.get('tickSz', '')
                        if tick_sz:
                            return float(tick_sz)
            return None
        except Exception as e:
            self.logger.warning(f"获取{symbol}的tickSz失败: {e}")
            return None

    def _get_decimal_places(self, tick_sz: float) -> int:
        """根据tickSz计算小数位数"""
        try:
            # 将tickSz转换为字符串，计算小数位数
            tick_str = f"{tick_sz:.10f}".rstrip('0').rstrip('.')
            if '.' in tick_str:
                return len(tick_str.split('.')[1])
            else:
                return 0
        except Exception:
            return 5  # 默认5位小数

    def _get_price_precision(self, symbol: str) -> int:
        """获取交易对的价格精度（小数位数）- 增加重试机制"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                if not self.public_api:
                    self.logger.warning("公共数据API未初始化，使用默认精度")
                    return 5

                response = self.public_api.get_instruments(instType='SWAP', instId=symbol)

                if response['code'] == '0' and response['data']:
                    instrument = response['data'][0]
                    tick_sz = instrument.get('tickSz', '')
                    if tick_sz:
                        return self._get_decimal_places(float(tick_sz))
                    else:
                        self.logger.warning(f"无法从API获取{symbol}的tickSz，使用默认精度")
                        return 5
                else:
                    # 如果API返回错误代码，也进行重试
                    raise Exception(f"API返回错误: {response.get('msg', '未知错误')}")

            except Exception as e:
                self.logger.warning(f"获取{symbol}价格精度失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(1 * (attempt + 1))  # 指数退避
                else:
                    self.logger.error(f"获取{symbol}价格精度最终失败，使用默认精度5")
                    return 5
        return 5  # Fallback

    def _set_leverage_if_needed(self, symbol: str, target_leverage: int):
        """根据需要设置杠杆倍数"""
        try:
            # 获取当前杠杆
            current_leverage = self._get_current_leverage(symbol)

            if current_leverage != target_leverage:
                self.logger.info(f"设置杠杆: {symbol} {current_leverage}x -> {target_leverage}x")

                # 设置杠杆
                response = self.account_api.set_leverage(
                    instId=symbol,
                    lever=str(target_leverage),
                    mgnMode='cross'  # 全仓模式
                )

                if response['code'] != '0':
                    raise Exception(f"设置杠杆失败: {response.get('msg', 'Unknown error')}")

                self.logger.info(f"杠杆设置成功: {symbol} = {target_leverage}x")
            else:
                self.logger.debug(f"杠杆已是目标值: {symbol} = {target_leverage}x")

        except Exception as e:
            self.logger.warning(f"设置杠杆失败: {symbol} - {e}")
            # 不抛出异常，继续交易流程

    def _get_current_leverage(self, symbol: str) -> int:
        """获取当前杠杆倍数 - 增加重试机制"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                response = self.account_api.get_positions(instId=symbol)
                if response['code'] == '0':
                    if response.get('data'):
                        for pos in response['data']:
                            if pos['instId'] == symbol:
                                lever_str = pos.get('lever', '1')
                                if lever_str and lever_str.strip():
                                    return int(float(lever_str))
                    # 没有持仓或没有杠杆信息，返回1
                    return 1
                raise Exception(f"API返回错误: {response.get('msg', 'Unknown error')}")
            except Exception as e:
                self.logger.warning(f"获取当前杠杆失败 (尝试 {attempt + 1}/{max_retries}): {symbol} - {e}")
                if attempt < max_retries - 1:
                    time.sleep(1 * (attempt + 1))
                else:
                    self.logger.error(f"获取当前杠杆最终失败，返回默认值1: {symbol}")
                    return 1
        return 1 # Fallback

    def _init_trading_apis(self) -> None:
        """初始化交易API - 优化连接稳定性和超时配置"""
        try:
            okx_config = self.config_manager.get('credentials.okx')
            if not okx_config:
                raise ValueError("OKX API配置不存在，请通过Web界面配置")

            # 多域名配置策略（与data_fetcher保持一致）
            self.api_domains = [
                'https://www.okx.com',
                'https://aws.okx.com',
                'https://okx.com',
                'https://www.okex.com'
            ]
            self.current_domain_index = 0
            
            # 使用第一个域名初始化
            domain = self.api_domains[self.current_domain_index]
            self.logger.info(f"🔗 初始化交易API - 使用域名: {domain}")

            # 直接创建API实例（库的原生方式）
            self.trade_api = TradeAPI(
                api_key=okx_config['api_key'],
                api_secret_key=okx_config['api_secret'],
                passphrase=okx_config['passphrase'],
                flag='1',  # 1: 模拟环境, 0: 实盘环境
                domain=domain
            )

            self.account_api = AccountAPI(
                api_key=okx_config['api_key'],
                api_secret_key=okx_config['api_secret'],
                passphrase=okx_config['passphrase'],
                flag='1',  # 1: 模拟环境, 0: 实盘环境
                domain=domain
            )

            # 添加市场API用于获取价格
            from okx.MarketData import MarketAPI
            self.market_api = MarketAPI(
                api_key=okx_config['api_key'],
                api_secret_key=okx_config['api_secret'],
                passphrase=okx_config['passphrase'],
                flag='1',  # 1: 模拟环境, 0: 实盘环境
                domain=domain
            )

            # 优化HTTP客户端连接池和超时配置
            self._configure_enhanced_timeouts()

            # 测试连接健康状态
            if self._test_connection_health():
                self.logger.info("✅ 交易API初始化完成，连接健康检查通过")
            else:
                self.logger.warning("⚠️ 交易API初始化完成，但连接健康检查失败")

        except Exception as e:
            self.logger.error(f"❌ 交易API初始化失败: {e}")
            raise

    def _configure_enhanced_timeouts(self) -> None:
        """增强型API客户端超时和连接池配置"""
        try:
            import httpx

            # 优化的超时配置 - 参考data_fetcher的成功实践
            timeout_config = httpx.Timeout(
                connect=15.0,   # 连接超时：15秒
                read=45.0,      # 读取超时：45秒
                write=15.0,     # 写入超时：15秒
                pool=10.0       # 连接池超时：10秒
            )

            # 连接池配置 - 提高并发性能和稳定性
            connection_limits = httpx.Limits(
                max_keepalive_connections=20,  # 最大保持连接数
                max_connections=30,            # 最大连接数
                keepalive_expiry=30.0          # 连接保持时间
            )

            # 应用配置到所有API客户端
            for api_name, api_client in [
                ('trade_api', self.trade_api),
                ('account_api', self.account_api),
                ('market_api', self.market_api)
            ]:
                if api_client and hasattr(api_client, '_client'):
                    try:
                        # 直接配置httpx客户端
                        api_client._client.timeout = timeout_config
                        api_client._client._limits = connection_limits
                        self.logger.debug(f"✅ {api_name}超时配置成功")
                    except Exception as e:
                        self.logger.warning(f"⚠️ {api_name}超时配置失败: {e}")

            self.logger.info("🔧 交易API增强超时配置完成: 连接15s, 读取45s, 连接池30个")

        except Exception as e:
            self.logger.warning(f"⚠️ 配置增强超时失败: {e}")

    def _test_connection_health(self) -> bool:
        """测试API连接健康状态"""
        try:
            # 简单的连接测试 - 获取服务器时间
            if self.trade_api:
                from okx.PublicData import PublicAPI
                public_api = PublicAPI(flag='1')
                result = public_api.get_system_time()
                if result.get('code') == '0':
                    self.logger.debug("🟢 交易API连接健康检查通过")
                    return True
            return False
        except Exception as e:
            self.logger.debug(f"🔴 交易API连接健康检查失败: {e}")
            return False

    def _reinitialize_connection(self) -> bool:
        """重新初始化API连接 - 处理连接断开"""
        try:
            self.logger.info("🔄 重新初始化交易API连接...")
            
            # 尝试切换到下一个域名
            self.current_domain_index = (self.current_domain_index + 1) % len(self.api_domains)
            domain = self.api_domains[self.current_domain_index]
            
            self.logger.info(f"🔗 切换到备用域名: {domain}")
            
            okx_config = self.config_manager.get('credentials.okx')
            if not okx_config:
                raise ValueError("无法获取OKX配置")

            # 重新创建API实例
            self.trade_api = TradeAPI(
                api_key=okx_config['api_key'],
                api_secret_key=okx_config['api_secret'],
                passphrase=okx_config['passphrase'],
                flag='1',
                domain=domain
            )

            self.account_api = AccountAPI(
                api_key=okx_config['api_key'],
                api_secret_key=okx_config['api_secret'],
                passphrase=okx_config['passphrase'],
                flag='1',
                domain=domain
            )

            from okx.MarketData import MarketAPI
            self.market_api = MarketAPI(
                api_key=okx_config['api_key'],
                api_secret_key=okx_config['api_secret'],
                passphrase=okx_config['passphrase'],
                flag='1',
                domain=domain
            )

            # 重新配置超时
            self._configure_enhanced_timeouts()
            
            # 测试新连接
            if self._test_connection_health():
                self.logger.info(f"✅ 连接重新初始化成功: {domain}")
                return True
            else:
                self.logger.warning(f"⚠️ 连接重新初始化后健康检查失败: {domain}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 连接重新初始化失败: {e}")
            return False
    
    def _smart_retry_request(self, api_call, operation_name: str, max_retries: int = 5) -> Optional[Dict[str, Any]]:
        """
        智能重试请求方法 - 处理SSL连接错误和其他网络问题
        
        Args:
            api_call: 要执行的API调用函数
            operation_name: 操作名称（用于日志）
            max_retries: 最大重试次数
            
        Returns:
            API响应结果或None（失败时）
        """
        base_delay = 0.5  # 基础延迟
        
        for attempt in range(max_retries):
            try:
                self.logger.debug(f"🔄 执行API调用: {operation_name} (尝试 {attempt + 1}/{max_retries})")
                
                # 执行API调用
                response = api_call()
                
                # 检查响应
                if response and response.get('code') == '0':
                    if attempt > 0:
                        self.logger.info(f"✅ {operation_name} 重试成功 (尝试 {attempt + 1}/{max_retries})")
                    return response
                else:
                    # API返回错误代码
                    error_msg = response.get('msg', '未知错误') if response else "无响应"
                    
                    # 检查是否是需要特殊处理的错误
                    if self._is_retriable_error(error_msg):
                        if attempt < max_retries - 1:
                            delay = self._calculate_retry_delay(attempt, base_delay)
                            self.logger.warning(f"⚠️ {operation_name} API错误 (可重试): {error_msg}, {delay:.1f}s后重试")
                            time.sleep(delay)
                            continue
                    
                    # 不可重试的错误，直接返回
                    self.logger.error(f"❌ {operation_name} API错误 (不可重试): {error_msg}")
                    return response
                    
            except Exception as e:
                error_str = str(e).lower()
                
                # 检测SSL/连接错误
                is_connection_error = any(keyword in error_str for keyword in [
                    'ssl', 'tls', 'protocol', 'connection', 'timeout', 'network',
                    'server disconnected', 'eof occurred', 'connection refused',
                    'connection reset', 'socket', 'httpx', 'httpcore'
                ])
                
                if is_connection_error:
                    self.logger.warning(f"🔌 {operation_name} 连接错误: {e}")
                    
                    # 每2次连接错误后尝试重新初始化连接
                    if attempt > 0 and attempt % 2 == 0:
                        self.logger.info(f"🔄 尝试重新初始化API连接...")
                        if self._reinitialize_connection():
                            self.logger.info(f"✅ API连接重新初始化成功")
                        else:
                            self.logger.warning(f"⚠️ API连接重新初始化失败")
                    
                    if attempt < max_retries - 1:
                        # 连接错误使用较长的延迟
                        delay = self._calculate_retry_delay(attempt, base_delay * 2, max_delay=10.0)
                        self.logger.info(f"⏳ {operation_name} 连接重试延迟 {delay:.1f}s")
                        time.sleep(delay)
                        continue
                else:
                    # 其他异常
                    self.logger.error(f"❌ {operation_name} 异常: {e}")
                    if attempt < max_retries - 1:
                        delay = self._calculate_retry_delay(attempt, base_delay)
                        self.logger.info(f"⏳ {operation_name} 异常重试延迟 {delay:.1f}s")
                        time.sleep(delay)
                        continue
                
                # 最后一次尝试失败
                if attempt == max_retries - 1:
                    self.logger.error(f"❌ {operation_name} 最终失败: {e}")
                    raise e
        
        # 所有尝试都失败
        self.logger.error(f"❌ {operation_name} 已达到最大重试次数 ({max_retries})")
        return None
    
    def _is_retriable_error(self, error_msg: str) -> bool:
        """判断错误是否可重试"""
        if not error_msg:
            return True
            
        error_lower = error_msg.lower()
        
        # 不可重试的错误
        non_retriable_errors = [
            'insufficient balance',    # 余额不足
            'invalid instrument',      # 无效合约
            'invalid parameter',       # 无效参数
            'position not exist',      # 持仓不存在
            'order not exist',         # 订单不存在
            'invalid signature',       # 签名错误
            'invalid api key',         # API密钥错误
            'invalid passphrase',      # 密码错误
            'permission denied',       # 权限拒绝
            'account restricted',      # 账户受限
        ]
        
        for error_keyword in non_retriable_errors:
            if error_keyword in error_lower:
                return False
        
        # 可重试的错误（服务器临时问题）
        retriable_errors = [
            'timeout',
            'server error',
            'internal error',
            'service unavailable',
            'rate limit',
            'too many requests',
            'network',
            'connection',
            'temporarily unavailable'
        ]
        
        for error_keyword in retriable_errors:
            if error_keyword in error_lower:
                return True
        
        # 默认情况下认为可重试
        return True
    
    def execute_decision(self,
                        symbol: str,
                        decision: Dict[str, Any],
                        current_position: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        执行交易决策
        
        Args:
            symbol: 交易对符号
            decision: AI决策结果
            current_position: 当前持仓信息
            
        Returns:
            执行结果
        """
        try:
            decision_type = decision.get('decision', 'HOLD')
            
            if decision_type == 'HOLD':
                return self._handle_hold_decision(symbol, decision, current_position)
            elif decision_type == 'BUY':
                return self._handle_buy_decision(symbol, decision, current_position)
            elif decision_type == 'SELL':
                return self._handle_sell_decision(symbol, decision, current_position)
            elif decision_type == 'CLOSE':
                return self._handle_close_decision(symbol, decision, current_position)
            elif decision_type == 'CLOSE_AND_REVERSE':
                return self._handle_close_and_reverse_decision(symbol, decision, current_position)
            else:
                raise ValueError(f"未知的决策类型: {decision_type}")
                
        except Exception as e:
            self.logger.error(f"执行交易决策失败: {symbol} - {e}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def _handle_hold_decision(self, 
                             symbol: str,
                             decision: Dict[str, Any],
                             current_position: Dict[str, Any]) -> Dict[str, Any]:
        """处理HOLD决策"""
        try:
            # HOLD决策通常不需要执行交易，但可能需要更新止盈止损
            result = {
                'success': True,
                'action': 'HOLD',
                'symbol': symbol,
                'message': '保持当前仓位',
                'timestamp': datetime.now().isoformat()
            }
            
            # 如果有持仓，检查是否需要更新止盈止损
            if current_position and current_position.get('size', 0) != 0:
                self._update_stop_orders(symbol, current_position, decision)
            
            self.logger.info(f"HOLD决策执行完成: {symbol}")
            return result
            
        except Exception as e:
            self.logger.error(f"处理HOLD决策失败: {e}")
            raise
    
    def _handle_buy_decision(self,
                            symbol: str,
                            decision: Dict[str, Any],
                            current_position: Dict[str, Any]) -> Dict[str, Any]:
        """处理BUY决策（单向持仓：先平空头，再开多头）"""
        try:
            # 检查交易对是否激活
            active_pairs = self.config_manager.get_active_trading_pairs()
            if symbol not in active_pairs:
                raise ValueError(f"交易对{symbol}未激活")

            # 获取交易对配置
            pair_config = active_pairs[symbol]

            # 根据AI决策设置杠杆
            ai_leverage = decision.get('leverage', 3)  # AI选择的杠杆
            self._set_leverage_if_needed(symbol, ai_leverage)

            # 单向持仓逻辑：检查是否有空头仓位需要先平掉
            current_short_size = 0
            if current_position:
                current_size = current_position.get('size', 0)
                current_short_size = abs(current_size) if current_size < 0 else 0

            # 第一步：如果有空头仓位，先平掉（使用python-okx库的close_positions方法）
            if current_short_size > 0:
                self.logger.info(f"检测到空头仓位 {current_short_size}，先执行平仓")

                # 使用python-okx库的原生close_positions方法
                close_response = self.trade_api.close_positions(
                    instId=symbol,
                    mgnMode='cross',  # 全仓模式
                    posSide='short'   # 平空头仓位
                )

                if close_response['code'] != '0':
                    raise Exception(f"平空头仓位失败: {close_response.get('msg', '未知错误')}")

                self.logger.info(f"空头仓位平仓成功")

                # 等待平仓完成
                import time
                time.sleep(2)

            # 第二步：开多头仓位 - 按保证金精确控制
            # AI建议的investment_amount就是目标保证金金额
            target_margin = decision.get('investment_amount', 0)
            if target_margin <= 0:
                return {
                    'success': True,
                    'action': 'BUY_SKIPPED',
                    'symbol': symbol,
                    'message': 'AI建议投资金额为0，跳过执行',
                    'timestamp': datetime.now().isoformat()
                }

            current_price = self._get_current_price(symbol)
            if current_price <= 0:
                raise Exception(f"无法获取{symbol}当前价格")

            leverage = decision.get('leverage', 1)

            # 使用新的基于OKX合约规格的精确计算方法
            calculated_size = self._calculate_contract_size_from_margin(
                symbol=symbol,
                target_margin=target_margin,
                leverage=leverage,
                current_price=current_price
            )

            # 调整精度
            calculated_size = self._adjust_order_size_precision(symbol, calculated_size)

            self.logger.info(f"🔍 最终下单参数: symbol={symbol}, size={calculated_size}, 目标保证金={target_margin} USDT")

            # 按计算出的数量下单（带重试机制）
            order_response = self._place_order_with_retry(
                symbol=symbol,
                side='buy',
                size=calculated_size,
                pos_side='long'
            )

            self.logger.info(f"🔍 下单响应: {order_response}")

            if order_response['code'] != '0':
                raise Exception(f"开多头仓位失败: {order_response.get('msg', '未知错误')}")

            order_id = order_response['data'][0]['ordId']
            self.logger.info(f"多头开仓成功，订单ID: {order_id}")

            # 等待订单完成并获取详情
            import time
            time.sleep(1)
            order_detail = self._get_order_detail(symbol, order_id)

            # 详细日志订单执行结果
            # 安全转换，处理空字符串情况
            filled_size = self._safe_float(order_detail.get('fillSz', 0))
            avg_price = self._safe_float(order_detail.get('avgPx', 0))

            # 对于SWAP合约，需要转换张数为BTC数量
            if 'SWAP' in symbol:
                btc_filled = filled_size * 0.01  # 张数转换为BTC数量
                actual_value = btc_filled * avg_price
            else:
                actual_value = filled_size * avg_price

            actual_margin = actual_value / leverage if leverage > 0 else actual_value

            self.logger.info(f"🔍 订单执行详情:")
            self.logger.info(f"  - 请求数量: {calculated_size}")
            self.logger.info(f"  - 实际成交数量: {filled_size}")
            self.logger.info(f"  - 成交均价: {avg_price}")
            self.logger.info(f"  - 实际成交价值: {actual_value:.2f} USDT")
            self.logger.info(f"  - 杠杆倍数: {leverage}x")
            self.logger.info(f"  - 实际保证金: {actual_margin:.2f} USDT")

            order_result = {
                'success': True,
                'order_id': order_id,
                'avg_price': avg_price,
                'filled_size': filled_size
            }
            
            if order_result['success']:
                # 设置止盈止损订单（使用实际成交数量）
                self._set_stop_orders(symbol, decision, order_result['order_id'], 'BUY', filled_size)
                
                result = {
                    'success': True,
                    'action': 'BUY',
                    'symbol': symbol,
                    'order_id': order_result['order_id'],
                    'size': calculated_size,
                    'price': order_result.get('avg_price', 0),
                    'message': f'买入订单执行成功',
                    'timestamp': datetime.now().isoformat()
                }

                self._record_trade(result)
                self.logger.info(f"BUY决策执行成功: {symbol} 数量={calculated_size}")
                return result
            else:
                raise Exception(f"买入订单失败: {order_result.get('error', '未知错误')}")
                
        except Exception as e:
            self.logger.error(f"处理BUY决策失败: {e}")
            raise
    
    def _handle_sell_decision(self,
                             symbol: str,
                             decision: Dict[str, Any],
                             current_position: Dict[str, Any]) -> Dict[str, Any]:
        """处理SELL决策（单向持仓：先平多头，再开空头）"""
        try:
            # 检查交易对是否激活
            active_pairs = self.config_manager.get_active_trading_pairs()
            if symbol not in active_pairs:
                raise ValueError(f"交易对{symbol}未激活")

            # 获取交易对配置
            pair_config = active_pairs[symbol]

            # 根据AI决策设置杠杆
            ai_leverage = decision.get('leverage', 3)  # AI选择的杠杆
            self._set_leverage_if_needed(symbol, ai_leverage)

            # 单向持仓逻辑：检查是否有多头仓位需要先平掉
            current_long_size = 0
            if current_position:
                current_size = current_position.get('size', 0)
                current_long_size = current_size if current_size > 0 else 0

            # 第一步：如果有多头仓位，先平掉（使用python-okx库的close_positions方法）
            if current_long_size > 0:
                self.logger.info(f"检测到多头仓位 {current_long_size}，先执行平仓")

                # 使用python-okx库的原生close_positions方法
                close_response = self.trade_api.close_positions(
                    instId=symbol,
                    mgnMode='cross',  # 全仓模式
                    posSide='long'    # 平多头仓位
                )

                if close_response['code'] != '0':
                    raise Exception(f"平多头仓位失败: {close_response.get('msg', '未知错误')}")

                self.logger.info(f"多头仓位平仓成功")

                # 等待平仓完成
                import time
                time.sleep(2)

            # 第二步：开空头仓位 - 按保证金精确控制
            # AI建议的investment_amount就是目标保证金金额
            target_margin = decision.get('investment_amount', 0)
            if target_margin <= 0:
                return {
                    'success': True,
                    'action': 'SELL_SKIPPED',
                    'symbol': symbol,
                    'message': 'AI建议投资金额为0，跳过执行',
                    'timestamp': datetime.now().isoformat()
                }

            current_price = self._get_current_price(symbol)
            if current_price <= 0:
                raise Exception(f"无法获取{symbol}当前价格")

            leverage = decision.get('leverage', 1)

            # 使用新的基于OKX合约规格的精确计算方法
            calculated_size = self._calculate_contract_size_from_margin(
                symbol=symbol,
                target_margin=target_margin,
                leverage=leverage,
                current_price=current_price
            )

            # 调整精度
            calculated_size = self._adjust_order_size_precision(symbol, calculated_size)

            self.logger.info(f"🔍 最终下单参数: symbol={symbol}, size={calculated_size}, 目标保证金={target_margin} USDT")

            # 按计算出的数量下单（带重试机制）
            order_response = self._place_order_with_retry(
                symbol=symbol,
                side='sell',
                size=calculated_size,
                pos_side='short'
            )

            self.logger.info(f"🔍 下单响应: {order_response}")

            if order_response['code'] != '0':
                raise Exception(f"开空头仓位失败: {order_response.get('msg', '未知错误')}")

            order_id = order_response['data'][0]['ordId']
            self.logger.info(f"空头开仓成功，订单ID: {order_id}")

            # 等待订单完成并获取详情
            import time
            time.sleep(1)
            order_detail = self._get_order_detail(symbol, order_id)

            # 获取实际成交信息
            avg_price = self._safe_float(order_detail.get('avgPx', 0))
            filled_size = self._safe_float(order_detail.get('fillSz', 0))

            order_result = {
                'success': True,
                'order_id': order_id,
                'avg_price': avg_price,
                'filled_size': filled_size
            }

            if order_result['success']:
                # 设置止盈止损订单（使用实际成交数量）
                self._set_stop_orders(symbol, decision, order_result['order_id'], 'SELL', filled_size)

                result = {
                    'success': True,
                    'action': 'SELL',
                    'symbol': symbol,
                    'order_id': order_result['order_id'],
                    'size': calculated_size,
                    'price': order_result.get('avg_price', 0),
                    'message': f'卖出订单执行成功',
                    'timestamp': datetime.now().isoformat()
                }

                self._record_trade(result)
                self.logger.info(f"SELL决策执行成功: {symbol} 数量={calculated_size}")
                return result
            else:
                raise Exception(f"卖出订单失败: {order_result.get('error', '未知错误')}")
                
        except Exception as e:
            self.logger.error(f"处理SELL决策失败: {e}")
            raise

    def _handle_close_decision(self,
                              symbol: str,
                              decision: Dict[str, Any],
                              current_position: Dict[str, Any]) -> Dict[str, Any]:
        """处理CLOSE决策（只平仓，不开新仓）"""
        try:
            # 实时获取最新持仓数据，避免使用缓存
            try:
                fresh_positions = self.account_api.get_positions(instId=symbol)
                if fresh_positions['code'] == '0':
                    # 检查是否有持仓数据
                    if not fresh_positions['data'] or len(fresh_positions['data']) == 0:
                        return {
                            'success': True,
                            'action': 'CLOSE_SKIPPED',
                            'symbol': symbol,
                            'message': '实时检查发现无持仓，跳过平仓操作',
                            'timestamp': datetime.now().isoformat()
                        }

                    # 更新持仓数据
                    pos_data = fresh_positions['data'][0]
                    current_size = self._safe_float(pos_data.get('pos', 0))
                    if current_size == 0:
                        return {
                            'success': True,
                            'action': 'CLOSE_SKIPPED',
                            'symbol': symbol,
                            'message': '实时检查发现持仓为0，跳过平仓操作',
                            'timestamp': datetime.now().isoformat()
                        }
                    # 更新持仓信息，包含原始的保证金模式和持仓方向
                    current_position = {
                        'size': current_size,
                        'avg_price': self._safe_float(pos_data.get('avgPx', 0)),
                        'position_side': 'long' if current_size > 0 else 'short',
                        'mgn_mode': pos_data.get('mgnMode', 'cross'),  # 获取实际的保证金模式
                        'pos_side': pos_data.get('posSide', 'long' if current_size > 0 else 'short')  # 获取实际的持仓方向
                    }
                    self.logger.info(f"实时持仓检查: {symbol} 持仓大小={current_size}, 保证金模式={current_position['mgn_mode']}, 持仓方向={current_position['pos_side']}")
                else:
                    self.logger.warning(f"获取实时持仓失败: {fresh_positions.get('msg', '未知错误')}")
                    return {
                        'success': True,
                        'action': 'CLOSE_SKIPPED',
                        'symbol': symbol,
                        'message': '获取实时持仓失败，跳过平仓操作',
                        'timestamp': datetime.now().isoformat()
                    }
            except Exception as e:
                self.logger.warning(f"获取实时持仓失败，使用传入数据: {e}")
                # 如果获取实时数据失败，使用传入的数据
                if not current_position:
                    return {
                        'success': True,
                        'action': 'CLOSE_SKIPPED',
                        'symbol': symbol,
                        'message': '当前无持仓，跳过平仓操作',
                        'timestamp': datetime.now().isoformat()
                    }

            current_size = current_position.get('size', 0)
            if current_size == 0:
                return {
                    'success': True,
                    'action': 'CLOSE_SKIPPED',
                    'symbol': symbol,
                    'message': '当前无持仓，跳过平仓操作',
                    'timestamp': datetime.now().isoformat()
                }

            # 确定平仓方向，添加重试机制，使用实际的保证金模式和持仓方向
            max_retries = 3
            mgn_mode = current_position.get('mgn_mode', 'cross')
            pos_side = current_position.get('pos_side', 'long' if current_size > 0 else 'short')

            for attempt in range(max_retries):
                try:
                    if current_size > 0:
                        # 平多头仓位
                        self.logger.info(f"执行平多头仓位: {current_size} (尝试 {attempt + 1}/{max_retries}) 保证金模式={mgn_mode} 持仓方向={pos_side}")
                        close_response = self.trade_api.close_positions(
                            instId=symbol,
                            mgnMode=mgn_mode,
                            posSide=pos_side
                        )
                    else:
                        # 平空头仓位
                        self.logger.info(f"执行平空头仓位: {abs(current_size)} (尝试 {attempt + 1}/{max_retries}) 保证金模式={mgn_mode} 持仓方向={pos_side}")
                        close_response = self.trade_api.close_positions(
                            instId=symbol,
                            mgnMode=mgn_mode,
                            posSide=pos_side
                        )

                    if close_response['code'] != '0':
                        raise Exception(f"平仓失败: {close_response.get('msg', '未知错误')}")

                    self.logger.info(f"平仓成功: {symbol}")
                    break

                except Exception as e:
                    if attempt == max_retries - 1:
                        raise e
                    self.logger.warning(f"平仓失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                    time.sleep(2 ** attempt)  # 指数退避

            return {
                'success': True,
                'action': 'CLOSE',
                'symbol': symbol,
                'message': '平仓订单执行成功',
                'size': abs(current_size),
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"处理CLOSE决策失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    def _handle_close_and_reverse_decision(self,
                                         symbol: str,
                                         decision: Dict[str, Any],
                                         current_position: Dict[str, Any]) -> Dict[str, Any]:
        """处理平仓并反向开仓决策"""
        try:
            target_side = decision.get('target_side', 'long')
            self.logger.info(f"执行平仓并反向开仓: {symbol} -> 目标方向: {target_side}")

            # 第一步：平仓
            close_result = self._handle_close_decision(symbol, decision, current_position)
            if not close_result.get('success', False):
                return close_result

            # 等待一下确保平仓完成
            time.sleep(1)

            # 第二步：反向开仓
            reverse_decision = {
                **decision,
                'decision': 'BUY' if target_side == 'long' else 'SELL'
            }

            if target_side == 'long':
                reverse_result = self._handle_buy_decision(symbol, reverse_decision, None)
            else:
                reverse_result = self._handle_sell_decision(symbol, reverse_decision, None)

            return {
                'success': reverse_result.get('success', False),
                'action': 'CLOSE_AND_REVERSE',
                'close_result': close_result,
                'reverse_result': reverse_result,
                'symbol': symbol,
                'target_side': target_side,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"处理CLOSE_AND_REVERSE决策失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    def _calculate_order_size(self,
                             symbol: str,
                             decision: Dict[str, Any],
                             current_position: Dict[str, Any],
                             side: str) -> float:
        """计算下单数量"""
        try:
            # 获取交易对配置（不再使用固定的max_position_size限制）
            pair_config = self.config_manager.get(f'trading_pairs.{symbol}', {})
            
            # 获取账户余额
            balance = self._get_available_balance()
            self.logger.info(f"🔍 调试余额: balance={balance}")
            if balance <= 0:
                self.logger.warning("账户余额不足")
                return 0
            
            # 获取用户配置的投资比例和投资金额
            trading_pairs_config = self.config_manager.get('trading_pairs', {})
            investment_ratio = trading_pairs_config.get('investment_ratio', 5.0)  # 投资比例%
            max_investment_amount = balance * (investment_ratio / 100.0)  # 最大投资金额

            # 获取AI建议的具体投资金额
            ai_investment_amount = decision.get('investment_amount', max_investment_amount * 0.5)  # AI建议的具体投资金额

            # 调试：输出AI决策的完整内容
            self.logger.info(f"AI决策完整内容: {decision}")
            self.logger.info(f"AI建议投资金额: {ai_investment_amount} USDT")

            # 使用AI建议的具体投资金额，但不能超过最大投资限制
            base_order_amount = min(ai_investment_amount, max_investment_amount)

            self.logger.info(f"仓位计算: 投资比例={investment_ratio}%, 最大投资={max_investment_amount:.2f}, AI建议投资={ai_investment_amount:.2f}, 实际投资={base_order_amount:.2f}")

            # 不再使用固定的max_position_size限制，而是使用用户配置的投资比例作为上限

            # 确保不超过可用余额
            self.logger.info(f"🔍 调试投资金额: base_order_amount={base_order_amount}, balance={balance}")
            base_order_amount = min(base_order_amount, balance)
            self.logger.info(f"🔍 调试最终投资金额: base_order_amount={base_order_amount}")
            
            # 获取当前价格
            current_price = self._get_current_price(symbol)
            if current_price <= 0:
                self.logger.warning(f"无法获取{symbol}当前价格")
                return 0
            
            # 计算下单数量
            order_size = base_order_amount / current_price
            
            # 考虑当前持仓
            current_size = 0
            if current_position:
                current_size = current_position.get('size', 0)
                if current_position.get('position_side') == 'short':
                    current_size = -current_size
            
            # 单向持仓模式：平仓和开仓分别处理，这里只计算开仓数量
            # 不需要考虑当前持仓，因为反手操作已经在上层处理了

            # 直接使用基础下单金额计算数量，不考虑当前持仓
            # 因为反手操作已经在_handle_buy_decision和_handle_sell_decision中处理
            
            # 调整数量精度以符合交易所要求
            order_size = self._adjust_order_size_precision(symbol, order_size)

            # 最小下单量检查
            min_order_size = 0.001  # 根据交易所要求调整
            if order_size < min_order_size:
                return 0

            return order_size
            
        except Exception as e:
            self.logger.error(f"计算下单数量失败: {e}")
            return 0

    def _get_contract_specs(self, symbol: str) -> Dict[str, Any]:
        """获取合约规格信息"""
        try:
            from okx.PublicData import PublicAPI
            public_api = PublicAPI(flag='1', domain='https://www.okx.com')

            response = public_api.get_instruments(instType='SWAP', instId=symbol)
            if response['code'] == '0' and response['data']:
                instrument = response['data'][0]
                return {
                    'ctVal': float(instrument.get('ctVal', 1)),      # 合约面值
                    'ctMult': float(instrument.get('ctMult', 1)),    # 合约乘数
                    'minSz': float(instrument.get('minSz', 0.01)),   # 最小下单数量
                    'lotSz': float(instrument.get('lotSz', 0.01)),   # 下单数量精度
                    'instId': instrument.get('instId', symbol),      # 合约ID
                    'uly': instrument.get('uly', ''),                # 标的指数
                }
            else:
                self.logger.warning(f"无法获取{symbol}合约规格信息")
                return None
        except Exception as e:
            self.logger.error(f"获取合约规格失败: {e}")
            return None

    def _calculate_contract_size_from_margin(self, symbol: str, target_margin: float, leverage: int, current_price: float) -> float:
        """根据目标保证金计算正确的合约张数"""
        try:
            # 获取合约规格
            specs = self._get_contract_specs(symbol)
            if not specs:
                self.logger.error(f"无法获取{symbol}合约规格，使用旧方法计算")
                return self._calculate_size_old_method(symbol, target_margin, leverage, current_price)

            ct_val = specs['ctVal']      # 合约面值
            ct_mult = specs['ctMult']    # 合约乘数

            # 计算名义价值
            target_notional_value = target_margin * leverage

            # 根据OKX合约规格计算张数
            # 公式：张数 = 名义价值 ÷ (合约面值 × 合约乘数 × 当前价格)
            calculated_size = target_notional_value / (ct_val * ct_mult * current_price)

            self.logger.info(f"🎯 使用OKX合约规格精确计算:")
            self.logger.info(f"  - 合约: {symbol}")
            self.logger.info(f"  - 合约面值(ctVal): {ct_val}")
            self.logger.info(f"  - 合约乘数(ctMult): {ct_mult}")
            self.logger.info(f"  - 当前价格: {current_price}")
            self.logger.info(f"  - 目标保证金: {target_margin} USDT")
            self.logger.info(f"  - 杠杆倍数: {leverage}x")
            self.logger.info(f"  - 名义价值: {target_notional_value} USDT")
            self.logger.info(f"  - 计算张数: {target_notional_value} ÷ ({ct_val} × {ct_mult} × {current_price}) = {calculated_size:.6f}")

            return calculated_size

        except Exception as e:
            self.logger.error(f"计算合约张数失败: {e}")
            return self._calculate_size_old_method(symbol, target_margin, leverage, current_price)

    def _calculate_size_old_method(self, symbol: str, target_margin: float, leverage: int, current_price: float) -> float:
        """旧的计算方法（作为备用）"""
        target_notional_value = target_margin * leverage

        if 'BTC' in symbol and 'SWAP' in symbol:
            btc_amount = target_notional_value / current_price
            calculated_size = btc_amount / 0.01  # 1张 = 0.01 BTC
        elif 'ETH' in symbol and 'SWAP' in symbol:
            eth_amount = target_notional_value / current_price
            calculated_size = eth_amount / 0.01  # 1张 = 0.01 ETH (修正后)
        else:
            calculated_size = target_notional_value / current_price

        self.logger.warning(f"使用旧方法计算: {symbol} = {calculated_size:.6f}张")
        return calculated_size

    def _adjust_order_size_precision(self, symbol: str, order_size: float) -> float:
        """调整下单数量精度以符合交易所要求"""
        try:
            # 获取合约规格信息
            specs = self._get_contract_specs(symbol)
            if specs:
                min_size = specs['minSz']
                lot_size = specs['lotSz']
            else:
                # 备用方案
                from okx.PublicData import PublicAPI
                public_api = PublicAPI(flag='1', domain='https://www.okx.com')
                response = public_api.get_instruments(instType='SWAP', instId=symbol)
                if response['code'] == '0' and response['data']:
                    instrument = response['data'][0]
                    min_size = float(instrument['minSz'])
                    lot_size = float(instrument['lotSz'])
                else:
                    self.logger.warning(f"无法获取{symbol}精度信息，使用默认值")
                    return round(order_size, 2)

            # 使用Decimal进行精确计算，避免浮点数精度问题
            from decimal import Decimal, ROUND_DOWN

            # 转换为Decimal进行精确计算
            order_decimal = Decimal(str(order_size))
            lot_decimal = Decimal(str(lot_size))
            min_decimal = Decimal(str(min_size))

            # 调整到符合精度要求的数量（向下取整，避免超出投资金额）
            adjusted_decimal = (order_decimal / lot_decimal).quantize(Decimal('1'), rounding=ROUND_DOWN) * lot_decimal

            # 确保调整后的数量不小于最小交易数量
            if adjusted_decimal < min_decimal:
                adjusted_decimal = min_decimal

            # 转换回float，并确保精度正确
            adjusted_size = float(adjusted_decimal)

            # 根据lot_size确定小数位数
            if lot_size >= 1:
                # 如果lot_size >= 1，则结果应该是整数
                adjusted_size = round(adjusted_size)
            else:
                # 计算lot_size的小数位数
                lot_str = str(lot_size)
                if '.' in lot_str:
                    decimal_places = len(lot_str.split('.')[1])
                    adjusted_size = round(adjusted_size, decimal_places)

            # 添加详细日志来调试精度调整过程
            self.logger.info(f"精度调整详情: 原始数量={order_size:.6f}, 最小数量={min_size}, 精度单位={lot_size}, 调整后={adjusted_size}")

            self.logger.debug(f"数量精度调整: {order_size:.6f} -> {adjusted_size}")
            return adjusted_size

        except Exception as e:
            self.logger.warning(f"调整数量精度失败: {e}")
            return round(order_size, 6)

    def _place_market_order(self,
                           symbol: str,
                           side: str,
                           size: float) -> Dict[str, Any]:
        """下市价单"""
        try:
            for attempt in range(self.max_retries):
                try:
                    # 根据交易方向确定持仓方向（在双向持仓模式下实现单向逻辑）
                    pos_side = 'long' if side == 'buy' else 'short'

                    # 格式化张数精度
                    formatted_size = self._format_size_precision(size, symbol)

                    response = self.trade_api.place_order(
                        instId=symbol,
                        tdMode='cross',  # 全仓模式
                        side=side,
                        ordType='market',  # 市价单
                        sz=formatted_size,  # 使用精度格式化的张数
                        posSide=pos_side  # 买入用long，卖出用short
                    )
                    
                    if response['code'] == '0':
                        order_data = response['data'][0]
                        order_id = order_data['ordId']
                        
                        # 等待订单执行完成
                        time.sleep(1)
                        
                        # 获取订单详情
                        order_detail = self._get_order_detail(symbol, order_id)

                        # 安全处理订单详情
                        if not order_detail:
                            order_detail = {}

                        return {
                            'success': True,
                            'order_id': order_id,
                            'avg_price': self._safe_float(order_detail.get('avgPx', 0)),
                            'filled_size': self._safe_float(order_detail.get('fillSz', 0)),
                            'status': order_detail.get('state', 'unknown')
                        }
                    else:
                        error_msg = response.get('msg', '未知错误')
                        self.logger.warning(f"下单失败 (尝试 {attempt + 1}/{self.max_retries}): {error_msg}")
                        
                        if attempt < self.max_retries - 1:
                            time.sleep(self.retry_delay * (attempt + 1))
                        else:
                            return {
                                'success': False,
                                'error': error_msg
                            }
                            
                except Exception as e:
                    self.logger.warning(f"下单异常 (尝试 {attempt + 1}/{self.max_retries}): {e}")
                    if attempt < self.max_retries - 1:
                        time.sleep(self.retry_delay * (attempt + 1))
                    else:
                        return {
                            'success': False,
                            'error': str(e)
                        }
            
        except Exception as e:
            self.logger.error(f"下市价单失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def _place_market_order_with_position(self,
                                         symbol: str,
                                         side: str,
                                         size: float,
                                         pos_side: str) -> Dict[str, Any]:
        """下市价单（指定持仓方向）"""
        try:
            # 格式化张数精度
            formatted_size = self._format_size_precision(size, symbol)

            response = self.trade_api.place_order(
                instId=symbol,
                tdMode='cross',
                side=side,
                ordType='market',
                sz=formatted_size,  # 使用精度格式化的张数
                posSide=pos_side  # 指定持仓方向
            )

            if response['code'] == '0':
                order_data = response['data'][0]
                order_id = order_data['ordId']

                # 等待订单执行完成
                time.sleep(1)

                # 获取订单详情
                order_detail = self._get_order_detail(symbol, order_id)

                # 安全处理订单详情
                if not order_detail:
                    order_detail = {}

                return {
                    'success': True,
                    'order_id': order_id,
                    'avg_price': self._safe_float(order_detail.get('avgPx', 0)),
                    'filled_size': self._safe_float(order_detail.get('fillSz', 0)),
                    'status': order_detail.get('state', 'unknown')
                }
            else:
                error_msg = response.get('msg', '未知错误')
                return {
                    'success': False,
                    'error': error_msg
                }

        except Exception as e:
            self.logger.error(f"下单失败: {e}")
            return {'success': False, 'error': str(e)}

    def _set_stop_orders(self,
                        symbol: str,
                        decision: Dict[str, Any],
                        parent_order_id: str,
                        side: str,
                        position_size: float = None) -> None:
        """设置止盈止损订单"""
        try:
            # 统一使用stop_loss/take_profit字段名（兼容旧的new_前缀）
            stop_loss_pct = decision.get('stop_loss') or decision.get('new_stop_loss', 0)
            take_profit_pct = decision.get('take_profit') or decision.get('new_take_profit', 0)

            # 处理null值
            if stop_loss_pct is None:
                stop_loss_pct = 0
            if take_profit_pct is None:
                take_profit_pct = 0

            self.logger.info(f"止盈止损参数检查: stop_loss={stop_loss_pct}, take_profit={take_profit_pct}")

            if stop_loss_pct <= 0 and take_profit_pct <= 0:
                self.logger.info("无止盈止损设置，跳过")
                return

            # 获取当前价格
            current_price = self._get_current_price(symbol)
            if current_price <= 0:
                self.logger.warning("无法获取当前价格，跳过止盈止损设置")
                return

            # 获取当前杠杆倍数
            leverage = decision.get('leverage', 1)
            if leverage <= 0:
                leverage = 1  # 防止除零错误

            # 杠杆止盈止损计算（与开仓提示词保持一致）：
            # 直接使用决策引擎提示词中的计算方式：
            # 价格变化% = 目标收益率% ÷ 杠杆倍数
            # 例如：50x杠杆下，5%止盈 → 价格变化 5 ÷ 50 = 0.1%
            actual_stop_loss_pct = stop_loss_pct / leverage if stop_loss_pct > 0 else 0
            actual_take_profit_pct = take_profit_pct / leverage if take_profit_pct > 0 else 0
            
            # 添加价格变动验证
            current_price = self._get_current_price(symbol)
            if current_price <= 0:
                raise ValueError("无法获取当前价格，止盈止损设置失败")
            
            self.logger.info(f"🔍 止盈验证 - 目标收益率: {take_profit_pct}%")
            self.logger.info(f"  - 杠杆倍数: {leverage}x")
            self.logger.info(f"  - 要求价格变动: {actual_take_profit_pct:.6f}%")

            # 将调整后的百分比转换为实际价格
            stop_loss_price = 0
            take_profit_price = 0

            # 使用成交均价作为止盈止损计算基准（开仓时使用当前价格作为预估）
            base_price = avg_price if 'avg_price' in locals() else current_price

            if actual_stop_loss_pct > 0:
                if side == 'BUY':  # 多头止损：成交均价 - 调整后止损百分比
                    stop_loss_price = self._round_price_precision(base_price * (1 - actual_stop_loss_pct / 100), symbol)
                else:  # 空头止损：成交均价 + 调整后止损百分比
                    stop_loss_price = self._round_price_precision(base_price * (1 + actual_stop_loss_pct / 100), symbol)

            if actual_take_profit_pct > 0:
                if side == 'BUY':  # 多头止盈：成交均价 + 调整后止盈百分比
                    take_profit_price = self._round_price_precision(base_price * (1 + actual_take_profit_pct / 100), symbol)
                else:  # 空头止盈：成交均价 - 调整后止盈百分比
                    take_profit_price = self._round_price_precision(base_price * (1 - actual_take_profit_pct / 100), symbol)

            self.logger.info(f"杠杆止盈止损价格计算: {symbol}")
            self.logger.info(f"  - 当前价格: {current_price}")
            self.logger.info(f"  - 杠杆倍数: {leverage}x")
            self.logger.info(f"  - 目标保证金止损: {stop_loss_pct}% -> 价格变化: {actual_stop_loss_pct:.4f}% -> 止损价格: {stop_loss_price}")
            self.logger.info(f"  - 目标保证金止盈: {take_profit_pct}% -> 价格变化: {actual_take_profit_pct:.4f}% -> 止盈价格: {take_profit_price}")
            self.logger.info(f"  - 持仓数量: {position_size}")

            # 如果同时有止损和止盈，使用OCO订单
            if stop_loss_price > 0 and take_profit_price > 0:
                self._place_oco_order(symbol, stop_loss_price, take_profit_price, side, position_size)
            else:
                # 单独设置止损订单
                if stop_loss_price > 0:
                    self._place_stop_order(symbol, stop_loss_price, 'stop_loss', side, position_size)

                # 单独设置止盈订单
                if take_profit_price > 0:
                    self._place_stop_order(symbol, take_profit_price, 'take_profit', side, position_size)

        except Exception as e:
            self.logger.warning(f"设置止盈止损失败: {e}")

    def _get_position_size_with_retry(self, symbol: str, max_retries: int = 3) -> float:
        """获取持仓数量（带重试机制）"""
        for attempt in range(max_retries):
            try:
                response = self.account_api.get_positions(instType='SWAP', instId=symbol)
                if response['code'] == '0' and response['data']:
                    position_size = abs(self._safe_float(response['data'][0].get('pos', 0)))
                    self.logger.info(f"获取持仓成功: {symbol} 数量={position_size}")
                    return position_size
                else:
                    self.logger.warning(f"获取持仓响应异常: {response}")
                    return 0
            except Exception as e:
                self.logger.warning(f"获取持仓失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(1)  # 等待1秒后重试
                else:
                    self.logger.error(f"获取持仓最终失败，已重试{max_retries}次")
                    return 0
        return 0

    def _place_stop_order(self,
                         symbol: str,
                         trigger_price: float,
                         order_type: str,
                         side: str,
                         position_size: float = None) -> None:
        """下止盈止损订单"""
        try:
            # 如果没有传入持仓数量，则获取当前持仓数量（带重试机制）
            if position_size is None:
                position_size = self._get_position_size_with_retry(symbol)

            if position_size <= 0:
                self.logger.warning(f"无持仓，跳过设置{order_type}订单")
                return

            # 根据订单类型确定方向
            if side == 'BUY':
                order_side = 'sell'  # 买入后的止损/止盈都是卖出
            else:
                order_side = 'buy'   # 卖出后的止损/止盈都是买入

            # 构建订单参数
            # 格式化张数精度
            formatted_size = self._format_size_precision(position_size, symbol)

            order_params = {
                'instId': symbol,
                'tdMode': 'cross',
                'side': order_side,
                'ordType': 'oco',  # 使用OCO订单类型
                'sz': formatted_size,  # 使用精度格式化的张数
                'posSide': 'long' if side == 'BUY' else 'short'
            }

            # 根据订单类型设置触发价格
            if order_type == 'stop_loss':
                order_params['slTriggerPx'] = str(trigger_price)
                order_params['slOrdPx'] = '-1'  # 市价单
            else:  # take_profit
                order_params['tpTriggerPx'] = str(trigger_price)
                order_params['tpOrdPx'] = '-1'  # 市价单

            response = None
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    response = self.trade_api.place_algo_order(**order_params)
                    if response['code'] == '0':
                        algo_id = response['data'][0]['algoId']
                        self.pending_orders[algo_id] = {
                            'symbol': symbol,
                            'type': order_type,
                            'trigger_price': trigger_price,
                            'timestamp': datetime.now().isoformat()
                        }
                        self.logger.info(f"{order_type}订单设置成功: {symbol} 触发价={trigger_price}")
                        return # Success, exit function
                    raise Exception(f"API返回错误: {response.get('msg', '未知错误')}")
                except Exception as e:
                    self.logger.warning(f"下{order_type}订单失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                    if attempt < max_retries - 1:
                        time.sleep(1 * (attempt + 1))
            
            # If loop finishes without success
            self.logger.error(f"设置{order_type}订单最终失败: {symbol}")

        except Exception as e:
            self.logger.warning(f"处理{order_type}订单时发生异常: {e}")

    def _place_oco_order(self,
                        symbol: str,
                        stop_loss: float,
                        take_profit: float,
                        side: str,
                        position_size: float = None) -> None:
        """下OCO订单（同时设置止损和止盈）"""
        try:
            # 如果没有传入持仓数量，则获取当前持仓数量（带重试机制）
            if position_size is None:
                position_size = self._get_position_size_with_retry(symbol)

            if position_size <= 0:
                self.logger.warning(f"无持仓，跳过设置OCO订单")
                return

            # 获取当前市场价格进行验证
            current_price = self._get_current_price(symbol)
            if current_price <= 0:
                self.logger.error(f"无法获取{symbol}当前价格，跳过OCO订单设置")
                return

            # 根据原始交易方向确定平仓方向
            if side == 'BUY':
                order_side = 'sell'  # 买入后的平仓是卖出
                pos_side = 'long'
            else:
                order_side = 'buy'   # 卖出后的平仓是买入
                pos_side = 'short'

            # 验证和调整止盈止损价格
            validated_stop_loss, validated_take_profit = self._validate_oco_prices(
                symbol, current_price, stop_loss, take_profit, side
            )

            if validated_stop_loss <= 0 or validated_take_profit <= 0:
                self.logger.warning(f"止盈止损价格验证失败，跳过OCO订单设置")
                return

            # 构建OCO订单参数
            # 格式化张数精度
            formatted_size = self._format_size_precision(position_size, symbol)

            order_params = {
                'instId': symbol,
                'tdMode': 'cross',
                'side': order_side,
                'ordType': 'oco',  # OCO订单类型
                'sz': formatted_size,  # 使用精度格式化的张数
                'posSide': pos_side,
                'tpTriggerPx': str(validated_take_profit),  # 验证后的止盈触发价
                'tpOrdPx': '-1',  # 止盈市价单
                'slTriggerPx': str(validated_stop_loss),   # 验证后的止损触发价
                'slOrdPx': '-1'   # 止损市价单
            }

            self.logger.info(f"OCO订单参数: {order_params}")
            
            # 使用智能重试机制下OCO订单
            def place_oco_api_call():
                return self.trade_api.place_algo_order(**order_params)
            
            response = self._smart_retry_request(
                api_call=place_oco_api_call,
                operation_name=f"OCO订单下单({symbol})",
                max_retries=5  # 增加重试次数，因为OCO订单容易遇到SSL错误
            )
            
            if response and response.get('code') == '0':
                algo_id = response['data'][0]['algoId']
                self.pending_orders[algo_id] = {
                    'symbol': symbol,
                    'type': 'oco',
                    'stop_loss': validated_stop_loss,
                    'take_profit': validated_take_profit,
                    'timestamp': datetime.now().isoformat()
                }
                self.logger.info(f"✅ OCO订单设置成功: {symbol}")
                self.logger.info(f"  - 止损价格: {validated_stop_loss} (原始: {stop_loss})")
                self.logger.info(f"  - 止盈价格: {validated_take_profit} (原始: {take_profit})")
                self.logger.info(f"  - 当前市价: {current_price}")
                self.logger.info(f"  - 订单ID: {algo_id}")
                return # 成功，退出函数
            else:
                # 智能重试最终失败
                error_msg = response.get('msg', '未知错误') if response else "连接失败"
                self.logger.error(f"❌ OCO订单设置最终失败: {error_msg}")
                self.logger.error(f"完整响应: {response}")

        except Exception as e:
            self.logger.warning(f"处理OCO订单时发生异常: {e}")

    def _validate_oco_prices(self, symbol: str, current_price: float,
                           stop_loss: float, take_profit: float, side: str) -> tuple:
        """验证和调整OCO订单的止盈止损价格"""
        try:
            # 获取价格精度
            price_precision = self._get_price_precision(symbol)
            min_price_increment = 10 ** (-price_precision)

            validated_stop_loss = stop_loss
            validated_take_profit = take_profit

            if side == 'BUY':  # 多头持仓
                # 多头止损价格必须低于当前价格
                if stop_loss >= current_price:
                    validated_stop_loss = self._round_price_precision(
                        current_price - min_price_increment * 5, symbol
                    )
                    self.logger.warning(f"多头止损价格调整: {stop_loss} -> {validated_stop_loss} (当前价格: {current_price})")

                # 多头止盈价格必须高于当前价格
                if take_profit <= current_price:
                    validated_take_profit = self._round_price_precision(
                        current_price + min_price_increment * 5, symbol
                    )
                    self.logger.warning(f"多头止盈价格调整: {take_profit} -> {validated_take_profit} (当前价格: {current_price})")

            else:  # 空头持仓
                # 空头止损价格必须高于当前价格
                if stop_loss <= current_price:
                    validated_stop_loss = self._round_price_precision(
                        current_price + min_price_increment * 5, symbol
                    )
                    self.logger.warning(f"空头止损价格调整: {stop_loss} -> {validated_stop_loss} (当前价格: {current_price})")

                # 空头止盈价格必须低于当前价格
                if take_profit >= current_price:
                    validated_take_profit = self._round_price_precision(
                        current_price - min_price_increment * 5, symbol
                    )
                    self.logger.warning(f"空头止盈价格调整: {take_profit} -> {validated_take_profit} (当前价格: {current_price})")

            # 最终验证
            if validated_stop_loss <= 0 or validated_take_profit <= 0:
                self.logger.error(f"价格验证失败: 止损={validated_stop_loss}, 止盈={validated_take_profit}")
                return 0, 0

            return validated_stop_loss, validated_take_profit

        except Exception as e:
            self.logger.error(f"价格验证过程中发生错误: {e}")
            return 0, 0

    def _update_stop_orders(self,
                           symbol: str,
                           current_position: Dict[str, Any],
                           decision: Dict[str, Any]) -> None:
        """更新止盈止损订单"""
        try:
            # 检查AI决策是否包含止盈止损参数
            # 统一使用stop_loss/take_profit字段名（兼容旧的new_前缀）
            stop_loss_pct = decision.get('stop_loss') or decision.get('new_stop_loss', 0)
            take_profit_pct = decision.get('take_profit') or decision.get('new_take_profit', 0)

            # 处理null值
            if stop_loss_pct is None:
                stop_loss_pct = 0
            if take_profit_pct is None:
                take_profit_pct = 0

            self.logger.info(f"HOLD决策止盈止损参数: stop_loss={stop_loss_pct}, take_profit={take_profit_pct}")

            if stop_loss_pct <= 0 and take_profit_pct <= 0:
                self.logger.debug(f"HOLD决策无止盈止损参数，跳过更新: {symbol}")
                return

            # 获取当前持仓信息
            position_size = abs(current_position.get('size', 0))
            if position_size <= 0:
                self.logger.debug(f"无持仓，跳过止盈止损更新: {symbol}")
                return

            # 确定持仓方向（使用OKX的实际持仓方向，而不是根据size判断）
            pos_side = current_position.get('position_side', 'long')  # 修复字段名
            current_size = current_position.get('size', 0)

            # 根据OKX的持仓方向确定交易方向
            if pos_side == 'long':
                side = 'BUY'  # 多头持仓
            else:  # pos_side == 'short'
                side = 'SELL'  # 空头持仓

            self.logger.info(f"更新持仓止盈止损: {symbol} 方向={side} 数量={position_size}")
            self.logger.info(f"  - AI建议止损: {stop_loss_pct}%")
            self.logger.info(f"  - AI建议止盈: {take_profit_pct}%")

            # 获取当前价格
            current_price = self._get_current_price(symbol)
            if current_price <= 0:
                self.logger.warning("无法获取当前价格，跳过止盈止损更新")
                return

            # 获取当前杠杆倍数（从实际持仓信息中获取，而不是从AI决策中）
            leverage = self._get_current_leverage(symbol)
            if leverage <= 0:
                leverage = 1  # 防止除零错误

            self.logger.info(f"获取实际杠杆倍数: {symbol} = {leverage}x")

            # 杠杆止盈止损计算：
            # 止损3%是指保证金亏损3%，不是价格变化3%
            # 价格变化% = 保证金亏损% ÷ 杠杆倍数
            actual_stop_loss_pct = stop_loss_pct / leverage if stop_loss_pct > 0 else 0
            actual_take_profit_pct = take_profit_pct / leverage if take_profit_pct > 0 else 0

            # 计算新的止盈止损价格
            stop_loss_price = 0
            take_profit_price = 0

            # 获取开仓均价作为止盈止损计算基准
            avg_price = current_position.get('avg_price', current_price)

            if actual_stop_loss_pct > 0:
                if side == 'BUY':  # 多头止损：开仓均价 - 调整后止损百分比
                    stop_loss_price = self._round_price_precision(avg_price * (1 - actual_stop_loss_pct / 100), symbol)
                else:  # 空头止损：开仓均价 + 调整后止损百分比
                    stop_loss_price = self._round_price_precision(avg_price * (1 + actual_stop_loss_pct / 100), symbol)

            if actual_take_profit_pct > 0:
                if side == 'BUY':  # 多头止盈：开仓均价 + 调整后止盈百分比
                    take_profit_price = self._round_price_precision(avg_price * (1 + actual_take_profit_pct / 100), symbol)
                else:  # 空头止盈：确保止盈价低于当前市场价格
                    current_price = self._get_current_price(symbol)
                    take_profit_price = self._round_price_precision(avg_price * (1 - actual_take_profit_pct / 100), symbol)
                    
                    # 验证止盈价有效性
                    if take_profit_price >= current_price:
                        self.logger.warning(f"⚠️ 空头止盈价 {take_profit_price} 高于当前价 {current_price}，调整为当前价的99%")
                        take_profit_price = self._round_price_precision(current_price * 0.99, symbol)
                    
                    self.logger.info(f"🔍 持仓空头止盈验证 - 开仓均价: {avg_price}, 当前价: {current_price}, 目标收益率: {take_profit_pct}%, 杠杆调整后: {actual_take_profit_pct:.6f}%, 最终止盈价: {take_profit_price}")

            self.logger.info(f"持仓杠杆止盈止损价格计算: {symbol}")
            self.logger.info(f"  - 当前价格: {current_price}")
            self.logger.info(f"  - 杠杆倍数: {leverage}x")
            self.logger.info(f"  - 目标保证金止损: {stop_loss_pct}% -> 价格变化: {actual_stop_loss_pct:.4f}% -> 止损价格: {stop_loss_price}")
            self.logger.info(f"  - 目标保证金止盈: {take_profit_pct}% -> 价格变化: {actual_take_profit_pct:.4f}% -> 止盈价格: {take_profit_price}")

            # 先取消现有的止盈止损订单（使用智能取消）
            self._smart_cancel_stop_orders(symbol)

            # 设置新的止盈止损订单
            if stop_loss_price > 0 and take_profit_price > 0:
                self._place_oco_order(symbol, stop_loss_price, take_profit_price, side, position_size)
            else:
                # 单独设置止损订单
                if stop_loss_price > 0:
                    self._place_stop_order(symbol, stop_loss_price, 'stop_loss', side, position_size)

                # 单独设置止盈订单
                if take_profit_price > 0:
                    self._place_stop_order(symbol, take_profit_price, 'take_profit', side, position_size)

        except Exception as e:
            self.logger.warning(f"更新止盈止损失败: {e}")

    def _cancel_existing_stop_orders(self, symbol: str) -> None:
        """取消现有的止盈止损订单 - 优化版本，处理超时和批量取消"""
        try:
            self.logger.info(f"取消现有止盈止损订单: {symbol}")

            # 获取当前的算法订单（止盈止损订单） - 增加重试机制
            response = None
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    response = self.trade_api.order_algos_list(
                        instType='SWAP',
                        instId=symbol,
                        ordType='oco'  # 只查询OCO订单
                    )
                    if response['code'] == '0':
                        break  # 成功获取
                    raise Exception(f"API返回错误: {response.get('msg', 'Unknown error')}")
                except Exception as e:
                    self.logger.warning(f"获取止盈止损订单列表失败 (尝试 {attempt + 1}/{max_retries}): {symbol} - {e}")
                    if attempt < max_retries - 1:
                        time.sleep(1 * (attempt + 1))
                    else:
                        self.logger.error(f"获取止盈止损订单列表最终失败: {symbol}")
                        return # 无法获取列表，直接返回

            if not response or response['code'] != '0' or not response.get('data'):
                self.logger.debug(f"没有找到活跃的止盈止损订单: {symbol}")
                return

            if response['code'] == '0' and response['data']:
                active_orders = []

                # 筛选活跃订单
                for order in response['data']:
                    order_state = order.get('state', '')
                    if order_state in ['live', 'effective']:
                        active_orders.append({
                            'algoId': order['algoId'],
                            'instId': symbol,
                            'ordType': order.get('ordType', 'unknown')
                        })

                if not active_orders:
                    self.logger.debug(f"没有找到活跃的止盈止损订单: {symbol}")
                    return

                self.logger.info(f"找到 {len(active_orders)} 个活跃订单需要取消")

                # 分批取消订单，避免一次性取消太多导致超时
                batch_size = 5  # 每批最多取消5个订单
                cancelled_count = 0
                failed_count = 0

                for i in range(0, len(active_orders), batch_size):
                    batch = active_orders[i:i + batch_size]

                    try:
                        # 批量取消订单
                        cancel_response = self.trade_api.cancel_algo_order(batch)

                        if cancel_response['code'] == '0':
                            # 检查每个订单的取消结果
                            if 'data' in cancel_response:
                                for result in cancel_response['data']:
                                    algo_id = result.get('algoId', 'unknown')
                                    s_code = result.get('sCode', '')
                                    s_msg = result.get('sMsg', '')

                                    if s_code == '0':
                                        cancelled_count += 1
                                        self.logger.info(f"✅ 取消订单成功: {algo_id}")
                                    else:
                                        failed_count += 1
                                        self.logger.warning(f"❌ 取消订单失败: {algo_id} - {s_msg}")
                            else:
                                # 如果没有详细结果，假设批次成功
                                cancelled_count += len(batch)
                                self.logger.info(f"✅ 批量取消成功: {len(batch)} 个订单")
                        else:
                            # 整个批次失败
                            error_msg = cancel_response.get('msg', 'Unknown error')
                            failed_count += len(batch)

                            # 如果是超时错误，尝试单个取消
                            if 'timeout' in error_msg.lower() or 'timed out' in error_msg.lower():
                                self.logger.warning(f"⏰ 批量取消超时，尝试单个取消: {error_msg}")
                                cancelled_count += self._cancel_orders_individually(batch)
                            else:
                                self.logger.warning(f"❌ 批量取消失败: {error_msg}")

                        # 批次间延迟，避免请求过于频繁
                        if i + batch_size < len(active_orders):
                            time.sleep(0.5)

                    except Exception as e:
                        failed_count += len(batch)
                        self.logger.warning(f"❌ 批量取消异常: {e}")

                        # 尝试单个取消
                        try:
                            cancelled_count += self._cancel_orders_individually(batch)
                        except Exception as e2:
                            self.logger.error(f"❌ 单个取消也失败: {e2}")

                self.logger.info(f"取消止盈止损订单完成: {symbol} - 成功: {cancelled_count}, 失败: {failed_count}")
            else:
                self.logger.debug(f"没有找到活跃的止盈止损订单: {symbol}")

        except Exception as e:
            self.logger.warning(f"取消现有止盈止损订单失败: {symbol} - {e}")

    def _cancel_orders_individually(self, orders: List[Dict[str, str]]) -> int:
        """单个取消订单，用于批量取消失败时的备用方案"""
        cancelled_count = 0

        for order in orders:
            try:
                algo_id = order['algoId']
                self.logger.info(f"🔄 单个取消订单: {algo_id}")

                # 带重试的单个取消
                success = self._cancel_single_order_with_retry(order, max_retries=3)
                if success:
                    cancelled_count += 1
                    self.logger.info(f"✅ 单个取消成功: {algo_id}")
                else:
                    self.logger.warning(f"❌ 单个取消最终失败: {algo_id}")

                # 单个取消间的延迟
                time.sleep(0.3)

            except Exception as e:
                self.logger.warning(f"❌ 单个取消异常: {order.get('algoId', 'unknown')} - {e}")

        return cancelled_count

    def _cancel_single_order_with_retry(self, order: Dict[str, str], max_retries: int = 3) -> bool:
        """带重试机制的单个订单取消"""
        algo_id = order.get('algoId', 'unknown')

        for attempt in range(max_retries):
            try:
                cancel_response = self.trade_api.cancel_algo_order([order])

                if cancel_response['code'] == '0':
                    return True
                else:
                    error_msg = cancel_response.get('msg', 'Unknown error')

                    # 如果订单已经不存在或已取消，认为成功
                    if any(keyword in error_msg.lower() for keyword in
                           ['not exist', 'already canceled', 'already cancelled', 'invalid order']):
                        self.logger.info(f"订单已不存在或已取消: {algo_id}")
                        return True

                    # 如果是超时错误，继续重试
                    if 'timeout' in error_msg.lower() or 'timed out' in error_msg.lower():
                        if attempt < max_retries - 1:
                            wait_time = (attempt + 1) * 0.5  # 递增等待时间
                            self.logger.warning(f"⏰ 取消超时，{wait_time}s后重试 ({attempt + 1}/{max_retries}): {algo_id}")
                            time.sleep(wait_time)
                            continue

                    self.logger.warning(f"❌ 取消失败 ({attempt + 1}/{max_retries}): {algo_id} - {error_msg}")

                    if attempt < max_retries - 1:
                        time.sleep(0.5)

            except Exception as e:
                self.logger.warning(f"❌ 取消异常 ({attempt + 1}/{max_retries}): {algo_id} - {e}")
                if attempt < max_retries - 1:
                    time.sleep(0.5)

        return False

    def _smart_cancel_stop_orders(self, symbol: str) -> None:
        """智能取消止盈止损订单 - 包含错误恢复和状态检查"""
        try:
            self.logger.info(f"🎯 智能取消止盈止损订单: {symbol}")

            # 首先尝试标准取消流程
            self._cancel_existing_stop_orders(symbol)

            # 等待一段时间让取消操作生效
            time.sleep(2)

            # 验证取消结果
            remaining_orders = self._get_remaining_active_orders(symbol)
            if remaining_orders:
                self.logger.warning(f"⚠️ 仍有 {len(remaining_orders)} 个订单未取消，尝试强制清理")

                # 强制清理剩余订单
                for order in remaining_orders:
                    try:
                        self._force_cancel_order(order)
                        time.sleep(0.5)
                    except Exception as e:
                        self.logger.error(f"❌ 强制取消失败: {order.get('algoId', 'unknown')} - {e}")

            self.logger.info(f"✅ 智能取消完成: {symbol}")

        except Exception as e:
            self.logger.error(f"❌ 智能取消失败: {symbol} - {e}")

    def _get_remaining_active_orders(self, symbol: str) -> List[Dict[str, Any]]:
        """获取剩余的活跃订单"""
        try:
            response = self.trade_api.order_algos_list(
                instType='SWAP',
                instId=symbol,
                ordType='oco'
            )

            if response['code'] == '0' and response['data']:
                return [order for order in response['data']
                       if order.get('state', '') in ['live', 'effective']]

            return []

        except Exception as e:
            self.logger.warning(f"获取剩余订单失败: {e}")
            return []

    def _force_cancel_order(self, order: Dict[str, Any]) -> None:
        """强制取消单个订单"""
        algo_id = order.get('algoId', 'unknown')
        symbol = order.get('instId', 'unknown')

        self.logger.info(f"🔨 强制取消订单: {algo_id}")

        try:
            cancel_response = self.trade_api.cancel_algo_order([{
                'algoId': algo_id,
                'instId': symbol
            }])

            if cancel_response['code'] == '0':
                self.logger.info(f"✅ 强制取消成功: {algo_id}")
            else:
                error_msg = cancel_response.get('msg', 'Unknown error')
                self.logger.warning(f"❌ 强制取消失败: {algo_id} - {error_msg}")

        except Exception as e:
            self.logger.error(f"❌ 强制取消异常: {algo_id} - {e}")

    def _get_order_detail(self, symbol: str, order_id: str) -> Dict[str, Any]:
        """获取订单详情，带重试机制"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                response = self.trade_api.get_order(instId=symbol, ordId=order_id)

                if response['code'] == '0' and response['data']:
                    return response['data'][0]
                else:
                    return {}

            except Exception as e:
                if attempt == max_retries - 1:
                    self.logger.warning(f"获取订单详情失败 (最终尝试): {e}")
                    return {}
                else:
                    self.logger.warning(f"获取订单详情失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                    time.sleep(1 + attempt)  # 递增等待时间
    
    def _get_available_balance(self) -> float:
        """获取可用余额 - 同步版本，带重试机制"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                response = self.account_api.get_account_balance()

                if response['code'] == '0':
                    for account in response['data']:
                        for detail in account['details']:
                            if detail['ccy'] == 'USDT':  # 假设使用USDT作为保证金
                                balance = self._safe_float(detail['availBal'])
                                if balance > 0:  # 只有余额大于0才返回
                                    return balance
                                else:
                                    # 余额为0，检查是否真的为0还是数据错误
                                    self.logger.warning(f"获取到余额为0，尝试 {attempt + 1}/{max_retries}")
                                    if attempt < max_retries - 1:
                                        time.sleep(1 + attempt)
                                        continue
                                    return 0.0

                # API返回错误，重试
                self.logger.warning(f"API返回错误: {response.get('msg', 'Unknown')}, 尝试 {attempt + 1}/{max_retries}")
                if attempt < max_retries - 1:
                    time.sleep(1 + attempt)
                    continue
                else:
                    raise Exception(f"API返回错误: {response.get('msg', 'Unknown error')}")

            except Exception as e:
                if attempt < max_retries - 1:
                    self.logger.warning(f"获取余额失败，重试 {attempt + 1}/{max_retries}: {e}")
                    time.sleep(1 + attempt)
                    continue
                else:
                    self.logger.error(f"获取余额失败，已重试{max_retries}次: {e}")
                    raise Exception(f"无法获取账户余额: {e}")

    async def _get_available_balance_async(self) -> float:
        """异步获取可用余额"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.executor, self._get_available_balance)
    
    def _get_current_price(self, symbol: str) -> float:
        """获取当前价格 - 优化连接稳定性和重试机制"""
        max_retries = 5  # 增加重试次数
        base_delay = 0.5  # 基础延迟
        
        for attempt in range(max_retries):
            try:
                # 检测连接断开并重新初始化API
                if attempt > 0 and self._should_reinit_api(attempt):
                    self._reinit_market_api()
                
                # 使用市场API获取最新价格
                response = self.market_api.get_ticker(instId=symbol)

                if response['code'] == '0' and response['data']:
                    price = self._safe_float(response['data'][0]['last'])
                    if price > 0:  # 只有价格大于0才返回
                        if attempt > 0:  # 如果是重试成功，记录恢复信息
                            self.logger.info(f"✅ 重试成功获取{symbol}价格: {price} (尝试 {attempt + 1}/{max_retries})")
                        else:
                            self.logger.debug(f"获取{symbol}当前价格: {price}")
                        return price
                    else:
                        # 价格为0，重试
                        self.logger.warning(f"获取到{symbol}价格为0，尝试 {attempt + 1}/{max_retries}")
                        if attempt < max_retries - 1:
                            delay = self._calculate_retry_delay(attempt, base_delay)
                            time.sleep(delay)
                            continue
                        return 0.0

                # API返回错误，重试
                error_msg = response.get('msg', 'Unknown')
                self.logger.warning(f"获取{symbol}价格API错误: {error_msg}, 尝试 {attempt + 1}/{max_retries}")
                
                if attempt < max_retries - 1:
                    delay = self._calculate_retry_delay(attempt, base_delay)
                    time.sleep(delay)
                    continue
                else:
                    raise Exception(f"获取价格API错误: {error_msg}")

            except Exception as e:
                error_str = str(e).lower()
                
                # 特殊处理连接断开错误
                if any(keyword in error_str for keyword in ['disconnected', 'connection', 'timeout', 'network']):
                    self.logger.warning(f"🔌 检测到连接问题: {e}")
                    if attempt < max_retries - 1:
                        # 连接问题使用更长的延迟和指数退避
                        delay = self._calculate_retry_delay(attempt, base_delay * 2, max_delay=10.0)
                        self.logger.info(f"⏳ 连接重试延迟 {delay:.1f}s (尝试 {attempt + 1}/{max_retries})")
                        time.sleep(delay)
                        continue
                
                if attempt < max_retries - 1:
                    delay = self._calculate_retry_delay(attempt, base_delay)
                    self.logger.warning(f"获取{symbol}价格失败，{delay:.1f}s后重试 {attempt + 1}/{max_retries}: {e}")
                    time.sleep(delay)
                    continue
                else:
                    self.logger.error(f"获取{symbol}价格失败，已重试{max_retries}次: {e}")
                    raise Exception(f"无法获取{symbol}价格: {e}")

    def _should_reinit_api(self, attempt: int) -> bool:
        """判断是否需要重新初始化API连接"""
        # 每2次失败后重新初始化一次
        return attempt > 0 and attempt % 2 == 0

    def _reinit_market_api(self) -> None:
        """重新初始化市场API连接"""
        try:
            self.logger.info("🔄 重新初始化市场API连接...")
            okx_config = self.config_manager.get('credentials.okx')
            if okx_config:
                from okx.MarketData import MarketAPI
                self.market_api = MarketAPI(
                    api_key=okx_config['api_key'],
                    api_secret_key=okx_config['api_secret'],
                    passphrase=okx_config['passphrase'],
                    flag='1',
                    domain='https://www.okx.com'
                )
                self.logger.info("✅ 市场API重新初始化完成")
            else:
                self.logger.warning("⚠️ 无法获取OKX配置，跳过API重新初始化")
        except Exception as e:
            self.logger.warning(f"⚠️ 重新初始化市场API失败: {e}")

    def _calculate_retry_delay(self, attempt: int, base_delay: float = 0.5, max_delay: float = 5.0) -> float:
        """计算重试延迟（指数退避算法）"""
        # 指数退避：base_delay * (2 ^ attempt)，但不超过max_delay
        delay = min(base_delay * (2 ** attempt), max_delay)
        # 添加随机抖动，避免所有请求同时重试
        import random
        jitter = random.uniform(0.1, 0.3)
        return delay + jitter

    async def _get_current_price_async(self, symbol: str) -> float:
        """异步获取当前价格，带有连接稳定性优化"""
        max_retries = 5  # 增加重试次数
        
        for attempt in range(max_retries):
            try:
                # 检查是否需要重新初始化API连接
                if self._should_reinit_api(attempt):
                    await asyncio.to_thread(self._reinit_market_api)
                
                # 获取ticker数据
                ticker_data = await asyncio.to_thread(self.market_api.get_ticker, instId=symbol)
                
                if ticker_data.get('code') == '0' and ticker_data.get('data'):
                    price = float(ticker_data['data'][0]['last'])
                    if attempt > 0:
                        self.logger.info(f"✅ 重试成功获取{symbol}价格: {price} (尝试{attempt + 1}次)")
                    else:
                        self.logger.debug(f"获取{symbol}价格: {price}")
                    return price
                else:
                    raise Exception(f"API返回错误: {ticker_data}")
                    
            except Exception as e:
                error_str = str(e).lower()
                is_connection_error = any(keyword in error_str for keyword in [
                    'server disconnected', 'connection', 'timeout', 'network',
                    'connection refused', 'connection reset', 'socket'
                ])
                
                if attempt < max_retries - 1:
                    # 计算重试延迟
                    if is_connection_error:
                        # 连接错误使用更长的延迟
                        wait_time = self._calculate_retry_delay(attempt, base_delay=1.0, max_delay=8.0)
                        self.logger.warning(f"🔗 连接错误获取{symbol}价格，{wait_time:.1f}秒后重试 ({attempt + 1}/{max_retries}): {e}")
                    else:
                        # 其他错误使用标准延迟
                        wait_time = self._calculate_retry_delay(attempt)
                        self.logger.warning(f"⚠️ 获取{symbol}价格失败，{wait_time:.1f}秒后重试 ({attempt + 1}/{max_retries}): {e}")
                    
                    await asyncio.sleep(wait_time)
                else:
                    self.logger.error(f"❌ 获取{symbol}价格失败，已重试{max_retries}次: {e}")
                    raise Exception(f"无法获取{symbol}价格: {e}")
    
    def _record_trade(self, trade_result: Dict[str, Any]) -> None:
        """记录交易历史"""
        try:
            self.trade_history.append(trade_result)
            
            # 保持历史记录数量限制
            max_history = 1000
            if len(self.trade_history) > max_history:
                self.trade_history = self.trade_history[-max_history:]
                
        except Exception as e:
            self.logger.warning(f"记录交易历史失败: {e}")
    
    def get_trade_statistics(self) -> Dict[str, Any]:
        """获取交易统计信息"""
        try:
            if not self.trade_history:
                return {"total_trades": 0}
            
            total_trades = len(self.trade_history)
            buy_trades = len([t for t in self.trade_history if t.get('action') == 'BUY'])
            sell_trades = len([t for t in self.trade_history if t.get('action') == 'SELL'])
            
            return {
                "total_trades": total_trades,
                "buy_trades": buy_trades,
                "sell_trades": sell_trades,
                "success_rate": len([t for t in self.trade_history if t.get('success', False)]) / total_trades
            }
            
        except Exception as e:
            self.logger.error(f"获取交易统计失败: {e}")
            return {"total_trades": 0}

    def get_account_balance(self) -> Dict[str, Any]:
        """获取账户余额信息（带重试机制）"""
        max_retries = 3
        retry_delay = 1  # 秒

        for attempt in range(max_retries):
            try:
                if not self.account_api:
                    self.logger.warning("账户API未初始化")
                    return {
                        'total_balance': '--',
                        'available_balance': '--',
                        'daily_pnl': '--',
                        'total_pnl': '--'
                    }

                # 获取账户余额 - 使用重试机制
                balance_result = self._get_balance_with_retry()

                # 简化调试信息，减少不必要的API调用
                if attempt == 0:  # 只在第一次尝试时获取额外信息
                    try:
                        # 获取交易账户余额（简化版）
                        trading_balance = self.account_api.get_account_balance(ccy='USDT')
                        self.logger.debug(f"交易账户USDT余额获取成功")
                    except Exception as e:
                        self.logger.debug(f"获取交易账户余额失败: {e}")

                break  # 成功获取，跳出重试循环

            except Exception as e:
                if attempt < max_retries - 1:
                    self.logger.warning(f"获取账户余额失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # 指数退避
                    continue
                else:
                    self.logger.error(f"获取账户余额失败，已重试 {max_retries} 次: {e}")
                    return {
                        'total_balance': '--',
                        'available_balance': '--',
                        'daily_pnl': '--',
                        'total_pnl': '--'
                    }

        return self._process_balance_result(balance_result)

    def _get_balance_with_retry(self) -> Dict[str, Any]:
        """带重试的余额获取"""
        try:
            balance_result = self.account_api.get_account_balance()
            if balance_result.get('code') == '0':
                return balance_result
            else:
                raise Exception(f"API返回错误: {balance_result.get('msg', 'Unknown error')}")
        except Exception as e:
            raise e

    def _process_balance_result(self, balance_result: Dict[str, Any]) -> Dict[str, Any]:
        """处理余额结果"""
        try:

            # 解析余额数据
            balance_data = balance_result.get('data', [])
            if not balance_data:
                self.logger.warning("账户余额数据为空")
                return {
                    'total_balance': '--',
                    'available_balance': '--',
                    'daily_pnl': '--',
                    'total_pnl': '--'
                }

            account_info = balance_data[0]
            details = account_info.get('details', [])

            # 简化日志，只在调试模式下显示详细信息
            self.logger.debug(f"账户详情数量: {len(details)}")

            # 计算总资产和可用余额（主要关注USDT）
            usdt_only_balance = 0
            usdt_only_available = 0

            for detail in details:
                ccy = detail.get('ccy', '')
                eq = self._safe_float(detail.get('eq', 0))
                availEq = self._safe_float(detail.get('availEq', 0))

                # 只记录USDT余额
                if ccy == 'USDT':
                    usdt_only_balance = eq
                    usdt_only_available = availEq
                    self.logger.debug(f"USDT - 总额: {eq:.2f}, 可用: {availEq:.2f}")
                    break  # 找到USDT后就退出循环

            # 获取今日盈亏和总盈亏
            daily_pnl = 0
            total_pnl = 0
            unrealized_pnl = 0

            # 1. 从交易历史计算已实现盈亏
            if hasattr(self, 'trade_history') and self.trade_history:
                today = datetime.now().date()
                for trade in self.trade_history:
                    try:
                        trade_date = datetime.fromisoformat(trade.get('timestamp', '')).date()
                        pnl = self._safe_float(trade.get('pnl', 0))
                        total_pnl += pnl
                        if trade_date == today:
                            daily_pnl += pnl
                    except Exception:
                        continue  # 忽略无效的交易记录

            # 2. 获取当前持仓的未实现盈亏
            try:
                positions_response = self.account_api.get_positions()
                if positions_response.get('code') == '0':
                    for position in positions_response.get('data', []):
                        pos_pnl = self._safe_float(position.get('upl', 0))  # unrealized PnL
                        unrealized_pnl += pos_pnl

                        # 检查是否是今日开仓的持仓
                        pos_time = position.get('uTime', '')
                        if pos_time:
                            try:
                                pos_date = datetime.fromtimestamp(int(pos_time) / 1000).date()
                                if pos_date == datetime.now().date():
                                    daily_pnl += pos_pnl
                            except Exception:
                                pass

                    # 将未实现盈亏加入总盈亏
                    total_pnl += unrealized_pnl

                    self.logger.debug(f"盈亏计算 - 已实现: {total_pnl - unrealized_pnl:.2f}, 未实现: {unrealized_pnl:.2f}, 今日: {daily_pnl:.2f}")

            except Exception as e:
                self.logger.debug(f"获取持仓盈亏失败: {e}")

            # 3. 基于余额变化计算盈亏（更准确的方法）
            current_time = datetime.now()

            # 初始化基准余额
            if self.initial_balance is None:
                self.initial_balance = usdt_only_balance
                self.logger.info(f"设置初始余额基准: {self.initial_balance:.2f} USDT")

            # 检查是否需要更新今日基准余额
            if (self.daily_start_balance is None or
                self.last_balance_update is None or
                self.last_balance_update.date() != current_time.date()):
                self.daily_start_balance = usdt_only_balance
                self.logger.info(f"设置今日余额基准: {self.daily_start_balance:.2f} USDT")

            self.last_balance_update = current_time

            # 基于余额变化计算盈亏
            balance_based_total_pnl = usdt_only_balance - self.initial_balance
            balance_based_daily_pnl = usdt_only_balance - self.daily_start_balance

            # 使用更准确的盈亏数据（优先使用余额变化）
            final_total_pnl = balance_based_total_pnl if abs(balance_based_total_pnl) > abs(total_pnl) else total_pnl
            final_daily_pnl = balance_based_daily_pnl if abs(balance_based_daily_pnl) > abs(daily_pnl) else daily_pnl

            # 简化日志输出
            self.logger.debug(f"USDT余额 - 总额: {usdt_only_balance:.2f}, 可用: {usdt_only_available:.2f}, 今日盈亏: {final_daily_pnl:.2f}, 总盈亏: {final_total_pnl:.2f}")

            return {
                'total_balance': f"{usdt_only_balance:.2f}",  # 显示USDT总权益
                'available_balance': f"{usdt_only_available:.2f}",  # 显示USDT可用余额
                'daily_pnl': f"{final_daily_pnl:.2f}",
                'total_pnl': f"{final_total_pnl:.2f}"
            }

        except Exception as e:
            self.logger.error(f"处理账户余额数据失败: {e}")
            return {
                'total_balance': '--',
                'available_balance': '--',
                'daily_pnl': '--',
                'total_pnl': '--'
            }

    def _place_order_with_retry(self, symbol: str, side: str, size: float, pos_side: str, max_retries: int = 3) -> Dict[str, Any]:
        """带重试机制的下单方法"""
        for attempt in range(max_retries):
            try:
                # 确保数量精度正确
                adjusted_size = self._adjust_order_size_precision(symbol, size)

                self.logger.info(f"🔄 下单尝试 {attempt + 1}/{max_retries}: {symbol}, size={adjusted_size}")

                order_response = self.trade_api.place_order(
                    instId=symbol,
                    tdMode='cross',
                    side=side,
                    ordType='market',
                    sz=str(adjusted_size),
                    posSide=pos_side
                )

                # 如果成功，直接返回
                if order_response['code'] == '0':
                    self.logger.info(f"✅ 下单成功: {symbol}")
                    return order_response

                # 如果是精度错误，尝试调整数量
                if 'lot size' in str(order_response.get('msg', '')).lower():
                    self.logger.warning(f"⚠️ 精度错误，尝试调整数量: {order_response}")

                    # 尝试减少数量到最近的有效值
                    if 'ETH' in symbol:
                        # ETH合约，尝试减少0.01
                        size = max(0.01, size - 0.01)
                    elif 'BTC' in symbol:
                        # BTC合约，尝试减少1张
                        size = max(1, size - 1)
                    else:
                        # 其他合约，尝试减少10%
                        size = size * 0.9

                    self.logger.info(f"🔧 调整后数量: {size}")
                    continue

                # 其他错误，记录并继续重试
                self.logger.warning(f"⚠️ 下单失败 (尝试 {attempt + 1}/{max_retries}): {order_response}")

            except Exception as e:
                self.logger.error(f"❌ 下单异常 (尝试 {attempt + 1}/{max_retries}): {e}")

                if attempt == max_retries - 1:
                    # 最后一次尝试失败，抛出异常
                    raise e

                # 等待一下再重试
                import time
                time.sleep(0.5)

        # 所有重试都失败
        raise Exception(f"下单失败，已重试{max_retries}次")

    def execute_perfect_margin_order(self,
                                   symbol: str,
                                   target_margin: float,
                                   leverage: int,
                                   side: str = "buy") -> Dict[str, Any]:
        """
        执行完美的保证金下单
        使用官方API实现精确的保证金控制

        Args:
            symbol: 交易对符号
            target_margin: 目标保证金金额 (USDT)
            leverage: 杠杆倍数
            side: 交易方向 ('buy' 或 'sell')

        Returns:
            下单执行结果
        """
        try:
            self.logger.info(f"🎯 执行完美保证金下单")
            self.logger.info(f"   交易对: {symbol}")
            self.logger.info(f"   目标保证金: {target_margin} USDT")
            self.logger.info(f"   杠杆倍数: {leverage}x")
            self.logger.info(f"   交易方向: {side}")

            # 导入完美下单系统
            from .perfect_okx_order_system import PerfectOKXOrderSystem

            # 创建完美下单系统实例
            perfect_system = PerfectOKXOrderSystem(self.config_manager)

            # 执行完美下单
            result = perfect_system.place_perfect_margin_order(
                symbol, target_margin, leverage, side
            )

            if result['success']:
                self.logger.info(f"✅ 完美下单成功！")
                self.logger.info(f"   订单ID: {result['order_id']}")
                self.logger.info(f"   实际保证金: ${result['calculation']['actual_margin']:,.2f}")
                self.logger.info(f"   偏差: {result['calculation']['margin_diff_percent']:+.2f}%")

                # 记录交易历史
                trade_record = {
                    'timestamp': time.time(),
                    'symbol': symbol,
                    'action': side.upper(),
                    'type': 'PERFECT_MARGIN',
                    'target_margin': target_margin,
                    'actual_margin': result['calculation']['actual_margin'],
                    'leverage': leverage,
                    'contracts': result['calculation']['contracts'],
                    'order_id': result['order_id'],
                    'quality': result['calculation']['quality']
                }
                self._record_trade(trade_record)

            return result

        except Exception as e:
            self.logger.error(f"完美保证金下单失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def close_all_positions(self) -> Dict[str, Any]:
        """
        一键平全部仓位

        Returns:
            平仓执行结果
        """
        try:
            self.logger.info(f"🔄 开始执行一键平全部仓位")

            # 获取所有持仓 - 增加重试机制
            positions_response = None
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    positions_response = self.account_api.get_positions()
                    if positions_response['code'] == '0':
                        break
                    raise Exception(f"API返回错误: {positions_response.get('msg', 'Unknown error')}")
                except Exception as e:
                    self.logger.warning(f"获取所有持仓失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                    if attempt < max_retries - 1:
                        time.sleep(1 * (attempt + 1))
                    else:
                        self.logger.error(f"获取所有持仓最终失败")
                        raise e

            if positions_response['code'] != '0':
                raise Exception(f"获取持仓失败: {positions_response.get('msg')}")

            positions = positions_response['data']

            if not positions:
                return {
                    'success': True,
                    'message': '当前无持仓，无需平仓',
                    'closed_positions': []
                }

            # 过滤出有效持仓（持仓数量不为0）
            active_positions = []
            for pos in positions:
                pos_size = self._safe_float(pos.get('pos', 0))
                if pos_size != 0:
                    active_positions.append(pos)

            if not active_positions:
                return {
                    'success': True,
                    'message': '当前无有效持仓，无需平仓',
                    'closed_positions': []
                }

            self.logger.info(f"发现 {len(active_positions)} 个有效持仓")

            # 执行平仓
            closed_positions = []
            failed_positions = []

            for pos in active_positions:
                try:
                    inst_id = pos['instId']
                    pos_size = self._safe_float(pos.get('pos', 0))
                    pos_side = pos.get('posSide', 'net')
                    mgn_mode = pos.get('mgnMode', 'cross')

                    self.logger.info(f"平仓 {inst_id}: 数量={pos_size}, 方向={pos_side}")

                    # 使用close_positions API
                    close_response = self.trade_api.close_positions(
                        instId=inst_id,
                        mgnMode=mgn_mode,
                        posSide=pos_side if pos_side != 'net' else None
                    )

                    self.logger.info(f"🔍 平仓API响应: {close_response}")

                    if close_response['code'] == '0':
                        # 检查响应数据结构
                        order_id = 'success'  # 默认值
                        response_data = None

                        try:
                            if close_response.get('data') and len(close_response['data']) > 0:
                                response_data = close_response['data'][0]
                                # 尝试获取订单ID，可能的字段名
                                order_id = (response_data.get('ordId') or
                                          response_data.get('clOrdId') or
                                          response_data.get('algoId') or
                                          response_data.get('id') or
                                          'success')
                            else:
                                # data为空，但API返回成功，说明平仓操作已执行
                                order_id = 'completed'
                        except Exception as parse_error:
                            self.logger.warning(f"解析响应数据失败: {parse_error}")
                            order_id = 'parse_error'

                        closed_positions.append({
                            'symbol': inst_id,
                            'size': pos_size,
                            'side': pos_side,
                            'order_id': order_id,
                            'status': 'success',
                            'response_data': response_data
                        })
                        self.logger.info(f"✅ {inst_id} 平仓成功，订单ID: {order_id}")
                    else:
                        failed_positions.append({
                            'symbol': inst_id,
                            'size': pos_size,
                            'side': pos_side,
                            'error': close_response.get('msg'),
                            'status': 'failed'
                        })
                        self.logger.error(f"❌ {inst_id} 平仓失败: {close_response.get('msg')}")

                except Exception as e:
                    failed_positions.append({
                        'symbol': pos.get('instId', 'unknown'),
                        'size': pos.get('pos', 0),
                        'error': str(e),
                        'status': 'failed'
                    })
                    self.logger.error(f"❌ 平仓异常: {e}")

            # 生成结果
            total_positions = len(active_positions)
            successful_closes = len(closed_positions)
            failed_closes = len(failed_positions)

            result = {
                'success': failed_closes == 0,
                'total_positions': total_positions,
                'successful_closes': successful_closes,
                'failed_closes': failed_closes,
                'closed_positions': closed_positions,
                'failed_positions': failed_positions
            }

            if failed_closes == 0:
                result['message'] = f'✅ 一键平仓成功！共平仓 {successful_closes} 个持仓'
                self.logger.info(f"🎉 一键平仓完全成功！共平仓 {successful_closes} 个持仓")
            else:
                result['message'] = f'⚠️ 部分平仓成功：成功 {successful_closes} 个，失败 {failed_closes} 个'
                self.logger.warning(f"⚠️ 一键平仓部分成功：成功 {successful_closes} 个，失败 {failed_closes} 个")

            # 记录平仓操作
            trade_record = {
                'timestamp': time.time(),
                'action': 'CLOSE_ALL',
                'type': 'BATCH_CLOSE',
                'total_positions': total_positions,
                'successful_closes': successful_closes,
                'failed_closes': failed_closes,
                'details': result
            }
            self._record_trade(trade_record)

            return result

        except Exception as e:
            self.logger.error(f"一键平仓失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': f'❌ 一键平仓失败: {str(e)}'
            }
