# DeepSeek 驱动的全自动加密货币合约交易系统设计文档

## 1. 项目概述 (Project Overview)

- **项目名称**：DeepSeek 驱动的全自动加密货币合约交易系统  
- **一句话简介**：基于 Python、OKX 接口、TA-Lib 与 DeepSeek 实时决策，实现 24×7 无人值守的多合约单向持仓自动下单。  
- **目标用户**：个人量化交易者（您本人），无需盯盘，即可全自动化执行策略。  
- **核心痛点**：人为情绪干扰、手动下单响应慢、易错漏；系统自动决策、快速执行，避免情绪带来的决策失误。

## 2. 核心功能模块 (Core Features)

1. **配置管理模块**  
   - **描述**：所有全局参数（OKX API Key/Secret、回溯天数、交易对列表、单向持仓方向、多空选择、测试/实盘标识）均存储于 SQLite3 数据库 `credentials.db`。  
   - **关键流程**：  
     1. 启动时连接或创建 `credentials.db`  
     2. 从 `credentials` 表读取 API Key/Secret  
     3. 从 `settings` 表读取其余参数  
     4. 校验完整性与格式，缺失/异常则抛错并报警  

2. **数据获取模块**  
   - **描述**：定时（如每 1 分钟）调用 OKX 合约接口，拉取 K 线数据及账户持仓状态。  
   - **关键流程**：  
     1. 获取 1/5/15 分钟 K 线  
     2. 获取账户保证金、持仓量  
     3. 缓存原始数据至内存或轻量级存储  

3. **指标计算模块**  
   - **描述**：使用 TA-Lib 批量计算 MA、EMA、RSI、MACD、BOLL 等，并支持自定义配置。  
   - **关键流程**：  
     1. 读取最新 N 条 K 线（N 由“回溯天数”或“指标窗口长度”决定）  
     2. 加载各指标参数  
     3. 调用 TA-Lib 计算，异常时记录并跳过  
     4. 对齐、填充、归一化后，组装成时间序列向量  

4. **决策引擎集成模块**  
   - **描述**：将指标向量发送给 DeepSeek，获取 BUY/SELL/HOLD 决策。  
   - **关键流程**：  
     1. 构造 DeepSeek 请求（指标向量 + 历史信号）  
     2. 等待并解析响应，映射为交易指令  

5. **交易执行模块**  
   - **描述**：依照决策信号，通过 `python-okx` 下单或平仓，并附加止盈/止损参数。  
   - **关键流程**：  
     1. 收到 BUY/SELL 信号，检查持仓与可用保证金  
     2. 构建下单请求，包含止盈价与止损价  
     3. 发起下单/平仓，保持单向持仓  
     4. 记录订单结果与错误信息  

6. **风控与错误处理模块**  
   - **描述**：持续监控持仓与账户风险，自动止盈/止损，并处理 API 异常或网络故障。  
   - **关键流程**：  
     1. 定时检查杠杆率、未实现盈亏、浮动盈亏  
     2. 超阈值时触发止损或报警（邮件/钉钉/Slack）  
     3. 持仓期间监测止盈/止损条件并执行平仓  
     4. 捕捉 API/网络异常，重试或切换备用节点  

7. **多合约管理模块**  
   - **描述**：并行管理多个交易对，每个交易对独立执行全流程。  
   - **关键流程**：  
     1. 动态生成多个交易单元  
     2. 各单元独立完成：数据获取 → 指标计算 → 决策 → 下单  
     3. 聚合日志与运行状态，集中监控  

8. **日志与监控模块**  
   - **描述**：记录系统与交易日志，提供健康检查接口。  
   - **关键流程**：  
     1. 使用 `logging` 输出至文件与控制台  
     2. 生成实时/每日运行报告（成交明细、盈亏统计）  
     3. 提供 HTTP 或 CLI 接口查询状态  

9. **Web 仪表盘模块**  
   - **描述**：基于 Jinja2 模板生成的服务端渲染页面，展示系统状态、交易数据，并支持手动干预。  
   - **关键流程**：  
     1. 后端（Flask/FastAPI + Jinja2）暴露 RESTful API  
     2. 使用 Jinja2 渲染折线图（Chart.js）、表格与指标卡片  
     3. 页面包含启动/停止交易、调整止盈/止损、手动下单等操作  
     4. 简易登录认证与日志审计  

## 3. 非功能性需求 (Non-Functional Requirements)

1. **部署环境**  
   - **模式**：全本地部署，运行于常规工作站或自有服务器  
2. **性能要求**  
   - **延迟**：单次全流程 ≤ 3 秒  
   - **并发**：支持 5–10 个交易对并行，未来可增至 20+  
3. **可靠性与可用性**  
   - **可用性**：24×7 无人值守运行，自动重启与报警  
4. **安全与权限**  
   - **密钥保护**：SQLite3 存储并限定文件权限；HTTPS 加密所有内部通信  
   - **访问控制**：仪表盘登录认证，记录操作日志  


## 4. 开发里程碑 (Milestones & Timeline)

| 阶段              | 周期 (周) | 内容                                                         |
| ----------------- | --------- | ------------------------------------------------------------ |
| **MVP（核心功能）** | 3         | 配置管理、数据获取、指标计算、决策集成、交易执行、日志与监控 |
| **增强风控**      | 1         | 止盈止损、持仓监测、自动报警                                 |
| **多合约并行**    | 1         | 多交易对管理、并行执行                                       |
| **Web 仪表盘**    | 2         | Flask/FastAPI + Jinja2 页面，手动操作入口、登录认证          |
| **测试与优化**    | 1         | 长时运行测试、性能调优、文档编写                            |

## 5. 风险与应对策略 (Risks & Mitigation)

- **本地环境宕机**：配置 UPS 与网络监控，自动重启或切换备用网络。  
- **API 异常/限流**：多节点重试、自动切换测试/实盘环境。  
- **指标计算超时**：异步并行计算，超时跳过并记录日志。  
- **策略适应性**：模块化策略接口，支持单独调参。  
- **密钥泄露**：数据库加密存储，文件权限最小化。

