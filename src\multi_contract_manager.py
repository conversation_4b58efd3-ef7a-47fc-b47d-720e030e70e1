"""
多合约管理模块 - Multi Contract Manager
负责管理多个交易对的并行交易
使用多线程实现并发处理，支持异步操作
"""

import threading
import time
import queue
import asyncio
from typing import Dict, List, Optional, Any, Callable
import logging
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
from .config_manager import ConfigManager

class MultiContractManager:
    """多合约管理器 - 并行管理多个交易对"""
    
    def __init__(self,
                 config_manager: ConfigManager,
                 data_fetcher,
                 indicator_calculator,
                 trade_executor,
                 risk_manager):
        """
        初始化多合约管理器

        Args:
            config_manager: 配置管理器
            data_fetcher: 数据获取器
            indicator_calculator: 指标计算器
            trade_executor: 交易执行器
            risk_manager: 风险管理器
        """
        self.config_manager = config_manager
        self.data_fetcher = data_fetcher
        self.indicator_calculator = indicator_calculator
        self.trade_executor = trade_executor
        self.risk_manager = risk_manager
        
        # 使用统一日志系统
        from .logger_monitor import get_unified_logger
        self.logger = get_unified_logger(__name__)

        # 初始化专门的决策引擎
        try:
            from .optimized_prompts.smart_hunter_engine import SmartHunterEngine
            from .optimized_prompts.risk_guardian_engine import RiskGuardianEngine
            self.open_position_engine = SmartHunterEngine(self.config_manager)
            self.position_monitor_engine = RiskGuardianEngine(self.config_manager)
            self.logger.info("专门决策引擎初始化成功")
        except Exception as e:
            self.logger.error(f"专门决策引擎初始化失败: {e}")
            raise RuntimeError(f"专门决策引擎是必需的，初始化失败: {e}")

        # 线程管理
        self.max_workers = 5  # 最大并发线程数
        self.executor = ThreadPoolExecutor(max_workers=self.max_workers)
        
        # 交易对状态管理
        self.contract_states = {}
        self.contract_locks = {}
        
        # 任务队列
        self.task_queue = queue.Queue()
        self.result_queue = queue.Queue()
        
        # 运行状态
        self.is_running = False
        self.worker_threads = []
        
        # 性能监控
        self.performance_metrics = {
            'total_processed': 0,
            'successful_trades': 0,
            'failed_trades': 0,
            'avg_processing_time': 0.0,
            'last_update': datetime.now()
        }
        
        # 初始化交易对
        self._initialize_contracts()
        
        self.logger.info("多合约管理器初始化完成")

    def reinitialize_contracts(self) -> None:
        """重新初始化交易对（当配置更改时调用）"""
        self.logger.info("重新初始化交易对...")
        self._initialize_contracts()
        self.logger.info("交易对重新初始化完成")
    
    def _initialize_contracts(self) -> None:
        """初始化交易对状态"""
        try:
            # 清空旧的状态，确保只处理当前激活的交易对
            self.contract_states.clear()
            self.contract_locks.clear()

            active_pairs = self.config_manager.get_active_trading_pairs()
            self.logger.info(f"获取到{len(active_pairs)}个活跃交易对: {list(active_pairs.keys())}")

            for symbol, config in active_pairs.items():
                try:
                    self.contract_states[symbol] = {
                        'status': 'IDLE',
                        'last_update': None,  # 初始化时不设置时间，允许立即处理
                        'last_trade': None,
                        'error_count': 0,
                        'success_count': 0,
                        'config': config,
                        'current_position': None,
                        'last_decision': None
                    }

                    # 为每个交易对创建锁
                    self.contract_locks[symbol] = threading.Lock()
                    self.logger.debug(f"初始化交易对: {symbol}")

                except Exception as e:
                    self.logger.error(f"初始化交易对{symbol}失败: {e}")
                    continue

            self.logger.info(f"成功初始化了{len(self.contract_states)}个交易对")

        except Exception as e:
            self.logger.error(f"初始化交易对失败: {e}")
            import traceback
            self.logger.error(f"详细错误: {traceback.format_exc()}")
    
    def start(self) -> bool:
        """启动多合约管理器"""
        try:
            if self.is_running:
                self.logger.warning("多合约管理器已在运行")
                return True

            # 重新初始化交易对（确保获取最新配置）
            self._initialize_contracts()

            self.is_running = True

            # 启动工作线程
            for i in range(self.max_workers):
                worker_thread = threading.Thread(
                    target=self._worker_thread,
                    name=f"ContractWorker-{i}",
                    daemon=True
                )
                worker_thread.start()
                self.worker_threads.append(worker_thread)

            # 启动主调度线程
            scheduler_thread = threading.Thread(
                target=self._scheduler_thread,
                name="ContractScheduler",
                daemon=True
            )
            scheduler_thread.start()
            self.worker_threads.append(scheduler_thread)

            self.logger.info("多合约管理器启动成功")
            return True

        except Exception as e:
            self.logger.error(f"启动多合约管理器失败: {e}")
            self.is_running = False
            return False
    
    def stop(self) -> bool:
        """停止多合约管理器"""
        try:
            self.is_running = False

            # 等待所有任务完成
            self.executor.shutdown(wait=True)

            self.logger.info("多合约管理器已停止")
            return True

        except Exception as e:
            self.logger.error(f"停止多合约管理器失败: {e}")
            return False
    
    def _scheduler_thread(self) -> None:
        """主调度线程"""
        try:
            while self.is_running:
                try:
                    # 获取当前激活的交易对，而不是使用历史状态
                    active_pairs = self.config_manager.get_active_trading_pairs()

                    # 为每个当前激活的交易对创建处理任务
                    for symbol in active_pairs.keys():
                        if symbol in self.contract_states and self._should_process_contract(symbol):
                            task = {
                                'symbol': symbol,
                                'timestamp': datetime.now(),
                                'task_type': 'PROCESS_CONTRACT'
                            }
                            self.task_queue.put(task)
                    
                    # 处理结果队列
                    self._process_results()
                    
                    # 更新性能指标
                    self._update_performance_metrics()
                    
                    # 休眠一段时间
                    time.sleep(10)  # 每10秒调度一次
                    
                except Exception as e:
                    self.logger.error(f"调度线程异常: {e}")
                    time.sleep(5)
                    
        except Exception as e:
            self.logger.error(f"调度线程失败: {e}")
    
    def _worker_thread(self) -> None:
        """工作线程"""
        try:
            while self.is_running:
                try:
                    # 从任务队列获取任务
                    task = self.task_queue.get(timeout=5)
                    
                    if task['task_type'] == 'PROCESS_CONTRACT':
                        result = self._process_single_contract(task['symbol'])
                        
                        # 将结果放入结果队列
                        self.result_queue.put({
                            'symbol': task['symbol'],
                            'result': result,
                            'timestamp': datetime.now()
                        })
                    
                    self.task_queue.task_done()
                    
                except queue.Empty:
                    continue
                except Exception as e:
                    self.logger.error(f"工作线程异常: {e}")
                    
        except Exception as e:
            self.logger.error(f"工作线程失败: {e}")
    
    def _should_process_contract(self, symbol: str) -> bool:
        """判断是否应该处理某个交易对"""
        try:
            state = self.contract_states.get(symbol, {})
            
            # 检查状态
            if state.get('status') == 'PROCESSING':
                return False
            
            # 检查错误次数
            if state.get('error_count', 0) >= 5:
                # 如果错误次数过多，暂停一段时间
                last_update = state.get('last_update', datetime.now())
                if datetime.now() - last_update < timedelta(minutes=30):
                    return False
                else:
                    # 重置错误计数
                    state['error_count'] = 0
            
            # 检查最后更新时间
            last_update = state.get('last_update')
            if last_update is None:
                # 如果没有上次更新时间，允许处理
                return True

            min_interval = timedelta(minutes=1)  # 最小处理间隔

            return datetime.now() - last_update >= min_interval
            
        except Exception as e:
            self.logger.warning(f"检查处理条件失败: {symbol} - {e}")
            return False
    
    def _process_single_contract(self, symbol: str) -> Dict[str, Any]:
        """处理单个交易对"""
        start_time = time.time()
        
        try:
            # 检查交易对是否已初始化
            if symbol not in self.contract_states:
                self.logger.warning(f"交易对{symbol}未初始化，跳过处理")
                return {
                    'success': False,
                    'error': f'交易对{symbol}未初始化',
                    'symbol': symbol
                }

            if symbol not in self.contract_locks:
                self.logger.warning(f"交易对{symbol}缺少锁，创建新锁")
                self.contract_locks[symbol] = threading.Lock()

            # 获取锁，确保同一时间只有一个线程处理同一个交易对
            with self.contract_locks[symbol]:
                # 更新状态
                self.contract_states[symbol]['status'] = 'PROCESSING'
                self.contract_states[symbol]['last_update'] = datetime.now()
                
                # 1. 首先检查当前持仓状态
                current_position = self._get_current_position(symbol)
                position_info = self._analyze_position_status(symbol, current_position)
                self.logger.info(f"持仓状态分析: {symbol} - {position_info['status']} - 大小: {position_info['size']}")

                # 2. 获取市场数据
                market_data = self._get_market_data(symbol)
                if not market_data:
                    raise Exception("获取市场数据失败")

                # 3. 计算技术指标
                indicators = self._calculate_indicators(symbol, market_data)
                if not indicators:
                    raise Exception("计算技术指标失败")

                # 4. 获取账户余额（为后续风险检查准备）
                account_balance = self._get_account_balance()
                current_positions = {symbol: current_position} if current_position else {}

                # 5. AI决策（根据持仓状态选择专门引擎）
                position_status = position_info.get('status', 'NO_POSITION')

                if position_status == 'NO_POSITION':
                    # 无持仓时使用开仓引擎
                    self.logger.info(f"🎯 {symbol} - 使用开仓引擎（机会猎手模式）")
                    decision = self.open_position_engine.make_open_decision(
                        symbol, indicators, market_data
                    )
                else:
                    # 有持仓时使用持仓监控引擎
                    self.logger.info(f"🛡️ {symbol} - 使用持仓监控引擎（风险守护者模式）")
                    decision = self.position_monitor_engine.make_monitor_decision(
                        symbol, indicators, market_data, position_info
                    )

                # 安全检查：确保决策不为None
                if decision is None:
                    self.logger.error(f"{symbol} - AI决策返回None，使用默认HOLD决策")
                    decision = {
                        "decision": "HOLD",
                        "confidence": 0.1,
                        "reasoning": "AI决策失败，采用默认保守策略",
                        "leverage": 1,
                        "stop_loss": 0,
                        "take_profit": 0,
                        "investment_amount": 0
                    }

                # 6. 基于持仓状态的交易逻辑判断
                trade_logic_result = self._evaluate_trade_logic(symbol, decision, position_info)

                self.logger.info(f"交易逻辑结果: {symbol}")
                self.logger.info(f"  should_trade: {trade_logic_result['should_trade']}")
                self.logger.info(f"  reason: {trade_logic_result['reason']}")

                # 7. 执行交易前的风险检查和仓位调整
                if trade_logic_result['should_trade']:
                    final_decision = trade_logic_result['final_decision']
                    decision_action = final_decision.get('decision', 'HOLD')
                    
                    # 对于CLOSE决策，跳过开仓风险检查，直接执行平仓
                    if decision_action == 'CLOSE':
                        self.logger.info(f"AI决策为CLOSE，直接执行平仓: {symbol}")
                        trade_result = self.trade_executor.execute_decision(
                            symbol, final_decision, current_position
                        )
                    else:
                        # 对于开仓决策，进行风险检查
                        risk_check = self.risk_manager.check_pre_trade_risk(
                            symbol=symbol,
                            decision=final_decision,
                            positions=current_positions,
                            balance=account_balance
                        )

                        if not risk_check['passed'] and risk_check.get('errors'):
                            self.logger.warning(f"风险检查未通过: {symbol} - {risk_check['errors']}")
                            trade_result = {
                                'success': False,
                                'action': 'SKIPPED',
                                'reason': f'风险检查未通过: {risk_check["errors"]}',
                                'message': '风险检查失败，跳过交易'
                            }
                        else:
                            # 调整仓位大小基于风险检查结果
                            original_size = final_decision.get('position_size', 0.1)
                            allowed_size = risk_check.get('allowed_size', 0.1)
                            final_size = min(original_size, allowed_size)

                            self.logger.info(f"仓位大小调整: {symbol}")
                            self.logger.info(f"  原始仓位: {original_size}")
                            self.logger.info(f"  风险允许: {allowed_size}")
                            self.logger.info(f"  最终仓位: {final_size}")

                            final_decision['position_size'] = final_size

                            if final_size <= 0:
                                self.logger.warning(f"仓位大小为0，跳过交易: {symbol}")
                                trade_result = {
                                    'success': False,
                                    'action': 'SKIPPED',
                                    'reason': f'风险检查后仓位大小为0 (原始: {original_size}, 允许: {allowed_size})',
                                    'message': '仓位大小不足，跳过交易'
                                }
                            else:
                                self.logger.info(f"执行交易决策: {symbol} -> {decision_action}")
                                trade_result = self.trade_executor.execute_decision(
                                    symbol, final_decision, current_position
                                )
                else:
                    trade_result = {
                        'success': True,
                        'action': 'SKIPPED',
                        'reason': trade_logic_result['reason'],
                        'action': 'HOLD',
                        'message': '保持当前仓位'
                    }
                
                # 更新状态
                self.contract_states[symbol]['status'] = 'IDLE'
                self.contract_states[symbol]['last_decision'] = decision
                self.contract_states[symbol]['current_position'] = current_position
                
                if trade_result.get('success', False):
                    self.contract_states[symbol]['success_count'] += 1
                    self.contract_states[symbol]['last_trade'] = trade_result
                else:
                    self.contract_states[symbol]['error_count'] += 1
                
                processing_time = time.time() - start_time
                
                return {
                    'success': trade_result.get('success', False),
                    'symbol': symbol,
                    'decision': decision,
                    'trade_result': trade_result,
                    'processing_time': processing_time,
                    'timestamp': datetime.now().isoformat()
                }
                
        except Exception as e:
            # 更新错误状态
            self.contract_states[symbol]['status'] = 'ERROR'
            self.contract_states[symbol]['error_count'] += 1
            
            processing_time = time.time() - start_time
            
            self.logger.error(f"处理交易对失败: {symbol} - {e}")
            
            return {
                'success': False,
                'symbol': symbol,
                'error': str(e),
                'processing_time': processing_time,
                'timestamp': datetime.now().isoformat()
            }
    
    def _get_market_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """获取多时间周期市场数据"""
        try:
            # 定义多时间周期（使用OKX API正确格式）- 优化为更实用的交易周期
            timeframes = ['1H', '30m', '15m', '5m', '1m']

            # 获取多时间周期K线数据
            self.logger.debug(f"获取多时间周期K线数据: {symbol}")
            multi_timeframe_data = self.data_fetcher.get_multiple_timeframes(
                symbol, timeframes, limit=100
            )

            if not multi_timeframe_data:
                self.logger.warning(f"多时间周期K线数据为空: {symbol}")
                return None

            # 验证主要交易周期数据
            primary_timeframe = '15m'  # 主要交易周期
            if primary_timeframe not in multi_timeframe_data or multi_timeframe_data[primary_timeframe].empty:
                self.logger.warning(f"主要交易周期{primary_timeframe}数据为空: {symbol}")
                return None

            # 获取行情数据
            self.logger.debug(f"获取行情数据: {symbol}")
            ticker_data = self.data_fetcher.get_ticker(symbol)
            if not ticker_data:
                self.logger.warning(f"行情数据为空: {symbol}")
                return None

            # 获取风险参数配置
            pair_config = self.config_manager.get(f'trading_pairs.{symbol}', {})
            max_loss_pct = pair_config.get('stop_loss', 2.0)  # 默认2%
            max_profit_pct = pair_config.get('take_profit', 4.0)  # 默认4%

            return {
                'multi_timeframe_kline': multi_timeframe_data,  # 多时间周期K线数据
                'primary_kline': multi_timeframe_data[primary_timeframe],  # 主要交易周期数据
                'kline': multi_timeframe_data[primary_timeframe],  # 保持向后兼容
                'ticker': ticker_data,
                'last_price': ticker_data.get('last_price', 0),
                'change_24h': ticker_data.get('change_24h', 0),
                'volume_24h': ticker_data.get('volume_24h', 0),
                'high_24h': ticker_data.get('high_24h', 0),
                'low_24h': ticker_data.get('low_24h', 0),
                'max_loss_pct': max_loss_pct,
                'max_profit_pct': max_profit_pct,
                'primary_timeframe': primary_timeframe,  # 标记主要交易周期
                'available_timeframes': timeframes  # 可用时间周期列表
            }

        except Exception as e:
            self.logger.error(f"获取市场数据失败: {symbol} - {e}")
            import traceback
            self.logger.debug(f"详细错误: {traceback.format_exc()}")
            return None

    def _get_current_leverage(self, symbol: str) -> int:
        """获取当前杠杆倍数"""
        try:
            # 先从持仓信息中获取杠杆
            positions = self.data_fetcher.get_positions(symbol)
            if symbol in positions:
                leverage = positions[symbol].get('leverage', 1)
                if leverage > 1:
                    self.logger.debug(f"从持仓获取杠杆: {symbol} = {leverage}x")
                    return int(leverage)

            # 如果没有持仓，只返回配置中的目标杠杆，不要设置
            pair_config = self.config_manager.get(f'trading_pairs.{symbol}', {})
            target_leverage = pair_config.get('max_leverage', 3)  # 使用正确的配置字段

            self.logger.debug(f"无持仓时返回配置杠杆: {symbol} = {target_leverage}x")
            return target_leverage

        except Exception as e:
            self.logger.warning(f"获取杠杆失败: {symbol} - {e}")
            return 3  # 默认返回3倍杠杆（与配置一致）

    def _set_leverage(self, symbol: str, leverage: int):
        """设置杠杆倍数"""
        try:
            # 使用账户API设置杠杆
            response = self.data_fetcher.account_api.set_leverage(
                instId=symbol,
                lever=str(leverage),
                mgnMode='cross'  # 全仓模式
            )

            if response['code'] != '0':
                raise Exception(f"设置杠杆API错误: {response.get('msg', 'Unknown error')}")

            self.logger.debug(f"杠杆设置API调用成功: {symbol} = {leverage}x")

        except Exception as e:
            self.logger.error(f"设置杠杆失败: {symbol} - {e}")
            raise
    
    def _calculate_indicators(self, symbol: str, market_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """计算多时间周期技术指标"""
        try:
            multi_timeframe_data = market_data['multi_timeframe_kline']
            primary_timeframe = market_data['primary_timeframe']

            # 计算多时间周期指标
            multi_indicators = self.indicator_calculator.calculate_multi_timeframe_indicators(
                multi_timeframe_data, symbol
            )

            if not multi_indicators:
                self.logger.warning(f"多时间周期指标计算失败: {symbol}")
                return None

            # 获取主要交易周期的最新指标值
            primary_indicators = multi_indicators.get(primary_timeframe, {})

            # 添加多时间周期分析结果
            primary_indicators['multi_timeframe_analysis'] = self._analyze_multi_timeframe_signals(multi_indicators)
            primary_indicators['timeframe_signals'] = multi_indicators  # 保留所有时间周期的信号

            return primary_indicators

        except Exception as e:
            self.logger.error(f"计算技术指标失败: {symbol} - {e}")
            return None

    def _analyze_multi_timeframe_signals(self, multi_indicators: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """分析多时间周期信号"""
        try:
            analysis = {
                'trend_alignment': 'NEUTRAL',  # 趋势一致性
                'momentum_strength': 'NEUTRAL',  # 动量强度
                'entry_quality': 'NEUTRAL',  # 入场质量
                'timeframe_scores': {},  # 各时间周期评分
                'overall_signal': 'NEUTRAL'  # 综合信号
            }

            timeframe_weights = {
                '1H': 0.30,   # 长期趋势权重最高
                '30m': 0.25,  # 中期趋势确认
                '15m': 0.25,  # 主要交易周期
                '5m': 0.15,   # 短期入场
                '1m': 0.05    # 实时监控
            }

            trend_scores = []
            momentum_scores = []

            for timeframe, indicators in multi_indicators.items():
                if not indicators:
                    continue

                # 计算趋势评分
                trend_score = self._calculate_trend_score(indicators)
                momentum_score = self._calculate_momentum_score(indicators)

                analysis['timeframe_scores'][timeframe] = {
                    'trend_score': trend_score,
                    'momentum_score': momentum_score,
                    'weight': timeframe_weights.get(timeframe, 0.1)
                }

                # 加权计算
                weight = timeframe_weights.get(timeframe, 0.1)
                trend_scores.append(trend_score * weight)
                momentum_scores.append(momentum_score * weight)

            # 计算综合评分
            overall_trend = sum(trend_scores)
            overall_momentum = sum(momentum_scores)

            # 判断趋势一致性
            if overall_trend > 0.6:
                analysis['trend_alignment'] = 'BULLISH'
            elif overall_trend < -0.6:
                analysis['trend_alignment'] = 'BEARISH'
            else:
                analysis['trend_alignment'] = 'NEUTRAL'

            # 判断动量强度
            if overall_momentum > 0.6:
                analysis['momentum_strength'] = 'STRONG'
            elif overall_momentum < -0.6:
                analysis['momentum_strength'] = 'WEAK'
            else:
                analysis['momentum_strength'] = 'NEUTRAL'

            # 判断入场质量
            if abs(overall_trend) > 0.7 and abs(overall_momentum) > 0.6:
                analysis['entry_quality'] = 'HIGH'
            elif abs(overall_trend) > 0.5 and abs(overall_momentum) > 0.4:
                analysis['entry_quality'] = 'MEDIUM'
            else:
                analysis['entry_quality'] = 'LOW'

            # 综合信号
            combined_score = (overall_trend + overall_momentum) / 2
            if combined_score > 0.6:
                analysis['overall_signal'] = 'STRONG_BUY'
            elif combined_score > 0.3:
                analysis['overall_signal'] = 'BUY'
            elif combined_score < -0.6:
                analysis['overall_signal'] = 'STRONG_SELL'
            elif combined_score < -0.3:
                analysis['overall_signal'] = 'SELL'
            else:
                analysis['overall_signal'] = 'HOLD'

            return analysis

        except Exception as e:
            self.logger.error(f"多时间周期信号分析失败: {e}")
            return {
                'trend_alignment': 'NEUTRAL',
                'momentum_strength': 'NEUTRAL',
                'entry_quality': 'LOW',
                'overall_signal': 'HOLD'
            }

    def _calculate_trend_score(self, indicators: Dict[str, Any]) -> float:
        """计算趋势评分 (-1到1之间)"""
        try:
            score = 0.0
            count = 0

            # MA趋势评分
            if 'sma_5' in indicators and 'sma_20' in indicators:
                sma_5 = indicators['sma_5']
                sma_20 = indicators['sma_20']
                if sma_5 > sma_20:
                    score += 0.3
                else:
                    score -= 0.3
                count += 1

            # EMA趋势评分
            if 'ema_5' in indicators and 'ema_20' in indicators:
                ema_5 = indicators['ema_5']
                ema_20 = indicators['ema_20']
                if ema_5 > ema_20:
                    score += 0.3
                else:
                    score -= 0.3
                count += 1

            # MACD趋势评分
            if 'macd' in indicators and 'macd_signal' in indicators:
                macd = indicators['macd']
                macd_signal = indicators['macd_signal']
                if macd > macd_signal:
                    score += 0.4
                else:
                    score -= 0.4
                count += 1

            return score / max(count, 1)

        except Exception as e:
            self.logger.warning(f"计算趋势评分失败: {e}")
            return 0.0

    def _calculate_momentum_score(self, indicators: Dict[str, Any]) -> float:
        """计算动量评分 (-1到1之间)"""
        try:
            score = 0.0
            count = 0

            # RSI动量评分
            if 'rsi' in indicators:
                rsi = indicators['rsi']
                if rsi > 70:
                    score -= 0.4  # 超买
                elif rsi < 30:
                    score += 0.4  # 超卖
                elif 50 < rsi < 70:
                    score += 0.2  # 偏强
                elif 30 < rsi < 50:
                    score -= 0.2  # 偏弱
                count += 1

            # STOCH动量评分
            if 'stoch_k' in indicators:
                stoch_k = indicators['stoch_k']
                if stoch_k > 80:
                    score -= 0.3  # 超买
                elif stoch_k < 20:
                    score += 0.3  # 超卖
                count += 1

            # ADX强度评分
            if 'adx' in indicators:
                adx = indicators['adx']
                if adx > 25:
                    score += 0.3  # 趋势强劲
                elif adx < 20:
                    score -= 0.1  # 趋势疲弱
                count += 1

            return score / max(count, 1)

        except Exception as e:
            self.logger.warning(f"计算动量评分失败: {e}")
            return 0.0
    
    def _get_current_position(self, symbol: str) -> Optional[Dict[str, Any]]:
        """获取当前持仓"""
        try:
            positions = self.data_fetcher.get_positions(symbol)
            return positions.get(symbol)

        except Exception as e:
            self.logger.warning(f"获取持仓信息失败: {symbol} - {e}")
            return None

    def _analyze_position_status(self, symbol: str, current_position: Dict[str, Any]) -> Dict[str, Any]:
        """分析当前持仓状态"""
        # 获取当前杠杆倍数
        current_leverage = self._get_current_leverage(symbol)

        if not current_position:
            return {
                'status': 'NO_POSITION',
                'size': 0,
                'side': None,
                'avg_price': 0,
                'unrealized_pnl': 0,
                'unrealized_pnl_ratio': 0,
                'leverage': current_leverage,  # 添加杠杆信息
                'margin': 0  # 添加保证金信息
            }

        size = current_position.get('size', 0)
        # 从OKX API获取保证金信息，字段名为imr (Initial Margin Requirement)
        margin = current_position.get('imr', current_position.get('margin', 0))

        if size == 0:
            return {
                'status': 'NO_POSITION',
                'size': 0,
                'side': None,
                'avg_price': 0,
                'unrealized_pnl': 0,
                'unrealized_pnl_ratio': 0,
                'leverage': current_leverage,  # 添加杠杆信息
                'margin': 0  # 添加保证金信息
            }
        elif size > 0:
            return {
                'status': 'LONG_POSITION',
                'size': size,
                'side': 'long',
                'avg_price': current_position.get('avg_price', 0),
                'unrealized_pnl': current_position.get('unrealized_pnl', 0),
                'unrealized_pnl_ratio': current_position.get('unrealized_pnl_ratio', 0),
                'leverage': current_leverage,  # 添加杠杆信息
                'margin': margin  # 添加保证金信息
            }
        else:
            return {
                'status': 'SHORT_POSITION',
                'size': abs(size),
                'side': 'short',
                'avg_price': current_position.get('avg_price', 0),
                'unrealized_pnl': current_position.get('unrealized_pnl', 0),
                'unrealized_pnl_ratio': current_position.get('unrealized_pnl_ratio', 0),
                'leverage': current_leverage,  # 添加杠杆信息
                'margin': margin  # 添加保证金信息
            }

    def _evaluate_trade_logic(self, symbol: str, ai_decision: Dict[str, Any], position_info: Dict[str, Any]) -> Dict[str, Any]:
        """基于持仓状态评估交易逻辑"""
        decision_action = ai_decision.get('decision', 'HOLD')
        current_status = position_info['status']

        self.logger.info(f"交易逻辑评估: {symbol} - AI决策: {decision_action}, 当前状态: {current_status}")

        # HOLD决策 - 如果有持仓，需要执行止盈止损更新
        if decision_action == 'HOLD':
            if current_status in ['LONG_POSITION', 'SHORT_POSITION']:
                return {
                    'should_trade': True,
                    'reason': f'AI决策为HOLD，当前有{current_status}，更新止盈止损',
                    'final_decision': ai_decision
                }
            else:
                return {
                    'should_trade': False,
                    'reason': f'AI决策为HOLD，当前无持仓，保持当前状态',
                    'final_decision': ai_decision
                }

        # CLOSE决策 - 只有在有持仓时才执行
        if decision_action == 'CLOSE':
            if current_status == 'NO_POSITION':
                return {
                    'should_trade': False,
                    'reason': f'AI决策为CLOSE，但当前无持仓，跳过',
                    'final_decision': ai_decision
                }
            else:
                return {
                    'should_trade': True,
                    'reason': f'AI决策为CLOSE，当前有{current_status}，执行平仓',
                    'final_decision': ai_decision
                }

        # BUY决策逻辑
        if decision_action == 'BUY':
            if current_status == 'NO_POSITION':
                return {
                    'should_trade': True,
                    'reason': f'AI决策为BUY，当前无持仓，执行开多头',
                    'final_decision': ai_decision
                }
            elif current_status == 'LONG_POSITION':
                return {
                    'should_trade': False,
                    'reason': f'AI决策为BUY，但已有多头持仓，避免重复开仓',
                    'final_decision': ai_decision
                }
            elif current_status == 'SHORT_POSITION':
                # 先平空头，再开多头
                return {
                    'should_trade': True,
                    'reason': f'AI决策为BUY，当前有空头持仓，先平空头再开多头',
                    'final_decision': {**ai_decision, 'decision': 'CLOSE_AND_REVERSE', 'target_side': 'long'}
                }

        # SELL决策逻辑
        if decision_action == 'SELL':
            if current_status == 'NO_POSITION':
                return {
                    'should_trade': True,
                    'reason': f'AI决策为SELL，当前无持仓，执行开空头',
                    'final_decision': ai_decision
                }
            elif current_status == 'SHORT_POSITION':
                return {
                    'should_trade': False,
                    'reason': f'AI决策为SELL，但已有空头持仓，避免重复开仓',
                    'final_decision': ai_decision
                }
            elif current_status == 'LONG_POSITION':
                # 先平多头，再开空头
                return {
                    'should_trade': True,
                    'reason': f'AI决策为SELL，当前有多头持仓，先平多头再开空头',
                    'final_decision': {**ai_decision, 'decision': 'CLOSE_AND_REVERSE', 'target_side': 'short'}
                }

        # 默认情况
        return {
            'should_trade': False,
            'reason': f'未知的AI决策: {decision_action}',
            'final_decision': ai_decision
        }
    
    def _get_account_balance(self) -> Dict[str, Any]:
        """获取账户余额"""
        try:
            return self.data_fetcher.get_account_balance()
        except Exception as e:
            self.logger.warning(f"获取账户余额失败: {e}")
            return {}
    
    def _process_results(self) -> None:
        """处理结果队列"""
        try:
            while not self.result_queue.empty():
                try:
                    result_data = self.result_queue.get_nowait()
                    symbol = result_data['symbol']
                    result = result_data['result']
                    
                    # 更新性能指标
                    self.performance_metrics['total_processed'] += 1
                    
                    if result.get('success', False):
                        self.performance_metrics['successful_trades'] += 1
                        self.logger.info(f"交易对处理成功: {symbol}")
                    else:
                        self.performance_metrics['failed_trades'] += 1
                        self.logger.warning(f"交易对处理失败: {symbol} - {result.get('error', '未知错误')}")
                    
                    # 更新平均处理时间
                    processing_time = result.get('processing_time', 0)
                    current_avg = self.performance_metrics['avg_processing_time']
                    total_processed = self.performance_metrics['total_processed']
                    
                    self.performance_metrics['avg_processing_time'] = (
                        (current_avg * (total_processed - 1) + processing_time) / total_processed
                    )
                    
                except queue.Empty:
                    break
                except Exception as e:
                    self.logger.error(f"处理结果失败: {e}")
                    
        except Exception as e:
            self.logger.error(f"处理结果队列失败: {e}")
    
    def _update_performance_metrics(self) -> None:
        """更新性能指标"""
        try:
            self.performance_metrics['last_update'] = datetime.now()
            
            # 定期重置计数器（每小时）
            last_update = self.performance_metrics['last_update']
            if hasattr(self, '_last_reset') and datetime.now() - self._last_reset > timedelta(hours=1):
                self.performance_metrics['total_processed'] = 0
                self.performance_metrics['successful_trades'] = 0
                self.performance_metrics['failed_trades'] = 0
                self._last_reset = datetime.now()
            elif not hasattr(self, '_last_reset'):
                self._last_reset = datetime.now()
                
        except Exception as e:
            self.logger.warning(f"更新性能指标失败: {e}")
    
    def get_contract_status(self, symbol: str = None) -> Dict[str, Any]:
        """获取交易对状态"""
        try:
            if symbol:
                return self.contract_states.get(symbol, {})
            else:
                return self.contract_states.copy()
                
        except Exception as e:
            self.logger.error(f"获取交易对状态失败: {e}")
            return {}
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        try:
            metrics = self.performance_metrics.copy()
            
            # 计算成功率
            total = metrics['total_processed']
            if total > 0:
                metrics['success_rate'] = metrics['successful_trades'] / total
                metrics['failure_rate'] = metrics['failed_trades'] / total
            else:
                metrics['success_rate'] = 0.0
                metrics['failure_rate'] = 0.0
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"获取性能指标失败: {e}")
            return {}
    
    def add_contract(self, symbol: str, config: Dict[str, Any]) -> bool:
        """添加新的交易对"""
        try:
            if symbol in self.contract_states:
                self.logger.warning(f"交易对{symbol}已存在")
                return False
            
            # 添加到配置
            self.config_manager.add_trading_pair(symbol, **config)
            
            # 初始化状态
            self.contract_states[symbol] = {
                'status': 'IDLE',
                'last_update': datetime.now(),
                'last_trade': None,
                'error_count': 0,
                'success_count': 0,
                'config': config,
                'current_position': None,
                'last_decision': None
            }
            
            # 创建锁
            self.contract_locks[symbol] = threading.Lock()
            
            self.logger.info(f"添加交易对成功: {symbol}")
            return True
            
        except Exception as e:
            self.logger.error(f"添加交易对失败: {symbol} - {e}")
            return False
    
    def remove_contract(self, symbol: str) -> bool:
        """移除交易对"""
        try:
            if symbol not in self.contract_states:
                self.logger.warning(f"交易对{symbol}不存在")
                return False
            
            # 等待当前处理完成
            with self.contract_locks[symbol]:
                del self.contract_states[symbol]
                del self.contract_locks[symbol]
            
            self.logger.info(f"移除交易对成功: {symbol}")
            return True
            
        except Exception as e:
            self.logger.error(f"移除交易对失败: {symbol} - {e}")
            return False

    async def process_contracts_async(self, symbols: List[str] = None) -> Dict[str, Any]:
        """异步处理多个合约"""
        try:
            if symbols is None:
                symbols = list(self.contract_states.keys())

            if not symbols:
                self.logger.warning("没有可处理的交易对")
                return {}

            # 控制并发数量
            semaphore = asyncio.Semaphore(4)  # 最多4个并发

            async def process_single_contract_async(symbol):
                async with semaphore:
                    try:
                        # 异步获取数据和处理
                        return await self._process_contract_async(symbol)
                    except Exception as e:
                        self.logger.error(f"异步处理合约{symbol}失败: {e}")
                        return {'symbol': symbol, 'success': False, 'error': str(e)}

            # 并发处理所有合约
            tasks = [process_single_contract_async(symbol) for symbol in symbols]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 整理结果
            processed_results = {}
            for result in results:
                if isinstance(result, dict) and 'symbol' in result:
                    processed_results[result['symbol']] = result
                elif isinstance(result, Exception):
                    self.logger.error(f"异步处理异常: {result}")

            self.logger.info(f"异步处理完成，成功处理{len(processed_results)}个合约")
            return processed_results

        except Exception as e:
            self.logger.error(f"异步处理合约失败: {e}")
            return {}

    async def _process_contract_async(self, symbol: str) -> Dict[str, Any]:
        """异步处理单个合约"""
        try:
            # 使用线程池执行同步操作
            loop = asyncio.get_event_loop()

            # 异步获取市场数据
            market_data = await loop.run_in_executor(
                None, self._get_market_data, symbol
            )

            if not market_data:
                return {
                    'symbol': symbol,
                    'success': False,
                    'error': '获取市场数据失败'
                }

            # 异步计算指标
            indicators = await loop.run_in_executor(
                None, self._calculate_indicators, symbol, market_data
            )

            if not indicators:
                return {
                    'symbol': symbol,
                    'success': False,
                    'error': '计算技术指标失败'
                }

            # 异步获取AI决策
            decision = await loop.run_in_executor(
                None, self._get_ai_decision, symbol, market_data, indicators
            )

            if not decision:
                return {
                    'symbol': symbol,
                    'success': False,
                    'error': '获取AI决策失败'
                }

            # 异步执行交易
            trade_result = await loop.run_in_executor(
                None, self._execute_trade, symbol, decision
            )

            return {
                'symbol': symbol,
                'success': True,
                'market_data': market_data,
                'indicators': indicators,
                'decision': decision,
                'trade_result': trade_result,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"异步处理合约{symbol}失败: {e}")
            return {
                'symbol': symbol,
                'success': False,
                'error': str(e)
            }
