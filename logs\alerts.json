[{"timestamp": "2025-07-25T04:48:53.999720", "type": "ERROR", "message": "错误数量过多: 17个错误在300秒内", "stats": {"total_logs": 144, "error_count": 17, "warning_count": 3, "info_count": 124, "debug_count": 0, "last_error": {"timestamp": "2025-07-25T04:48:50.453854", "message": "备用决策也失败: 无法获取账户余额信息，请检查API连接", "module": "open_position_engine"}, "last_warning": {"timestamp": "2025-07-25T04:48:41.216861", "message": "交易对处理失败: ETH-USDT-SWAP - 获取市场数据失败", "module": "multi_contract_manager"}, "start_time": "2025-07-25T04:47:53.997563"}}, {"timestamp": "2025-07-26T17:26:49.427196", "type": "ERROR", "message": "错误数量过多: 24个错误在300秒内", "stats": {"total_logs": 58, "error_count": 24, "warning_count": 0, "info_count": 34, "debug_count": 0, "last_error": {"timestamp": "2025-07-26T17:26:46.933262", "message": "API模块初始化失败: 专门决策引擎是必需的，初始化失败: unexpected indent (position_monitor_engine.py, line 24)", "module": "main_controller"}, "last_warning": null, "start_time": "2025-07-26T17:25:49.425069"}}]