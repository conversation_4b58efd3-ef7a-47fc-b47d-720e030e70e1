#!/usr/bin/env python3
"""
DeepSeek驱动的全自动加密货币合约交易系统
主启动文件

使用方法:
    python main.py

功能特性:
- AI驱动的交易决策 (DeepSeek)
- 多合约并行交易
- 实时风险管理
- Web可视化界面
- 24x7无人值守运行
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.main_controller import MainController

def print_banner():
    """打印系统横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║    🤖 DeepSeek驱动的全自动加密货币合约交易系统                                  ║
║                                                                              ║
║    ✨ 特性:                                                                   ║
║       • AI智能决策 (DeepSeek模型)                                             ║
║       • 多合约并行交易                                                         ║
║       • 实时风险管理                                                           ║
║       • Web可视化监控                                                          ║
║       • 24x7无人值守                                                          ║
║                                                                              ║
║    🔧 技术栈:                                                                 ║
║       • Python 3.11+ | SQLite3 | TA-Lib                                    ║
║       • OKX API | DeepSeek API | Flask                                      ║
║                                                                              ║
║    ⚠️  风险提示:                                                              ║
║       • 量化交易存在风险，请谨慎使用                                            ║
║       • 建议先在模拟环境测试                                                   ║
║       • 请确保API密钥安全                                                      ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_requirements():
    """检查系统要求"""
    print("🔍 检查系统要求...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ Python版本过低，需要Python 3.8+")
        return False
    
    # 检查TA-Lib库文件（可选）
    talib_file = 'ta_lib-0.6.3-cp311-cp311-win_amd64.whl'
    if not os.path.exists(talib_file):
        print(f"⚠️  TA-Lib库文件不存在: {talib_file}")
        print("   如果TA-Lib未安装，请先安装该库")
        print("   或者使用: pip install TA-Lib")
    else:
        print(f"✓ 找到TA-Lib库文件: {talib_file}")

    # API密钥将通过Web界面配置，无需检查文件
    print("✓ API密钥配置: 将通过Web界面进行配置")
    
    # 检查必需的目录
    required_dirs = ['src', 'logs', 'templates', 'config']
    for dir_name in required_dirs:
        if not os.path.exists(dir_name):
            try:
                os.makedirs(dir_name)
                print(f"✓ 创建目录: {dir_name}")
            except Exception as e:
                print(f"❌ 创建目录失败: {dir_name} - {e}")
                return False
    
    print("✅ 系统要求检查通过")
    return True

def main():
    """主函数"""
    try:
        # 打印横幅
        print_banner()
        
        # 检查系统要求
        if not check_requirements():
            print("\n❌ 系统要求检查失败，请解决上述问题后重试")
            sys.exit(1)
        
        print("\n🚀 正在启动系统...")
        print("=" * 60)
        
        # 创建并运行主控制器
        controller = MainController()
        controller.run_interactive()
        
    except KeyboardInterrupt:
        print("\n\n🛑 用户中断，系统退出")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 系统运行失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
