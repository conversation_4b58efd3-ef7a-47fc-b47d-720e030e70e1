#!/usr/bin/env python3
"""
验证修复
"""

import sys
sys.path.insert(0, 'src')

print("=== 快速验证修复 ===")

try:
    from src.core.unified_trading_service import UnifiedTradingService
    from src.config_manager import ConfigManager

    config = ConfigManager()
    service = UnifiedTradingService(config, test_mode=False)

    # 测试账户余额
    balance = service.get_account_balance()
    print(f"账户余额获取: {'✅ 成功' if balance else '❌ 失败'}")
    if balance:
        print(f"总权益: {balance.get('total_equity', 0):.2f} USDT")
        print(f"可用余额: {balance.get('available_balance', 0):.2f} USDT")

    # 测试持仓信息
    positions = service.get_positions()
    print(f"持仓信息获取: {'✅ 成功' if positions is not None else '❌ 失败'}")
    print(f"当前持仓数: {len(positions) if positions else 0}")

    print("\n🎉 修复验证完成！系统正常工作！")

except Exception as e:
    print(f"❌ 验证失败: {e}")
    import traceback
    traceback.print_exc()
