"""
Web仪表盘模块 - Web Dashboard
提供Web界面用于监控和控制交易系统
基于Flask框架实现
"""

from flask import Flask, render_template, request, jsonify, redirect, url_for
import json
import os
from typing import Dict, List, Optional, Any
import logging
from datetime import datetime, timedelta
import threading
from .config_manager import ConfigManager

class WebDashboard:
    """Web仪表盘 - 提供Web界面监控和控制"""
    
    def __init__(self,
                 config_manager: ConfigManager,
                 data_fetcher=None,
                 decision_engine=None,  # 保留参数兼容性，但不使用
                 trade_executor=None,
                 risk_manager=None,
                 multi_contract_manager=None,
                 logger_monitor=None):
        """
        初始化Web仪表盘

        Args:
            config_manager: 配置管理器
            data_fetcher: 数据获取器
            decision_engine: 已移除，保留参数兼容性
            trade_executor: 交易执行器
            risk_manager: 风险管理器
            multi_contract_manager: 多合约管理器
            logger_monitor: 日志监控器
        """
        self.config_manager = config_manager
        self.data_fetcher = data_fetcher
        # self.decision_engine = None  # 已移除
        self.trade_executor = trade_executor
        self.risk_manager = risk_manager
        self.multi_contract_manager = multi_contract_manager
        self.logger_monitor = logger_monitor
        
        self.logger = logging.getLogger(__name__)
        
        # 创建Flask应用
        self.app = Flask(__name__, 
                        template_folder='../templates',
                        static_folder='../static')
        self.app.secret_key = 'deepseek_trading_system_2024'
        
        # 配置Flask
        self._configure_flask()
        
        # 注册路由
        self._register_routes()
        
        # 运行状态
        self.is_running = False
        self.server_thread = None
        
        self.logger.info("Web仪表盘初始化完成")
    
    def _configure_flask(self) -> None:
        """配置Flask应用"""
        try:
            # 禁用Flask的默认日志
            log = logging.getLogger('werkzeug')
            log.setLevel(logging.ERROR)
            
            # 移除Flask自定义日志设置，使用统一的日志格式
            
            # 配置JSON序列化
            self.app.json.ensure_ascii = False
            
        except Exception as e:
            self.logger.warning(f"配置Flask失败: {e}")
    
    def _register_routes(self) -> None:
        """注册路由"""
        
        @self.app.route('/')
        def index():
            """主页"""
            try:
                return render_template('index.html')
            except Exception as e:
                self.logger.error(f"渲染主页失败: {e}")
                return f"页面加载失败: {str(e)}", 500

        @self.app.route('/test')
        def test_page():
            """测试页面"""
            try:
                return render_template('test.html')
            except Exception as e:
                self.logger.error(f"渲染测试页面失败: {e}")
                return f"页面加载失败: {str(e)}", 500

        @self.app.route('/config')
        def config_page():
            """配置页面"""
            try:
                return render_template('config.html')
            except Exception as e:
                self.logger.error(f"渲染配置页面失败: {e}")
                return f"页面加载失败: {str(e)}", 500
        
        @self.app.route('/api/system/status')
        def get_system_status():
            """获取系统状态"""
            try:
                status = {
                    'timestamp': datetime.now().isoformat(),
                    'system_running': self.multi_contract_manager.is_running if self.multi_contract_manager else False,
                    'active_contracts': len(self.config_manager.get_active_trading_pairs()),
                    'risk_level': 'UNKNOWN',
                    'total_trades': 0,
                    'success_rate': 0.0
                }
                
                # 获取风险状态
                if self.risk_manager:
                    try:
                        risk_summary = self.risk_manager.get_risk_summary()
                        self.logger.debug(f"风险摘要类型: {type(risk_summary)}, 内容: {risk_summary}")

                        if isinstance(risk_summary, dict):
                            risk_status = risk_summary.get('risk_status', {})
                            if isinstance(risk_status, dict):
                                status['risk_level'] = risk_status.get('overall_risk_level', 'UNKNOWN')
                            else:
                                self.logger.warning(f"risk_status不是字典类型: {type(risk_status)}")
                                status['risk_level'] = 'UNKNOWN'
                        else:
                            self.logger.warning(f"risk_summary不是字典类型: {type(risk_summary)}")
                            status['risk_level'] = 'UNKNOWN'
                    except Exception as e:
                        self.logger.error(f"获取风险状态失败: {e}")
                        status['risk_level'] = 'UNKNOWN'

                # 获取交易统计
                if self.trade_executor:
                    try:
                        trade_stats = self.trade_executor.get_trade_statistics()
                        self.logger.debug(f"交易统计类型: {type(trade_stats)}, 内容: {trade_stats}")

                        if isinstance(trade_stats, dict):
                            status['total_trades'] = trade_stats.get('total_trades', 0)
                            status['success_rate'] = trade_stats.get('success_rate', 0.0)
                        else:
                            self.logger.warning(f"trade_stats不是字典类型: {type(trade_stats)}")
                            status['total_trades'] = 0
                            status['success_rate'] = 0.0
                    except Exception as e:
                        self.logger.error(f"获取交易统计失败: {e}")
                        status['total_trades'] = 0
                        status['success_rate'] = 0.0
                
                return jsonify(status)
                
            except Exception as e:
                import traceback
                self.logger.error(f"获取系统状态失败: {e}")
                self.logger.error(f"错误堆栈: {traceback.format_exc()}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/contracts')
        def get_contracts():
            """获取交易对列表"""
            try:
                contracts = []
                active_pairs = self.config_manager.get_active_trading_pairs()
                
                for symbol, config in active_pairs.items():
                    contract_info = {
                        'symbol': symbol,
                        'is_active': config.get('is_active', False),
                        'position_side': config.get('position_side', 'long'),
                        'leverage': config.get('leverage', 1),
                        'status': 'UNKNOWN',
                        'last_price': 0,
                        'change_24h': 0,
                        'current_position': None
                    }
                    
                    # 获取合约状态
                    if self.multi_contract_manager:
                        contract_status = self.multi_contract_manager.get_contract_status(symbol)
                        contract_info['status'] = contract_status.get('status', 'UNKNOWN')
                        contract_info['current_position'] = contract_status.get('current_position')
                    
                    # 获取市场数据
                    if self.data_fetcher:
                        try:
                            ticker = self.data_fetcher.get_ticker(symbol)
                            contract_info['last_price'] = ticker.get('last_price', 0)
                            contract_info['change_24h'] = ticker.get('change_24h', 0)
                        except:
                            pass
                    
                    contracts.append(contract_info)
                
                return jsonify(contracts)
                
            except Exception as e:
                self.logger.error(f"获取交易对列表失败: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/contracts/<symbol>/chart')
        def get_contract_chart(symbol):
            """获取交易对图表数据"""
            try:
                if not self.data_fetcher:
                    return jsonify({'error': '数据获取器未初始化'}), 500
                
                # 获取K线数据
                timeframe = request.args.get('timeframe', '1m')
                limit = int(request.args.get('limit', 100))
                
                kline_data = self.data_fetcher.get_candlesticks(symbol, timeframe, limit)
                
                if kline_data.empty:
                    return jsonify({'error': '无法获取K线数据'}), 404
                
                # 转换为图表格式
                chart_data = []
                for _, row in kline_data.iterrows():
                    chart_data.append({
                        'timestamp': row['timestamp'].isoformat(),
                        'open': float(row['open']),
                        'high': float(row['high']),
                        'low': float(row['low']),
                        'close': float(row['close']),
                        'volume': float(row['volume'])
                    })
                
                return jsonify(chart_data)
                
            except Exception as e:
                self.logger.error(f"获取图表数据失败: {symbol} - {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/decisions/recent')
        def get_recent_decisions():
            """获取最近的AI决策（从专门引擎获取）"""
            try:
                # 从多合约管理器获取决策信息
                if not self.multi_contract_manager:
                    return jsonify({'error': '多合约管理器未初始化'}), 500

                # 获取专门引擎的决策统计
                stats = {
                    'total_decisions': 0,
                    'buy_decisions': 0,
                    'sell_decisions': 0,
                    'hold_decisions': 0,
                    'close_decisions': 0
                }

                recent_decisions = []

                return jsonify({
                    'statistics': stats,
                    'recent_decisions': recent_decisions,
                    'note': '决策数据现在由专门的开仓和持仓监控引擎提供'
                })
                
            except Exception as e:
                self.logger.error(f"获取最近决策失败: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/risk/summary')
        def get_risk_summary():
            """获取风险摘要"""
            try:
                if not self.risk_manager:
                    return jsonify({'error': '风险管理器未初始化'}), 500
                
                risk_summary = self.risk_manager.get_risk_summary()
                return jsonify(risk_summary)
                
            except Exception as e:
                self.logger.error(f"获取风险摘要失败: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/logs/recent')
        def get_recent_logs():
            """获取最近的日志"""
            try:
                if not self.logger_monitor:
                    return jsonify({'error': '日志监控器未初始化'}), 500
                
                level = request.args.get('level')
                count = int(request.args.get('count', 50))
                
                recent_logs = self.logger_monitor.get_recent_logs(count, level)
                
                # 转换时间格式
                for log in recent_logs:
                    if isinstance(log['timestamp'], datetime):
                        log['timestamp'] = log['timestamp'].isoformat()
                
                return jsonify(recent_logs)
                
            except Exception as e:
                self.logger.error(f"获取最近日志失败: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/performance')
        def get_performance_metrics():
            """获取性能指标"""
            try:
                metrics = {}
                
                # 多合约管理器性能
                if self.multi_contract_manager:
                    metrics['contract_manager'] = self.multi_contract_manager.get_performance_metrics()
                
                # 日志统计
                if self.logger_monitor:
                    metrics['logging'] = self.logger_monitor.get_log_statistics()
                
                # 交易统计
                if self.trade_executor:
                    metrics['trading'] = self.trade_executor.get_trade_statistics()
                
                return jsonify(metrics)
                
            except Exception as e:
                self.logger.error(f"获取性能指标失败: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/system/start', methods=['POST'])
        def start_system():
            """启动交易系统"""
            try:
                if self.multi_contract_manager:
                    self.multi_contract_manager.start()
                    return jsonify({'success': True, 'message': '交易系统启动成功'})
                else:
                    return jsonify({'success': False, 'message': '多合约管理器未初始化'}), 500
                    
            except Exception as e:
                self.logger.error(f"启动交易系统失败: {e}")
                return jsonify({'success': False, 'message': str(e)}), 500
        
        @self.app.route('/api/system/stop', methods=['POST'])
        def stop_system():
            """停止交易系统"""
            try:
                if self.multi_contract_manager:
                    self.multi_contract_manager.stop()
                    return jsonify({'success': True, 'message': '交易系统停止成功'})
                else:
                    return jsonify({'success': False, 'message': '多合约管理器未初始化'}), 500
                    
            except Exception as e:
                self.logger.error(f"停止交易系统失败: {e}")
                return jsonify({'success': False, 'message': str(e)}), 500
        
        @self.app.route('/api/contracts/<symbol>/toggle', methods=['POST'])
        def toggle_contract(symbol):
            """切换交易对状态"""
            try:
                data = request.get_json()
                is_active = data.get('is_active', False)
                
                # 更新配置
                self.config_manager.set_setting('trading_pairs', f'{symbol}.is_active', is_active)
                
                return jsonify({'success': True, 'message': f'交易对{symbol}状态已更新'})
                
            except Exception as e:
                self.logger.error(f"切换交易对状态失败: {symbol} - {e}")
                return jsonify({'success': False, 'message': str(e)}), 500
        
        @self.app.route('/api/config')
        def get_config():
            """获取系统配置"""
            try:
                # 返回部分配置信息（隐藏敏感信息）
                config = {
                    'trading_pairs': self.config_manager.get('trading_pairs', {}),
                    'risk_management': self.config_manager.get('risk_management', {}),
                    'indicators': self.config_manager.get('indicators', {}),
                    'logging': self.config_manager.get('logging', {}),
                    # 返回API配置状态（不返回实际密钥）
                    'credentials': {
                        'okx': bool(self.config_manager.get('credentials', {}).get('okx')),
                        'deepseek': bool(self.config_manager.get('credentials', {}).get('deepseek'))
                    }
                }
                
                return jsonify(config)
                
            except Exception as e:
                self.logger.error(f"获取系统配置失败: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/config', methods=['POST'])
        def update_config():
            """更新系统配置"""
            try:
                data = request.get_json()

                for category, settings in data.items():
                    if category in ['credentials', 'trading_pairs', 'risk_management', 'indicators', 'logging']:
                        for key, value in settings.items():
                            self.config_manager.set_setting(category, key, value)

                return jsonify({'success': True, 'message': '配置更新成功'})

            except Exception as e:
                self.logger.error(f"更新系统配置失败: {e}")
                return jsonify({'success': False, 'message': str(e)}), 500

        @self.app.route('/api/test-connection', methods=['POST'])
        def test_api_connection():
            """测试API连接"""
            try:
                results = {}

                # 获取保存的API配置
                credentials = self.config_manager.get('credentials', {})

                # 测试OKX API
                okx_config = credentials.get('okx')
                if okx_config:
                    results['okx'] = self._test_okx_api(okx_config)
                else:
                    results['okx'] = {'success': False, 'error': 'OKX API配置未找到'}

                # 测试DeepSeek API
                deepseek_config = credentials.get('deepseek')
                if deepseek_config:
                    results['deepseek'] = self._test_deepseek_api(deepseek_config)
                else:
                    results['deepseek'] = {'success': False, 'error': 'DeepSeek API配置未找到'}

                return jsonify(results)

            except Exception as e:
                self.logger.error(f"API连接测试失败: {e}")
                return jsonify({'error': str(e)}), 500
        
        # ==================== 持仓相关API ====================

        @self.app.route('/api/positions')
        def get_positions():
            """获取持仓信息"""
            try:
                if not self.data_fetcher:
                    return jsonify({'error': '数据获取器未初始化'}), 500

                positions = self.data_fetcher.get_positions()

                # 如果API未配置，返回空列表而不是错误
                if not positions:
                    return jsonify({
                        'positions': [],
                        'total_count': 0
                    })

                # 转换为前端友好的格式
                position_list = []
                for symbol, pos_data in positions.items():
                    if pos_data['size'] != 0:  # 只显示有持仓的
                        position_list.append({
                            'symbol': symbol,
                            'side': pos_data['position_side'],
                            'size': f"{pos_data['size']:.4f}",
                            'avg_price': f"{pos_data['avg_price']:.4f}",
                            'mark_price': f"{pos_data['mark_price']:.4f}",
                            'unrealized_pnl': f"{pos_data['unrealized_pnl']:.2f}",
                            'unrealized_pnl_ratio': f"{pos_data['unrealized_pnl_ratio']:.2f}%",
                            'margin': f"{pos_data['margin']:.2f}",
                            'leverage': f"{pos_data['leverage']:.0f}x",
                            'liquidation_price': f"{pos_data['liquidation_price']:.4f}" if pos_data['liquidation_price'] > 0 else '--'
                        })

                return jsonify({
                    'positions': position_list,
                    'total_count': len(position_list)
                })

            except Exception as e:
                # 如果是API未配置的错误，使用debug级别
                if 'NoneType' in str(e) and 'get_positions' in str(e):
                    self.logger.debug(f"API未配置，无法获取持仓信息: {e}")
                    return jsonify({
                        'positions': [],
                        'total_count': 0
                    })
                else:
                    self.logger.error(f"获取持仓信息失败: {e}")
                    return jsonify({'error': str(e)}), 500

        @self.app.route('/api/instruments')
        def get_instruments():
            """获取交易对列表"""
            try:
                if not self.data_fetcher:
                    return jsonify({'error': '数据获取器未初始化'}), 500

                inst_type = request.args.get('type', 'popular')  # 默认获取热门交易对

                if inst_type == 'popular':
                    # 获取热门交易对
                    instruments = self.data_fetcher.get_popular_instruments()
                else:
                    # 获取指定类型的所有交易对
                    instruments = self.data_fetcher.get_instruments(inst_type)

                # 转换为前端友好的格式
                instrument_list = []
                for inst in instruments:
                    instrument_list.append({
                        'symbol': inst['inst_id'],
                        'base_currency': inst['base_ccy'],
                        'quote_currency': inst['quote_ccy'],
                        'settle_currency': inst.get('settle_ccy', ''),
                        'type': inst['inst_type'],
                        'state': inst.get('state', ''),
                        'min_size': inst.get('min_sz', ''),
                        'tick_size': inst.get('tick_sz', ''),
                        'leverage': inst.get('lever', '')
                    })

                return jsonify({
                    'instruments': instrument_list,
                    'total_count': len(instrument_list),
                    'type': inst_type
                })

            except Exception as e:
                self.logger.error(f"获取交易对列表失败: {e}")
                return jsonify({'error': str(e)}), 500

        # ==================== 交易测试API ====================

        @self.app.route('/api/trade/test-order', methods=['POST'])
        def test_trade_order():
            """测试交易下单"""
            try:
                if not self.trade_executor:
                    return jsonify({'error': '交易执行器未初始化'}), 500

                data = request.get_json()
                if not data:
                    return jsonify({'error': '请求数据为空'}), 400

                # 验证必需参数
                required_fields = ['symbol', 'side', 'size']
                for field in required_fields:
                    if field not in data:
                        return jsonify({'error': f'缺少必需参数: {field}'}), 400

                symbol = data['symbol']
                side = data['side'].lower()  # 'buy' or 'sell'
                size = float(data['size'])

                # 验证参数
                if side not in ['buy', 'sell']:
                    return jsonify({'error': 'side参数必须是buy或sell'}), 400

                if size <= 0:
                    return jsonify({'error': '交易数量必须大于0'}), 400

                # 安全检查：限制测试交易的最大数量
                max_test_size = {
                    'BTC-USDT-SWAP': 0.01,  # 最多0.01 BTC
                    'ETH-USDT-SWAP': 0.1,   # 最多0.1 ETH
                    'SOL-USDT-SWAP': 1.0,   # 最多1 SOL
                }

                if symbol in max_test_size and size > max_test_size[symbol]:
                    return jsonify({
                        'error': f'测试交易数量不能超过 {max_test_size[symbol]} {symbol}'
                    }), 400

                # 执行测试交易
                self.logger.info(f"执行测试交易: {symbol} {side} {size}")

                # 调用交易执行器的下单方法
                position_side = 'long' if side == 'buy' else 'short'
                result = self.trade_executor._place_market_order(
                    symbol=symbol,
                    side=side,
                    size=size,
                    position_side=position_side
                )

                if result.get('success'):
                    return jsonify({
                        'success': True,
                        'message': '测试交易执行成功',
                        'order_id': result.get('order_id'),
                        'symbol': symbol,
                        'side': side,
                        'size': size,
                        'result': result
                    })
                else:
                    return jsonify({
                        'success': False,
                        'error': result.get('error', '交易执行失败')
                    }), 500

            except Exception as e:
                self.logger.error(f"测试交易失败: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/trade/orders')
        def get_orders():
            """获取订单列表"""
            try:
                if not self.trade_executor or not hasattr(self.trade_executor, 'trade_api'):
                    return jsonify({'error': '交易API未初始化'}), 500

                # 获取最近的订单
                response = self.trade_executor.trade_api.get_order_list(
                    instType='SWAP',
                    limit='20'
                )

                if response['code'] != '0':
                    return jsonify({'error': f"获取订单失败: {response['msg']}"}), 500

                orders = []
                for order in response['data']:
                    orders.append({
                        'order_id': order['ordId'],
                        'symbol': order['instId'],
                        'side': order['side'],
                        'size': order['sz'],
                        'price': order.get('px', '市价'),
                        'state': order['state'],
                        'filled_size': order.get('fillSz', '0'),
                        'avg_price': order.get('avgPx', '0'),
                        'create_time': order['cTime'],
                        'update_time': order['uTime']
                    })

                return jsonify({
                    'orders': orders,
                    'total_count': len(orders)
                })

            except Exception as e:
                self.logger.error(f"获取订单列表失败: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/trade/cancel-order', methods=['POST'])
        def cancel_order():
            """取消订单"""
            try:
                if not self.trade_executor or not hasattr(self.trade_executor, 'trade_api'):
                    return jsonify({'error': '交易API未初始化'}), 500

                data = request.get_json()
                if not data or 'order_id' not in data or 'symbol' not in data:
                    return jsonify({'error': '缺少必需参数: order_id, symbol'}), 400

                order_id = data['order_id']
                symbol = data['symbol']

                # 取消订单
                response = self.trade_executor.trade_api.cancel_order(
                    instId=symbol,
                    ordId=order_id
                )

                if response['code'] == '0':
                    return jsonify({
                        'success': True,
                        'message': '订单取消成功',
                        'order_id': order_id
                    })
                else:
                    return jsonify({
                        'success': False,
                        'error': response.get('msg', '取消订单失败')
                    }), 500

            except Exception as e:
                self.logger.error(f"取消订单失败: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/trade/close-all-positions', methods=['POST'])
        def close_all_positions():
            """一键平全部仓位"""
            try:
                if not self.trade_executor:
                    return jsonify({'error': '交易执行器未初始化'}), 500

                self.logger.info("🔄 收到一键平仓请求")

                # 执行一键平仓
                result = self.trade_executor.close_all_positions()

                if result['success']:
                    self.logger.info(f"✅ 一键平仓成功: {result['message']}")
                    return jsonify({
                        'success': True,
                        'message': result['message'],
                        'data': {
                            'total_positions': result.get('total_positions', 0),
                            'successful_closes': result.get('successful_closes', 0),
                            'failed_closes': result.get('failed_closes', 0),
                            'closed_positions': result.get('closed_positions', []),
                            'failed_positions': result.get('failed_positions', [])
                        }
                    })
                else:
                    self.logger.error(f"❌ 一键平仓失败: {result.get('message', result.get('error'))}")
                    return jsonify({
                        'success': False,
                        'message': result.get('message', result.get('error')),
                        'data': result
                    })

            except Exception as e:
                self.logger.error(f"一键平仓API异常: {e}")
                return jsonify({
                    'success': False,
                    'message': f'一键平仓失败: {str(e)}'
                }), 500

        # ==================== 账户管理API ====================

        @self.app.route('/api/account/balance')
        def get_account_balance():
            """获取账户余额"""
            try:
                # 检查trade_executor是否存在且有对应方法
                if not self.trade_executor or not hasattr(self.trade_executor, 'get_account_balance'):
                    return jsonify({
                        'total_balance': '--',
                        'available_balance': '--',
                        'daily_pnl': '--',
                        'total_pnl': '--'
                    })

                # 获取账户信息
                balance_info = self.trade_executor.get_account_balance()

                return jsonify({
                    'total_balance': balance_info.get('total_balance', '--'),
                    'available_balance': balance_info.get('available_balance', '--'),
                    'daily_pnl': balance_info.get('daily_pnl', '--'),
                    'total_pnl': balance_info.get('total_pnl', '--')
                })

            except Exception as e:
                self.logger.error(f"获取账户余额失败: {e}")
                return jsonify({
                    'total_balance': '--',
                    'available_balance': '--',
                    'daily_pnl': '--',
                    'total_pnl': '--'
                })

        # ==================== 交易管理API ====================

        @self.app.route('/api/trading/active-pairs')
        def get_active_trading_pairs():
            """获取活跃交易对"""
            try:
                pairs = []
                active_pairs = self.config_manager.get_active_trading_pairs()

                for symbol in active_pairs:
                    try:
                        # 获取价格信息
                        price_info = {}
                        if self.data_fetcher and hasattr(self.data_fetcher, 'get_ticker'):
                            price_info = self.data_fetcher.get_ticker(symbol)

                        # 获取持仓信息
                        position_info = {}
                        if self.trade_executor and hasattr(self.trade_executor, 'get_position'):
                            position_info = self.trade_executor.get_position(symbol)

                        pair_data = {
                            'symbol': symbol,
                            'price': price_info.get('last', '--'),
                            'change': price_info.get('change_24h', 0),
                            'position': position_info.get('size', '--'),
                            'pnl': position_info.get('unrealized_pnl', '--'),
                            'status': 'Active' if position_info.get('size', 0) > 0 else 'Monitoring'
                        }
                        pairs.append(pair_data)

                    except Exception as e:
                        self.logger.warning(f"获取交易对{symbol}信息失败: {e}")
                        continue

                return jsonify({'pairs': pairs})

            except Exception as e:
                self.logger.error(f"获取活跃交易对失败: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/trading/statistics')
        def get_trading_statistics():
            """获取交易统计"""
            try:
                stats = {
                    'total_trades': 0,
                    'successful_trades': 0,
                    'success_rate': '--',
                    'avg_profit': '--',
                    'risk_level': '--',
                    'max_drawdown': '--',
                    'current_leverage': '--',
                    'margin_ratio': '--'
                }

                # 获取交易统计
                if self.trade_executor and hasattr(self.trade_executor, 'get_trade_statistics'):
                    trade_stats = self.trade_executor.get_trade_statistics()
                    stats.update({
                        'total_trades': trade_stats.get('total_trades', 0),
                        'successful_trades': trade_stats.get('successful_trades', 0),
                        'success_rate': f"{trade_stats.get('success_rate', 0):.1f}",
                        'avg_profit': trade_stats.get('avg_profit', '--')
                    })

                # 获取风险信息
                if self.risk_manager and hasattr(self.risk_manager, 'get_risk_summary'):
                    risk_info = self.risk_manager.get_risk_summary()
                    if isinstance(risk_info, dict):
                        risk_status = risk_info.get('risk_status', {})
                        if isinstance(risk_status, dict):
                            risk_level = risk_status.get('overall_risk_level', '--')
                        else:
                            risk_level = '--'

                        stats.update({
                            'risk_level': risk_level,
                            'max_drawdown': f"{risk_info.get('max_drawdown', 0):.2f}",
                            'current_leverage': risk_info.get('current_leverage', '--'),
                            'margin_ratio': f"{risk_info.get('margin_ratio', 0):.2f}"
                        })
                    else:
                        stats.update({
                            'risk_level': '--',
                            'max_drawdown': '0.00',
                            'current_leverage': '--',
                            'margin_ratio': '0.00'
                        })

                return jsonify(stats)

            except Exception as e:
                self.logger.error(f"获取交易统计失败: {e}")
                return jsonify(stats)  # 返回默认数据而不是错误

        @self.app.route('/api/trading/signals')
        def get_trading_signals():
            """获取交易信号"""
            try:
                signals = []

                # 从多合约管理器获取交易信号
                if self.multi_contract_manager:
                    # 获取专门引擎的信号（这里需要实现相应的方法）
                    recent_signals = []  # 暂时返回空列表

                    for signal in recent_signals:
                        signal_data = {
                            'time': signal.get('timestamp', '--'),
                            'symbol': signal.get('symbol', '--'),
                            'type': signal.get('signal_type', '--'),
                            'price': signal.get('price', '--'),
                            'quantity': signal.get('quantity', '--'),
                            'status': signal.get('status', 'Pending')
                        }
                        signals.append(signal_data)

                return jsonify({'signals': signals})

            except Exception as e:
                self.logger.error(f"获取交易信号失败: {e}")
                return jsonify({'signals': []})

        @self.app.route('/api/trading/history')
        def get_trading_history():
            """获取交易历史"""
            try:
                trades = []

                # 检查trade_executor是否存在且有对应方法
                if self.trade_executor and hasattr(self.trade_executor, 'get_trade_history'):
                    trade_history = self.trade_executor.get_trade_history(limit=50)

                    for trade in trade_history:
                        trade_data = {
                            'time': trade.get('timestamp', '--'),
                            'symbol': trade.get('symbol', '--'),
                            'side': trade.get('side', '--'),
                            'open_price': trade.get('open_price', '--'),
                            'close_price': trade.get('close_price', '--'),
                            'quantity': trade.get('quantity', '--'),
                            'pnl': trade.get('pnl', '--'),
                            'fee': trade.get('fee', '--')
                        }
                        trades.append(trade_data)

                return jsonify({'trades': trades})

            except Exception as e:
                self.logger.error(f"获取交易历史失败: {e}")
                return jsonify({'trades': []})

        @self.app.route('/api/trading/start', methods=['POST'])
        def start_trading():
            """启动交易"""
            try:
                if self.multi_contract_manager:
                    result = self.multi_contract_manager.start()
                    if result:
                        return jsonify({'success': True, 'message': '交易启动成功'})
                    else:
                        return jsonify({'success': False, 'message': '交易启动失败'})
                else:
                    return jsonify({'success': False, 'message': '多合约管理器未初始化'}), 500

            except Exception as e:
                self.logger.error(f"启动交易失败: {e}")
                return jsonify({'success': False, 'message': str(e)}), 500

        @self.app.route('/api/trading/stop', methods=['POST'])
        def stop_trading():
            """停止交易"""
            try:
                if self.multi_contract_manager:
                    result = self.multi_contract_manager.stop()
                    if result:
                        return jsonify({'success': True, 'message': '交易停止成功'})
                    else:
                        return jsonify({'success': False, 'message': '交易停止失败'})
                else:
                    return jsonify({'success': False, 'message': '多合约管理器未初始化'}), 500

            except Exception as e:
                self.logger.error(f"停止交易失败: {e}")
                return jsonify({'success': False, 'message': str(e)}), 500

        @self.app.route('/api/trading/export')
        def export_trading_data():
            """导出交易数据"""
            try:
                # 这里应该生成Excel文件并返回下载链接
                return jsonify({'success': False, 'message': '导出功能开发中'})

            except Exception as e:
                self.logger.error(f"导出交易数据失败: {e}")
                return jsonify({'error': str(e)}), 500

        # ==================== 策略配置API ====================

        @self.app.route('/api/strategy/config')
        def get_strategy_config():
            """获取策略配置"""
            try:
                config = {
                    'trading_pairs': self.config_manager.get('trading_pairs', {}),
                    'risk_management': self.config_manager.get('risk_management', {}),
                    'investment_ratio': self.config_manager.get('trading_pairs', {}).get('investment_ratio', 5.0),
                    'max_leverage': self.config_manager.get('trading_pairs', {}).get('max_leverage', 3),
                    'stop_loss': self.config_manager.get('risk_management', {}).get('stop_loss_percentage', 5),
                    'take_profit': self.config_manager.get('risk_management', {}).get('take_profit_percentage', 10),
                    'max_positions': self.config_manager.get('risk_management', {}).get('max_positions', 3),
                    'daily_loss_limit': self.config_manager.get('risk_management', {}).get('daily_loss_limit', 500)
                }
                return jsonify(config)

            except Exception as e:
                self.logger.error(f"获取策略配置失败: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/strategy/config', methods=['POST'])
        def save_strategy_config():
            """保存策略配置"""
            try:
                data = request.get_json()

                # 保存交易对配置
                if 'trading_pairs' in data:
                    self.config_manager.set_setting('trading_pairs', 'active_pairs', data['trading_pairs'])

                if 'investment_ratio' in data:
                    self.config_manager.set_setting('trading_pairs', 'investment_ratio', data['investment_ratio'])

                if 'max_leverage' in data:
                    self.config_manager.set_setting('trading_pairs', 'max_leverage', data['max_leverage'])

                return jsonify({'success': True, 'message': '策略配置保存成功'})

            except Exception as e:
                self.logger.error(f"保存策略配置失败: {e}")
                return jsonify({'success': False, 'message': str(e)}), 500

        @self.app.route('/api/risk/config', methods=['POST'])
        def save_risk_config():
            """保存风险配置"""
            try:
                data = request.get_json()

                # 保存风险管理配置
                if 'stop_loss' in data:
                    self.config_manager.set_setting('risk_management', 'stop_loss_percentage', data['stop_loss'])

                if 'take_profit' in data:
                    self.config_manager.set_setting('risk_management', 'take_profit_percentage', data['take_profit'])

                if 'max_positions' in data:
                    self.config_manager.set_setting('risk_management', 'max_positions', data['max_positions'])

                if 'daily_loss_limit' in data:
                    self.config_manager.set_setting('risk_management', 'daily_loss_limit', data['daily_loss_limit'])

                return jsonify({'success': True, 'message': '风险配置保存成功'})

            except Exception as e:
                self.logger.error(f"保存风险配置失败: {e}")
                return jsonify({'success': False, 'message': str(e)}), 500

        # ==================== 日志管理API ====================

        @self.app.route('/api/logs')
        def get_system_logs():
            """获取系统日志"""
            try:
                level = request.args.get('level', 'ALL')
                limit = int(request.args.get('limit', 100))

                logs = []

                # 检查logger_monitor是否存在且有对应方法
                if self.logger_monitor and hasattr(self.logger_monitor, 'recent_logs'):
                    # 直接访问recent_logs属性，而不是调用不存在的方法
                    recent_logs = self.logger_monitor.recent_logs[-limit:] if self.logger_monitor.recent_logs else []

                    for log in recent_logs:
                        # 根据级别过滤
                        if level != 'ALL' and log.get('level') != level:
                            continue

                        log_data = {
                            'time': log.get('timestamp', '--'),
                            'level': log.get('level', 'INFO'),
                            'message': log.get('message', '')
                        }
                        logs.append(log_data)

                return jsonify({'logs': logs})

            except Exception as e:
                self.logger.error(f"获取系统日志失败: {e}")
                return jsonify({'logs': []})

        @self.app.route('/api/logs/clear', methods=['POST'])
        def clear_system_logs():
            """清空系统日志"""
            try:
                if self.logger_monitor:
                    self.logger_monitor.clear_logs()
                    return jsonify({'success': True, 'message': '日志清空成功'})
                else:
                    return jsonify({'success': False, 'message': '日志监控器未初始化'}), 500

            except Exception as e:
                self.logger.error(f"清空系统日志失败: {e}")
                return jsonify({'success': False, 'message': str(e)}), 500

        # ==================== 开发模式API ====================

        @self.app.route('/api/dev/reload-status')
        def get_reload_status():
            """获取热重载状态（开发模式）"""
            try:
                import os
                import time

                # 检查关键文件的修改时间
                files_to_check = [
                    'templates/index.html',
                    'src/web_dashboard.py',
                    'src/trade_executor.py'
                ]

                latest_modified = 0
                for file_path in files_to_check:
                    if os.path.exists(file_path):
                        mtime = os.path.getmtime(file_path)
                        latest_modified = max(latest_modified, mtime)

                return jsonify({
                    'last_modified': latest_modified,
                    'timestamp': time.time(),
                    'dev_mode': True
                })

            except Exception as e:
                self.logger.error(f"获取热重载状态失败: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.errorhandler(404)
        def not_found(error):
            """404错误处理"""
            return jsonify({'error': '页面未找到'}), 404

    def _test_okx_api(self, okx_config):
        """测试OKX API连接"""
        try:
            import requests
            import time
            import hmac
            import hashlib
            import base64

            # OKX API配置
            api_key = okx_config.get('api_key')
            api_secret = okx_config.get('api_secret')
            passphrase = okx_config.get('passphrase')
            flag = okx_config.get('flag', '1')  # 1=模拟交易, 0=实盘交易

            if not all([api_key, api_secret, passphrase]):
                return {'success': False, 'error': 'API配置不完整'}

            # 测试获取服务器时间（最简单的API调用）
            url = 'https://www.okx.com/api/v5/public/time'
            timestamp = str(int(time.time() * 1000))
            method = 'GET'
            request_path = '/api/v5/public/time'

            # 创建签名
            message = timestamp + method + request_path
            signature = base64.b64encode(
                hmac.new(api_secret.encode(), message.encode(), hashlib.sha256).digest()
            ).decode()

            headers = {
                'OK-ACCESS-KEY': api_key,
                'OK-ACCESS-SIGN': signature,
                'OK-ACCESS-TIMESTAMP': timestamp,
                'OK-ACCESS-PASSPHRASE': passphrase,
                'x-simulated-trading': flag,
                'Content-Type': 'application/json'
            }

            response = requests.get(url, headers=headers, timeout=10)

            if response.status_code == 200:
                data = response.json()
                if data.get('code') == '0':
                    server_time = data.get('data', [{}])[0].get('ts', 'Unknown')
                    return {
                        'success': True,
                        'details': f'服务器时间: {server_time}'
                    }
                else:
                    return {
                        'success': False,
                        'error': f"API错误: {data.get('msg', 'Unknown error')}"
                    }
            else:
                return {
                    'success': False,
                    'error': f"HTTP错误: {response.status_code}"
                }

        except Exception as e:
            return {'success': False, 'error': f"连接异常: {str(e)}"}

    def _test_deepseek_api(self, deepseek_config):
        """测试DeepSeek API连接"""
        try:
            import requests

            api_key = deepseek_config.get('api_key')
            if not api_key:
                return {'success': False, 'error': 'API密钥未配置'}

            # 测试DeepSeek API
            url = 'https://api.deepseek.com/v1/chat/completions'
            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            }

            data = {
                'model': 'deepseek-chat',
                'messages': [
                    {'role': 'user', 'content': 'Hello, this is a connection test.'}
                ],
                'max_tokens': 10
            }

            response = requests.post(url, headers=headers, json=data, timeout=15)

            if response.status_code == 200:
                result = response.json()
                if 'choices' in result and len(result['choices']) > 0:
                    reply = result['choices'][0]['message']['content']
                    return {
                        'success': True,
                        'details': f'API响应正常: {reply[:50]}...'
                    }
                else:
                    return {'success': False, 'error': '响应格式异常'}
            else:
                try:
                    error_data = response.json()
                    error_msg = error_data.get('error', {}).get('message', 'Unknown error')
                except:
                    error_msg = f"HTTP {response.status_code}"
                return {'success': False, 'error': f"API错误: {error_msg}"}

        except Exception as e:
            return {'success': False, 'error': f"连接异常: {str(e)}"}
        
        @self.app.errorhandler(500)
        def internal_error(error):
            """500错误处理"""
            return jsonify({'error': '内部服务器错误'}), 500
    
    def start(self, host: str = '127.0.0.1', port: int = 5000, debug: bool = True) -> None:
        """启动Web服务器（支持热重载）"""
        try:
            if self.is_running:
                self.logger.warning("Web服务器已在运行")
                return

            self.is_running = True
            self.logger.info(f"Web仪表盘启动成功: http://{host}:{port}")

            # 启用调试模式和热重载
            self.app.run(
                host=host,
                port=port,
                debug=debug,
                use_reloader=True,  # 启用代码热重载
                use_debugger=True,  # 启用调试器
                threaded=True,
                extra_files=[       # 监控额外文件变化
                    'templates/index.html',
                    'src/web_dashboard.py',
                    'src/trade_executor.py',
                    'src/config_manager.py',
                    'src/data_fetcher.py',
                    # 'src/decision_engine.py',  # 已移除
                    'src/risk_manager.py',
                    'src/logger_monitor.py'
                ]
            )

        except Exception as e:
            self.logger.error(f"启动Web仪表盘失败: {e}")
            self.is_running = False
    
    def stop(self) -> None:
        """停止Web服务器"""
        try:
            self.is_running = False
            
            # Flask没有优雅停止的方法，只能通过设置标志位
            # 在生产环境中建议使用WSGI服务器如Gunicorn
            
            self.logger.info("Web仪表盘停止请求已发送")
            
        except Exception as e:
            self.logger.error(f"停止Web仪表盘失败: {e}")
    
    def create_templates(self) -> None:
        """创建模板文件"""
        try:
            # 创建模板目录
            template_dir = 'templates'
            if not os.path.exists(template_dir):
                os.makedirs(template_dir)
            
            # 创建基础HTML模板
            index_template = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepSeek加密货币量化交易系统</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .card { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .status { display: inline-block; padding: 4px 8px; border-radius: 4px; color: white; font-size: 12px; }
        .status.running { background-color: #27ae60; }
        .status.stopped { background-color: #e74c3c; }
        .status.warning { background-color: #f39c12; }
        .btn { padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; margin: 4px; }
        .btn.primary { background-color: #3498db; color: white; }
        .btn.success { background-color: #27ae60; color: white; }
        .btn.danger { background-color: #e74c3c; color: white; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; }
        .log-container { max-height: 400px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 4px; }
        .log-entry { margin-bottom: 5px; font-family: monospace; font-size: 12px; }
        .log-error { color: #e74c3c; }
        .log-warning { color: #f39c12; }
        .log-info { color: #2c3e50; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 DeepSeek加密货币量化交易系统</h1>
            <p>AI驱动的全自动合约交易平台</p>
        </div>
        
        <div class="grid">
            <div class="card">
                <h3>系统状态</h3>
                <div id="system-status">加载中...</div>
                <div style="margin-top: 10px;">
                    <button class="btn success" onclick="startSystem()">启动系统</button>
                    <button class="btn danger" onclick="stopSystem()">停止系统</button>
                </div>
            </div>
            
            <div class="card">
                <h3>风险监控</h3>
                <div id="risk-summary">加载中...</div>
            </div>
        </div>
        
        <div class="card">
            <h3>交易对状态</h3>
            <div id="contracts-table">加载中...</div>
        </div>
        
        <div class="card">
            <h3>最近日志</h3>
            <div class="log-container" id="recent-logs">加载中...</div>
        </div>
    </div>
    
    <script>
        // 定期刷新数据
        setInterval(loadData, 5000);
        loadData();
        
        function loadData() {
            loadSystemStatus();
            loadContracts();
            loadRiskSummary();
            loadRecentLogs();
        }
        
        function loadSystemStatus() {
            fetch('/api/system/status')
                .then(response => response.json())
                .then(data => {
                    const statusHtml = `
                        <p>运行状态: <span class="status ${data.system_running ? 'running' : 'stopped'}">${data.system_running ? '运行中' : '已停止'}</span></p>
                        <p>活跃交易对: ${data.active_contracts}</p>
                        <p>风险等级: <span class="status ${data.risk_level.toLowerCase()}">${data.risk_level}</span></p>
                        <p>总交易次数: ${data.total_trades}</p>
                        <p>成功率: ${(data.success_rate * 100).toFixed(1)}%</p>
                    `;
                    document.getElementById('system-status').innerHTML = statusHtml;
                })
                .catch(error => console.error('加载系统状态失败:', error));
        }
        
        function loadContracts() {
            fetch('/api/contracts')
                .then(response => response.json())
                .then(data => {
                    let tableHtml = '<table><tr><th>交易对</th><th>状态</th><th>当前价格</th><th>24h涨跌</th><th>持仓</th></tr>';
                    data.forEach(contract => {
                        const positionText = contract.current_position ? 
                            `${contract.current_position.position_side} ${contract.current_position.size}` : '无持仓';
                        tableHtml += `
                            <tr>
                                <td>${contract.symbol}</td>
                                <td><span class="status ${contract.status.toLowerCase()}">${contract.status}</span></td>
                                <td>${contract.last_price.toFixed(4)}</td>
                                <td style="color: ${contract.change_24h >= 0 ? 'green' : 'red'}">${contract.change_24h.toFixed(2)}%</td>
                                <td>${positionText}</td>
                            </tr>
                        `;
                    });
                    tableHtml += '</table>';
                    document.getElementById('contracts-table').innerHTML = tableHtml;
                })
                .catch(error => console.error('加载交易对失败:', error));
        }
        
        function loadRiskSummary() {
            fetch('/api/risk/summary')
                .then(response => response.json())
                .then(data => {
                    const riskStatus = data.risk_status || {};
                    const summaryHtml = `
                        <p>整体风险: <span class="status ${riskStatus.overall_risk_level?.toLowerCase()}">${riskStatus.overall_risk_level || 'UNKNOWN'}</span></p>
                        <p>当前回撤: ${((riskStatus.current_drawdown || 0) * 100).toFixed(2)}%</p>
                        <p>最大回撤: ${((riskStatus.max_drawdown || 0) * 100).toFixed(2)}%</p>
                        <p>总仓位: ${((riskStatus.total_exposure || 0) * 100).toFixed(2)}%</p>
                    `;
                    document.getElementById('risk-summary').innerHTML = summaryHtml;
                })
                .catch(error => console.error('加载风险摘要失败:', error));
        }
        
        function loadRecentLogs() {
            fetch('/api/logs/recent?count=20')
                .then(response => response.json())
                .then(data => {
                    let logsHtml = '';
                    data.forEach(log => {
                        const timestamp = new Date(log.timestamp).toLocaleString();
                        logsHtml += `<div class="log-entry log-${log.level.toLowerCase()}">[${timestamp}] ${log.level} - ${log.message}</div>`;
                    });
                    document.getElementById('recent-logs').innerHTML = logsHtml;
                })
                .catch(error => console.error('加载日志失败:', error));
        }
        
        function startSystem() {
            fetch('/api/system/start', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    alert(data.message);
                    loadData();
                })
                .catch(error => console.error('启动系统失败:', error));
        }
        
        function stopSystem() {
            fetch('/api/system/stop', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    alert(data.message);
                    loadData();
                })
                .catch(error => console.error('停止系统失败:', error));
        }
    </script>
</body>
</html>
            '''
            
            with open(os.path.join(template_dir, 'index.html'), 'w', encoding='utf-8') as f:
                f.write(index_template)
            
            self.logger.info("模板文件创建完成")
            
        except Exception as e:
            self.logger.error(f"创建模板文件失败: {e}")
    
    def get_app(self) -> Flask:
        """获取Flask应用实例"""
        return self.app

    # ==================== 辅助方法 ====================

    def _get_total_trades(self) -> int:
        """获取总交易次数"""
        try:
            if self.trade_executor:
                stats = self.trade_executor.get_trade_statistics()
                return stats.get('total_trades', 0)
            return 0
        except Exception as e:
            self.logger.error(f"获取总交易次数失败: {e}")
            return 0

    def _get_success_rate(self) -> float:
        """获取成功率"""
        try:
            if self.trade_executor:
                stats = self.trade_executor.get_trade_statistics()
                return stats.get('success_rate', 0.0)
            return 0.0
        except Exception as e:
            self.logger.error(f"获取成功率失败: {e}")
            return 0.0

    def set_main_controller(self, main_controller):
        """设置主控制器引用"""
        self.main_controller = main_controller
        self.start_time = datetime.now()

    def set_components(self, **components):
        """设置系统组件引用"""
        for name, component in components.items():
            setattr(self, name, component)

    def run(self, debug=False, host='127.0.0.1', port=5000):
        """启动Web服务器"""
        try:
            self.logger.info(f"启动Web服务器: http://{host}:{port}")
            self.app.run(debug=debug, host=host, port=port, threaded=True)
        except Exception as e:
            self.logger.error(f"Web服务器启动失败: {e}")
            raise
