import sys
import os
import logging
import time

# 将项目根目录（大模型加密货币量化系统）添加到Python路径，以便能够导入src模块
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from src.logger_monitor import LoggerMonitor
from src.config_manager import ConfigManager

class MockConfigManager:
    """模拟ConfigManager，用于测试LoggerMonitor"""
    def get(self, key, default=None):
        if key == 'logging':
            # 返回一些自定义的日志配置，模拟从配置文件加载
            return {
                'log_level': 'DEBUG',
                'enable_console': True,
                'enable_file': True,
                'enable_color': True
            }
        return default

def main():
    print("--- 开始测试日志监控器 ---")

    # 初始化模拟的ConfigManager
    mock_config_manager = MockConfigManager()

    # 初始化LoggerMonitor
    logger_monitor = LoggerMonitor(mock_config_manager)

    # 启动监控 (可选，但为了全面测试，启动它)
    logger_monitor.start_monitoring()

    # 记录不同级别的日志
    logger_monitor.logger.debug("这是一条调试信息。")
    logger_monitor.logger.info("这是一条普通信息，表示操作成功完成。")
    logger_monitor.logger.warning("这是一条警告信息，表示可能存在问题。")
    logger_monitor.logger.error("这是一条错误信息，表示发生了严重错误。")
    try:
        raise ValueError("模拟一个异常")
    except ValueError as e:
        logger_monitor.logger.critical("这是一条严重错误信息，系统即将崩溃。", exc_info=True)

    print("\n--- 等待日志处理器处理完成... ---")
    time.sleep(1) # 给日志处理器一些时间

    # 记录性能日志
    logger_monitor.log_performance("数据处理", 0.123, success=True, records=100)
    logger_monitor.log_performance("API调用", 0.543, success=False, endpoint="/api/v1/data")

    # 记录交易事件
    logger_monitor.log_trade_event("ORDER_FILLED", "BTC/USDT", {"price": 30000, "quantity": 0.01})

    # 获取并打印日志统计信息
    stats = logger_monitor.get_log_statistics()
    print("\n--- 日志统计信息 ---")
    for k, v in stats.items():
        print(f"{k}: {v}")

    # 获取最近日志
    recent_logs = logger_monitor.get_recent_logs(count=5)
    print("\n--- 最近5条日志 ---")
    for log in recent_logs:
        print(f"[{log['timestamp']}] {log['level']} - {log['message']}")

    # 停止监控
    logger_monitor.stop_monitoring()
    print("\n--- 日志监控器测试完成 ---")

if __name__ == "__main__":
    main()