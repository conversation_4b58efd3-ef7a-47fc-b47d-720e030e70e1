"""
简化的风险管理器调试脚本
直接模拟风险检查逻辑来定位allowed_size=0的问题
"""

import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def debug_balance_check():
    """调试余额检查逻辑"""
    print("🔍 开始调试风险管理器余额检查逻辑...")
    
    # 测试不同的账户余额情况
    test_cases = [
        {
            'name': '正常余额数据(5000 USDT)',
            'balance': {
                'USDT': {
                    'available': 5000.0,
                    'total': 5000.0
                }
            }
        },
        {
            'name': '低余额数据(50 USDT)',
            'balance': {
                'USDT': {
                    'available': 50.0,
                    'total': 50.0
                }
            }
        },
        {
            'name': '极低余额数据(10 USDT)',
            'balance': {
                'USDT': {
                    'available': 10.0,
                    'total': 10.0
                }
            }
        },
        {
            'name': '缺少USDT数据',
            'balance': {
                'BTC': {
                    'available': 0.1,
                    'total': 0.1
                }
            }
        },
        {
            'name': '空余额数据',
            'balance': {}
        },
        {
            'name': '字符串格式余额',
            'balance': {
                'USDT': {
                    'available': '5000.0',  # 字符串
                    'total': '5000.0'
                }
            }
        }
    ]
    
    # 模拟交易决策
    decision = {
        'decision': 'BUY',
        'confidence': 0.75,
        'position_size': 0.1,
        'reasoning': '多个时间周期技术指标显示上涨趋势'
    }
    
    for i, test_case in enumerate(test_cases):
        print(f"\n{'='*60}")
        print(f"🧪 测试案例 {i+1}: {test_case['name']}")
        print(f"{'='*60}")
        
        account_balance = test_case['balance']
        
        # 模拟风险管理器的余额检查逻辑
        result = check_account_balance_simulation(account_balance)
        
        print(f"📊 余额检查结果:")
        print(f"  通过: {result['passed']}")
        print(f"  允许仓位: {result['allowed_size']}")
        if result.get('errors'):
            print(f"  错误: {result['errors']}")
        
        # 分析为什么allowed_size为0
        if result['allowed_size'] == 0:
            print(f"🚨 CRITICAL: allowed_size为0的原因分析:")
            print(f"    原始数据: {account_balance}")
            print(f"    USDT数据: {account_balance.get('USDT', 'MISSING')}")
            if 'USDT' in account_balance:
                usdt_data = account_balance['USDT']
                print(f"    可用余额: {usdt_data.get('available', 'MISSING')} (类型: {type(usdt_data.get('available'))})")
                print(f"    总权益: {usdt_data.get('total', 'MISSING')} (类型: {type(usdt_data.get('total'))})")

def check_account_balance_simulation(account_balance):
    """模拟风险管理器的账户余额检查逻辑"""
    try:
        result = {'passed': True, 'errors': [], 'allowed_size': 1.0}
        
        # 详细记录账户余额数据
        logger.info(f"🔍 账户余额检查开始")
        logger.info(f"📊 原始账户数据: {account_balance}")
        logger.info(f"📊 数据类型: {type(account_balance)}")
        
        # 获取USDT余额
        usdt_balance = account_balance.get('USDT', {})
        logger.info(f"💰 USDT余额数据: {usdt_balance}")
        logger.info(f"💰 USDT数据类型: {type(usdt_balance)}")
        
        available_balance = usdt_balance.get('available', 0)
        total_equity = usdt_balance.get('total', usdt_balance.get('equity', 0))
        
        logger.info(f"💵 可用余额: {available_balance} (类型: {type(available_balance)})")
        logger.info(f"💵 总权益: {total_equity} (类型: {type(total_equity)})")
        
        # 类型转换处理
        try:
            available_balance = float(available_balance) if available_balance is not None else 0
            total_equity = float(total_equity) if total_equity is not None else 0
        except (ValueError, TypeError) as e:
            logger.error(f"❌ 数据类型转换失败: {e}")
            result['passed'] = False
            result['allowed_size'] = 0
            result['errors'].append(f"余额数据类型错误: {e}")
            return result
        
        # 检查最小余额要求
        min_balance = 100  # 最小100 USDT
        logger.info(f"⚠️ 最小余额要求: {min_balance} USDT")
        
        if available_balance < min_balance:
            logger.error(f"❌ 余额检查失败: {available_balance} < {min_balance}")
            result['passed'] = False
            result['allowed_size'] = 0
            result['errors'].append(f"账户余额不足，当前: {available_balance} USDT，最小要求: {min_balance} USDT")
            return result
        else:
            logger.info(f"✅ 余额充足: {available_balance} >= {min_balance}")
        
        # 检查资金是否足够支持最小仓位(0.1)
        min_position_value = 0.1 * total_equity if total_equity > 0 else 0
        logger.info(f"📈 最小仓位价值: {min_position_value} USDT (基于总权益 {total_equity})")
        
        if available_balance < min_position_value:
            logger.error(f"❌ 仓位资金检查失败: {available_balance} < {min_position_value}")
            result['passed'] = False
            result['allowed_size'] = 0
            result['errors'].append(f"资金不足以支持最小仓位: 需要{min_position_value:.2f} USDT, 可用{available_balance:.2f} USDT")
            return result
        else:
            logger.info(f"✅ 仓位资金充足: {available_balance} >= {min_position_value}")
        
        logger.info(f"✅ 账户余额检查通过，允许仓位: {result['allowed_size']}")
        return result
        
    except Exception as e:
        logger.error(f"❌ 余额检查异常: {e}")
        return {'passed': False, 'errors': [f"余额检查失败: {str(e)}"], 'allowed_size': 0}

if __name__ == '__main__':
    debug_balance_check()
    print(f"\n🎯 调试完成！")
    print(f"基于以上测试结果，我们可以分析allowed_size=0的根本原因。")