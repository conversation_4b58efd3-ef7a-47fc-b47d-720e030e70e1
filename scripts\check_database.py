#!/usr/bin/env python3
"""
检查数据库内容
"""

import sys
import os
import sqlite3

def check_database():
    """检查数据库内容"""
    try:
        print("=== 检查数据库内容 ===")
        db_path = 'credentials.db'
        
        if not os.path.exists(db_path):
            print("数据库文件不存在")
            return False
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查看所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"数据库表: {[t[0] for t in tables]}")
        
        # 查看credentials表内容
        if ('credentials',) in tables:
            cursor.execute('SELECT * FROM credentials')
            rows = cursor.fetchall()
            print(f"credentials表行数: {len(rows)}")
            
            cursor.execute('PRAGMA table_info(credentials)')
            columns = cursor.fetchall()
            print(f"credentials表结构: {[col[1] for col in columns]}")
            
            # 查看所有记录
            cursor.execute('SELECT service, key FROM credentials')
            all_rows = cursor.fetchall()
            print("所有凭证记录:")
            for row in all_rows:
                print(f"  {row[0]}: {row[1]}")
            
            # 查看是否有OKX相关的记录
            cursor.execute("SELECT service, key FROM credentials WHERE service LIKE '%okx%' OR service LIKE '%OKX%'")
            okx_rows = cursor.fetchall()
            print(f"OKX相关记录: {len(okx_rows)}")
            for row in okx_rows:
                print(f"  {row[0]}: {row[1]}")
        
        # 查看config表内容
        if ('config',) in tables:
            cursor.execute('SELECT * FROM config')
            rows = cursor.fetchall()
            print(f"config表行数: {len(rows)}")
            
            cursor.execute('PRAGMA table_info(config)')
            columns = cursor.fetchall()
            print(f"config表结构: {[col[1] for col in columns]}")
            
            # 查看配置内容
            cursor.execute('SELECT key, value FROM config')
            config_rows = cursor.fetchall()
            print("配置记录:")
            for row in config_rows:
                print(f"  {row[0]}: {row[1][:50]}..." if len(row[1]) > 50 else f"  {row[0]}: {row[1]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"检查数据库失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    check_database()
