#!/usr/bin/env python3
"""
AI JSON解析工具类
统一处理AI返回的JSON响应，支持各种格式错误的修复
"""

import json
import re
import logging
from typing import Dict, Any, Optional


class AIJsonParser:
    """AI JSON解析器，专门处理AI返回的复杂JSON格式"""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化JSON解析器
        
        Args:
            logger: 日志记录器，如果不提供则创建默认的
        """
        self.logger = logger or logging.getLogger(__name__)
    
    def parse(self, response_text: str) -> Optional[Dict[str, Any]]:
        """
        强大的JSON解析函数，能处理常见的格式错误
        
        Args:
            response_text: AI返回的原始文本
            
        Returns:
            解析后的字典，如果解析失败返回None
        """
        try:
            # 1. 基本清理
            cleaned = response_text.strip()
            self.logger.debug(f"原始响应长度: {len(cleaned)}")

            # 2. 移除markdown代码块标记
            cleaned = self._remove_markdown_blocks(cleaned)

            # 3. 尝试直接解析
            result = self._try_direct_parse(cleaned)
            if result is not None:
                return result

            # 4. 智能修复reasoning字段
            cleaned = self._fix_reasoning_field(cleaned)

            # 5. 通用JSON修复
            cleaned = self._apply_general_fixes(cleaned)

            # 6. 再次尝试解析
            result = self._try_direct_parse(cleaned)
            if result is not None:
                return result

            # 7. 最后尝试：提取JSON对象并简化
            return self._extract_and_simplify(cleaned, response_text)

        except Exception as e:
            self.logger.error(f"JSON解析过程中发生异常: {e}")
            return None
    
    def _remove_markdown_blocks(self, text: str) -> str:
        """移除markdown代码块标记"""
        if text.startswith('```json'):
            text = text[7:]
        elif text.startswith('```'):
            text = text[3:]
        if text.endswith('```'):
            text = text[:-3]
        return text.strip()
    
    def _try_direct_parse(self, text: str) -> Optional[Dict[str, Any]]:
        """尝试直接解析JSON"""
        try:
            result = json.loads(text)
            # 确保confidence字段为数值类型
            if 'confidence' in result:
                try:
                    result['confidence'] = float(result['confidence'])
                except (ValueError, TypeError):
                    self.logger.warning(f"置信度转换失败: {result['confidence']}")
                    result['confidence'] = 0.5
            return result
        except json.JSONDecodeError:
            return None
    
    def _fix_reasoning_field(self, text: str) -> str:
        """智能修复reasoning字段中的多行文本和特殊字符"""
        try:
            # 使用正则表达式找到reasoning字段
            pattern = r'"reasoning"\s*:\s*"([^"]*(?:\\.[^"]*)*)"'
            match = re.search(pattern, text, re.DOTALL)
            
            if not match:
                # 如果没有找到完整的reasoning字段，尝试修复不完整的JSON
                reasoning_start = text.find('"reasoning":')
                if reasoning_start != -1:
                    # 找到reasoning字段的开始
                    quote_start = text.find('"', reasoning_start + len('"reasoning":'))
                    if quote_start != -1:
                        # 从引号开始查找内容
                        content_start = quote_start + 1
                        
                        # 查找reasoning字段的结束位置
                        pos = content_start
                        while pos < len(text):
                            if text[pos] == '"':
                                # 检查是否是转义的引号
                                if pos == content_start or text[pos-1] != '\\':
                                    # 检查下一个字符是否表示字段结束
                                    next_chars = text[pos+1:].lstrip()
                                    if next_chars.startswith(',') or next_chars.startswith('}'):
                                        break
                            pos += 1
                        
                        if pos < len(text):
                            # 提取reasoning内容
                            reasoning_content = text[content_start:pos]
                            
                            # 清理和转义内容
                            reasoning_content = self._clean_reasoning_content(reasoning_content)
                            
                            # 重构JSON
                            text = text[:content_start] + reasoning_content + text[pos:]
            
            return text
        except Exception as e:
            self.logger.warning(f"修复reasoning字段失败: {e}")
            return text
    
    def _clean_reasoning_content(self, content: str) -> str:
        """清理reasoning内容"""
        # 换行转空格
        content = content.replace('\n', ' ')
        content = content.replace('\r', ' ')
        content = content.replace('\t', ' ')
        
        # 多个空格合并
        content = re.sub(r'\s+', ' ', content)
        
        # 转义特殊字符
        content = content.replace('\\', '\\\\')
        content = content.replace('"', '\\"')
        
        return content.strip()
    
    def _apply_general_fixes(self, text: str) -> str:
        """应用通用的JSON修复"""
        # 移除末尾多余的逗号
        text = re.sub(r',(\s*[}\]])', r'\1', text)
        
        # 修复单引号为双引号（仅针对键名）
        text = re.sub(r"'(\w+)':", r'"\1":', text)
        
        # 修复未引用的键名
        text = re.sub(r'(\w+):', r'"\1":', text)
        
        # 修复重复引号
        text = re.sub(r'""(\w+)"":', r'"\1":', text)
        
        return text
    
    def _extract_and_simplify(self, cleaned: str, original_text: str) -> Optional[Dict[str, Any]]:
        """提取JSON对象并在必要时简化"""
        # 尝试提取JSON对象
        json_match = re.search(r'\{.*\}', cleaned, re.DOTALL)
        if json_match:
            json_str = json_match.group()
            
            # 再次修复常见错误
            json_str = re.sub(r',(\s*[}\]])', r'\1', json_str)
            
            result = self._try_direct_parse(json_str)
            if result is not None:
                return result
        
        # 如果所有方法都失败，尝试提取关键信息构造简化JSON
        try:
            decision_match = re.search(r'"decision"\s*:\s*"([^"]*)"', original_text)
            confidence_match = re.search(r'"confidence"\s*:\s*([0-9.]+)', original_text)
            
            if decision_match and confidence_match:
                # 构造简化的JSON
                simple_json = {
                    "decision": decision_match.group(1),
                    "confidence": float(confidence_match.group(1)),
                    "reasoning": "AI分析完成，详细推理过程已简化"
                }
                self.logger.info("使用简化的JSON响应")
                return simple_json
        except Exception as e:
            self.logger.warning(f"简化JSON失败: {e}")
        
        # 如果所有方法都失败，返回None
        self.logger.error(f"JSON解析完全失败，原始文本前200字符: {original_text[:200]}")
        return None


# 创建全局实例，方便直接使用
default_parser = AIJsonParser()


def parse_ai_json(response_text: str, logger: Optional[logging.Logger] = None) -> Optional[Dict[str, Any]]:
    """
    便捷函数：解析AI返回的JSON
    
    Args:
        response_text: AI返回的原始文本
        logger: 可选的日志记录器
        
    Returns:
        解析后的字典，如果解析失败返回None
    """
    if logger:
        parser = AIJsonParser(logger)
        return parser.parse(response_text)
    else:
        return default_parser.parse(response_text)


if __name__ == "__main__":
    # 测试代码
    import logging
    
    # 移除自定义的basicConfig设置，使用统一的日志格式
    logger = logging.getLogger(__name__)
    
    # 测试用例
    test_cases = [
        # 正常JSON
        '{"decision": "BUY", "confidence": 0.8, "reasoning": "正常推理"}',
        
        # 字符串confidence
        '{"decision": "HOLD", "confidence": "0.55", "reasoning": "字符串置信度"}',
        
        # 多行reasoning
        '''{"decision": "SELL", "confidence": 0.6, "reasoning": "
        多行推理内容
        包含换行符
        "}''',
        
        # 带markdown
        '''```json
        {"decision": "BUY", "confidence": 0.7, "reasoning": "带markdown的JSON"}
        ```''',
    ]
    
    parser = AIJsonParser(logger)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}:")
        print(f"输入: {test_case[:50]}...")
        result = parser.parse(test_case)
        if result:
            print(f"✅ 解析成功: {result}")
        else:
            print(f"❌ 解析失败")
