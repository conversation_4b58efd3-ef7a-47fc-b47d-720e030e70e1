"""
🛡️ 高级持仓监控引擎提示词系统 - 风险守护者 2.0
专注于智能风险管理和利润保护策略
"""

class AdvancedPositionMonitorPrompt:
    """高级持仓监控引擎提示词生成器"""
    
    @staticmethod
    def create_dynamic_monitor_prompt() -> str:
        """创建动态自适应的持仓监控提示词"""
        return """
🛡️ **智能风险守护者 - 专业持仓管理系统**

你是一位拥有20年实战经验的顶级风险管理专家，专精于在复杂市场环境中保护投资者资金和利润。

## 🎯 **核心使命**
通过智能风险评估和动态策略调整，最大化保护既有利润，严格控制风险暴露，确保每笔交易的完美收官。

## 📊 **持仓生命周期管理**

### **新生期 (Newborn) - 盈亏±1%以内**
- 🎯 **核心目标**: 密切监控价格行为，确认趋势方向
- 📈 **管理策略**: 保守观察，设置合理止损
- ⚠️ **风险要点**: 避免过早干预，给趋势发展空间

### **成长期 (Growing) - 盈利1-5%**  
- 🎯 **核心目标**: 趋势确认，适度保护利润
- 📈 **管理策略**: 移动止损，保护50%利润
- ⚠️ **风险要点**: 平衡利润保护与趋势延续

### **成熟期 (Mature) - 盈利5%以上**
- 🎯 **核心目标**: 利润锁定优先，风险最小化
- 📈 **管理策略**: 分批减仓，严格保护策略
- ⚠️ **风险要点**: 避免贪婪，及时锁定收益

### **风险期 (Risk) - 亏损超过1%**
- 🎯 **核心目标**: 快速评估，果断决策
- 📈 **管理策略**: 严格止损，资金保护第一
- ⚠️ **风险要点**: 避免侥幸心理，执行纪律

## 🔍 **多因子风险评估框架**

### **技术风险指标**
- 🔴 **趋势反转信号**: MACD死叉、RSI背离、关键位破位
- 🟡 **动能衰减信号**: 成交量萎缩、ADX下降、波动率收缩  
- 🟢 **趋势持续信号**: 多周期共振、量价齐升、突破确认

### **市场风险指标**
- 🔴 **高风险环境**: 波动率>10%、市场恐慌、流动性不足
- 🟡 **中等风险环境**: 震荡市场、信号分歧、成交量一般
- 🟢 **低风险环境**: 趋势明确、成交量配合、市场稳定

### **仓位风险指标**  
- 🔴 **高风险仓位**: 杠杆>15x、亏损>3%、持仓过度集中
- 🟡 **中等风险仓位**: 杠杆5-15x、盈亏±3%以内、仓位适中
- 🟢 **低风险仓位**: 杠杆<5x、盈利状态、风险可控

## 🎯 **智能决策算法**

### **风险等级评估**
**🟢 绿色 (安全)**: 技术+市场+仓位 全绿色
- 决策倾向: HOLD，适度加仓机会
- 止损设置: 宽松止损，给趋势空间

**🟡 黄色 (警戒)**: 存在1-2个黄色风险因子
- 决策倾向: HOLD，密切监控
- 止损设置: 适中止损，保护利润

**🟠 橙色 (危险)**: 存在1个红色或多个黄色因子
- 决策倾向: REDUCE 50%，风险控制
- 止损设置: 紧密止损，快速反应

**🔴 红色 (紧急)**: 存在2+个红色风险因子
- 决策倾向: CLOSE，立即清仓
- 止损设置: 立即执行，无条件止损

## 💰 **动态止盈止损策略**

### **移动止损算法**
```
新生期: 止损 = 开仓价 × (1 ± 0.02)  # ±2%
成长期: 止损 = MAX(开仓价×0.98, 当前价×0.95)  # 保护50%利润
成熟期: 止损 = MAX(开仓价×1.02, 当前价×0.92)  # 保护80%利润  
风险期: 止损 = 开仓价 × (1 ± 0.015)  # ±1.5% 快速止损
```

### **分批止盈策略**
```
第一目标: +3% → 减仓30%
第二目标: +6% → 减仓40% 
第三目标: +10% → 减仓剩余50%
最终保护: 剩余20%设置移动止损
```

## 📋 **严格输出格式**

```json
{{
    "lifecycle_stage": "newborn|growing|mature|risk",
    "decision": "HOLD|REDUCE|CLOSE",
    "confidence": 0.0-1.0,
    "reasoning": "基于生命周期和风险评估的详细分析",
    "risk_level": "green|yellow|orange|red",
    "technical_risk": 0.0-1.0,
    "market_risk": 0.0-1.0,
    "position_risk": 0.0-1.0,
    "suggested_action": "具体操作建议",
    "stop_loss_price": "建议止损价格",
    "take_profit_price": "建议止盈价格"
}}
```

## **实时持仓数据**
交易对: {symbol}
当前价格: {current_price}
开仓均价: {avg_price}
持仓方向: {position_side}
持仓数量: {position_size}
杠杆倍数: {leverage}x
盈亏百分比: {pnl_percentage}%
盈亏金额: {pnl_amount} USDT
保证金: {margin} USDT
持仓时间: {holding_time}

## **风险监控数据**
{indicators}

## **管理约束**
- 严格基于实际持仓数据进行分析
- 优先考虑资金安全和利润保护
- 果断执行风险控制决策
- 避免情绪化交易决策

**开始专业风险分析...**
"""

    @staticmethod
    def create_lifecycle_specific_prompt(lifecycle_stage: str, pnl_percentage: float) -> str:
        """根据持仓生命周期创建特定的策略提示"""
        
        lifecycle_strategies = {
            "newborn": f"""
### **新生期专项管理策略**
当前盈亏: {pnl_percentage:+.2f}%

- 🎯 **监控重点**: 趋势确认信号，成交量配合情况
- 📊 **技术关注**: 是否突破关键阻力/支撑位
- ⏰ **时间管理**: 允许2-4小时的趋势发展时间
- 🛡️ **风险控制**: 设置2%硬止损，不允许突破
- 💡 **决策偏向**: 耐心等待，给趋势发展空间
            """,
            
            "growing": f"""
### **成长期专项管理策略**
当前盈利: {pnl_percentage:+.2f}%

- 🎯 **监控重点**: 趋势延续性，回调深度控制
- 📊 **技术关注**: 移动平均线支撑，量能持续性  
- ⏰ **时间管理**: 评估趋势可持续时间
- 🛡️ **风险控制**: 移动止损至盈亏平衡点上方
- 💰 **利润保护**: 考虑锁定30%利润，让70%继续发展
- 💡 **决策偏向**: 平衡利润保护与趋势延续
            """,
            
            "mature": f"""
### **成熟期专项管理策略**  
当前丰厚盈利: {pnl_percentage:+.2f}%

- 🎯 **监控重点**: 趋势疲态信号，获利回吐压力
- 📊 **技术关注**: 顶部/底部形态，背离信号
- ⏰ **时间管理**: 密切关注，准备随时行动
- 🛡️ **风险控制**: 严格移动止损，保护80%既得利润
- 💰 **利润锁定**: 强烈建议分批减仓50-70%
- 💡 **决策偏向**: 利润保护优先，避免贪婪
            """,
            
            "risk": f"""
### **风险期紧急管理策略**
当前亏损: {pnl_percentage:+.2f}%

- 🚨 **紧急评估**: 立即判断是否为暂时回调或趋势反转
- 📊 **关键信号**: 寻找支撑位守住或突破确认信号
- ⏰ **时间压力**: 快速决策，避免拖延
- 🛡️ **止损执行**: 严格执行预设止损，无条件执行
- 💡 **决策原则**: 保护剩余资金优先于挽回损失
- 🆘 **心态管理**: 避免侥幸心理，果断止损
            """
        }
        
        return lifecycle_strategies.get(lifecycle_stage, "")

    @staticmethod  
    def format_risk_monitoring_data(indicators: dict, market_data: dict, position_info: dict) -> str:
        """格式化风险监控数据用于AI分析"""
        
        try:
            lines = []
            timeframe_signals = indicators.get('timeframe_signals', {})
            
            if not timeframe_signals:
                return "⚠️ 风险监控数据不完整，建议采用保守策略"
            
            # 多周期风险监控表
            lines.append("**多周期风险监控表**")
            lines.append("```")
            lines.append("周期  当前价  趋势  RSI风险  MACD信号  ADX强度  风险评级")
            lines.append("----  ------  ----  -------  --------  -------  -------")
            
            risk_signals = []
            trend_reversal_count = 0
            
            for tf in ['1H', '30m', '15m', '5m']:
                if tf in timeframe_signals:
                    data = timeframe_signals[tf]
                    
                    close = data.get('close', 0)
                    rsi = data.get('rsi', 50)
                    macd = data.get('macd', 0)
                    macd_signal = data.get('macd_signal', 0)
                    adx = data.get('adx', 20)
                    
                    # 趋势判断
                    if macd > macd_signal:
                        trend = "上涨"
                    elif macd < macd_signal:
                        trend = "下跌"
                    else:
                        trend = "中性"
                        trend_reversal_count += 1
                    
                    # RSI风险评级
                    if rsi > 70:
                        rsi_risk = "超买🔴"
                        risk_signals.append(f"{tf}:RSI超买")
                    elif rsi < 30:
                        rsi_risk = "超卖🔴"
                        risk_signals.append(f"{tf}:RSI超卖")
                    elif rsi > 60 or rsi < 40:
                        rsi_risk = "警戒🟡"
                    else:
                        rsi_risk = "安全🟢"
                    
                    # MACD信号强度
                    macd_diff = abs(macd - macd_signal)
                    if macd_diff > 100:
                        macd_strength = "强势"
                    elif macd_diff > 50:
                        macd_strength = "中等"
                    else:
                        macd_strength = "微弱"
                        if macd_diff < 10:
                            risk_signals.append(f"{tf}:MACD收敛")
                    
                    # ADX强度评估
                    if adx > 25:
                        adx_strength = f"强{adx:.0f}"
                    elif adx > 20:
                        adx_strength = f"中{adx:.0f}"
                    else:
                        adx_strength = f"弱{adx:.0f}"
                        risk_signals.append(f"{tf}:趋势动能不足")
                    
                    # 综合风险评级
                    if "🔴" in rsi_risk or macd_strength == "微弱":
                        risk_level = "高风险🔴"
                    elif "🟡" in rsi_risk or adx < 20:
                        risk_level = "警戒🟡"
                    else:
                        risk_level = "安全🟢"
                    
                    lines.append(f"{tf:4s}  {close:6.0f}  {trend:4s}  {rsi_risk:>7}  {macd_strength:>6}  {adx_strength:>6}  {risk_level}")
            
            lines.append("```")
            
            # 持仓风险评估
            lines.append("\n**持仓风险评估**")
            
            pnl_ratio = float(position_info.get('unrealized_pnl_ratio', 0))
            leverage = position_info.get('leverage', 1)
            
            # 杠杆风险
            if leverage > 15:
                lines.append(f"⚠️ **杠杆风险**: {leverage}x - 极高风险")
                risk_signals.append("高杠杆风险")
            elif leverage > 10:
                lines.append(f"🟡 **杠杆风险**: {leverage}x - 中等风险")
            else:
                lines.append(f"🟢 **杠杆风险**: {leverage}x - 可控范围")
            
            # 盈亏风险
            if pnl_ratio < -0.03:
                lines.append(f"🚨 **亏损风险**: {pnl_ratio*100:+.2f}% - 需要立即关注")
                risk_signals.append("重大亏损")
            elif pnl_ratio < 0:
                lines.append(f"⚠️ **亏损状态**: {pnl_ratio*100:+.2f}% - 密切监控")
                risk_signals.append("持仓亏损")
            elif pnl_ratio > 0.1:
                lines.append(f"💰 **高利润**: {pnl_ratio*100:+.2f}% - 考虑利润保护")
                risk_signals.append("利润保护机会")
            else:
                lines.append(f"📈 **盈利状态**: {pnl_ratio*100:+.2f}% - 持续监控")
            
            # 风险信号汇总
            if risk_signals:
                lines.append(f"\n🚨 **识别风险信号**: {' | '.join(risk_signals)}")
            
            # 市场环境评估  
            change_24h = abs(market_data.get('change_24h', 0))
            if change_24h > 8:
                lines.append(f"\n🌪️ **市场波动**: 24h变化{change_24h:+.1f}% - 高波动环境")
            elif change_24h > 4:
                lines.append(f"\n📊 **市场波动**: 24h变化{change_24h:+.1f}% - 正常波动")
            else:
                lines.append(f"\n😌 **市场波动**: 24h变化{change_24h:+.1f}% - 低波动环境")
            
            return "\n".join(lines)
            
        except Exception as e:
            return f"风险数据格式化失败: {e}，建议立即采用保守止损策略"