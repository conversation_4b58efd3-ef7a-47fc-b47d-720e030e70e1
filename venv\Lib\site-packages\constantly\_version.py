
# This file was generated by 'versioneer.py' (0.29) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2023-10-28T16:14:45-0700",
 "dirty": false,
 "error": null,
 "full-revisionid": "c63aa51794c314778b5699dd1cec9b3547fe6911",
 "version": "23.10.4"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
