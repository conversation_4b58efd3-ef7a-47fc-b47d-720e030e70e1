#!/usr/bin/env python3
"""检查数据库中的杠杆配置"""

import sqlite3
import os

if os.path.exists('credentials.db'):
    conn = sqlite3.connect('credentials.db')
    cursor = conn.cursor()
    
    print("=== 数据库中的所有配置 ===")
    cursor.execute('SELECT category, key, value FROM settings')
    results = cursor.fetchall()
    
    for row in results:
        print(f"{row[0]}.{row[1]} = {row[2]}")
    
    print("\n=== 杠杆相关配置 ===")
    cursor.execute("SELECT category, key, value FROM settings WHERE key LIKE '%leverage%' OR key LIKE '%max%'")
    leverage_results = cursor.fetchall()
    
    for row in leverage_results:
        print(f"{row[0]}.{row[1]} = {row[2]}")
    
    conn.close()
else:
    print("credentials.db 文件不存在")