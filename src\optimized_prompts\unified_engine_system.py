"""
🚀 统一引擎系统 - 集成开仓和持仓智能决策
结合智能猎手和风险守护者，实现完美的交易决策流程
"""

import json
import logging
from typing import Dict, Any, Optional
from .advanced_opening_engine_prompt import AdvancedOpeningEnginePrompt
from .advanced_position_monitor_prompt import AdvancedPositionMonitorPrompt

class UnifiedEngineSystem:
    """统一的智能交易决策系统"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.opening_prompt = AdvancedOpeningEnginePrompt()
        self.monitor_prompt = AdvancedPositionMonitorPrompt()
        
    def create_optimized_opening_prompt(self, symbol: str, indicators: Dict[str, Any], 
                                      market_data: Dict[str, Any]) -> str:
        """创建优化的开仓决策提示词"""
        try:
            # 获取基础提示词模板
            base_prompt = self.opening_prompt.create_dynamic_opening_prompt()
            
            # 分析市场环境
            market_environment = self._detect_market_environment(indicators, market_data)
            
            # 获取市场特定策略
            market_specific = self.opening_prompt.create_market_specific_prompt(market_environment)
            
            # 格式化市场数据
            formatted_data = self.opening_prompt.format_market_data_for_analysis(indicators, market_data)
            
            # 计算市场波动率
            volatility = self._calculate_volatility(market_data)
            
            # 组装完整提示词
            complete_prompt = base_prompt.format(
                symbol=symbol,
                current_price=market_data.get('last_price', 0),
                change_24h=market_data.get('change_24h', 0),
                volatility=volatility,
                indicators=formatted_data
            )
            
            # 添加市场特定策略
            complete_prompt += "\n\n" + market_specific
            
            # 添加风险提醒
            risk_reminder = self._generate_risk_reminder(market_environment, volatility)
            complete_prompt += "\n\n" + risk_reminder
            
            return complete_prompt
            
        except Exception as e:
            self.logger.error(f"生成开仓提示词失败: {e}")
            return self._get_fallback_opening_prompt()
    
    def create_optimized_monitor_prompt(self, symbol: str, indicators: Dict[str, Any],
                                      market_data: Dict[str, Any], position_info: Dict[str, Any]) -> str:
        """创建优化的持仓监控提示词"""
        try:
            # 获取基础提示词模板
            base_prompt = self.monitor_prompt.create_dynamic_monitor_prompt()
            
            # 判断持仓生命周期
            lifecycle_stage = self._determine_lifecycle_stage(position_info)
            
            # 计算盈亏百分比
            pnl_percentage = float(position_info.get('unrealized_pnl_ratio', 0)) * 100
            
            # 获取生命周期特定策略
            lifecycle_specific = self.monitor_prompt.create_lifecycle_specific_prompt(lifecycle_stage, pnl_percentage)
            
            # 格式化风险监控数据
            risk_data = self.monitor_prompt.format_risk_monitoring_data(indicators, market_data, position_info)
            
            # 计算持仓时间
            holding_time = self._calculate_holding_time(position_info)
            
            # 组装完整提示词
            complete_prompt = base_prompt.format(
                symbol=symbol,
                current_price=market_data.get('last_price', 0),
                avg_price=position_info.get('avg_price', 0),
                position_side=position_info.get('side', 'long').upper(),
                position_size=position_info.get('size', 0),
                leverage=position_info.get('leverage', 1),
                pnl_percentage=pnl_percentage,
                pnl_amount=position_info.get('unrealized_pnl', 0),
                margin=position_info.get('margin', 0),
                holding_time=holding_time,
                indicators=risk_data
            )
            
            # 添加生命周期特定策略
            complete_prompt += "\n\n" + lifecycle_specific
            
            # 添加风险等级评估
            risk_assessment = self._generate_risk_assessment(indicators, position_info)
            complete_prompt += "\n\n" + risk_assessment
            
            return complete_prompt
            
        except Exception as e:
            self.logger.error(f"生成监控提示词失败: {e}")
            return self._get_fallback_monitor_prompt()
    
    def _detect_market_environment(self, indicators: Dict[str, Any], market_data: Dict[str, Any]) -> str:
        """智能检测当前市场环境"""
        try:
            timeframe_signals = indicators.get('timeframe_signals', {})
            
            if not timeframe_signals:
                return "uncertain"
            
            # 分析主要周期的趋势一致性
            trend_consistency = 0
            adx_values = []
            
            for tf in ['1H', '30m', '15m']:
                if tf in timeframe_signals:
                    data = timeframe_signals[tf]
                    macd = data.get('macd', 0)
                    macd_signal = data.get('macd_signal', 0)
                    adx = data.get('adx', 20)
                    rsi = data.get('rsi', 50)
                    
                    adx_values.append(adx)
                    
                    # 趋势一致性判断
                    if macd > macd_signal and rsi > 50:
                        trend_consistency += 1
                    elif macd < macd_signal and rsi < 50:
                        trend_consistency -= 1
            
            # 计算平均ADX
            avg_adx = sum(adx_values) / len(adx_values) if adx_values else 20
            
            # 计算24小时变化幅度
            change_24h = abs(market_data.get('change_24h', 0))
            
            # 市场环境判断逻辑
            if avg_adx > 25 and abs(trend_consistency) >= 2:
                if change_24h > 5:
                    return "breakout"  # 突破市场
                else:
                    return "trending"  # 趋势市场
            elif avg_adx < 20:
                return "ranging"  # 震荡市场
            else:
                return "uncertain"  # 不确定市场
                
        except Exception as e:
            self.logger.error(f"市场环境检测失败: {e}")
            return "uncertain"
    
    def _determine_lifecycle_stage(self, position_info: Dict[str, Any]) -> str:
        """判断持仓生命周期阶段"""
        try:
            # 获取持仓时间（模拟）- 在实际实现中需要从数据库获取
            # 这里使用盈亏状态作为简单判断依据
            pnl_ratio = float(position_info.get('unrealized_pnl_ratio', 0))
            
            if abs(pnl_ratio) < 0.01:  # 盈亏在1%以内
                return "newborn"
            elif 0.01 <= pnl_ratio < 0.05:  # 盈利1-5%
                return "growing"
            elif pnl_ratio >= 0.05:  # 盈利5%以上
                return "mature"
            else:  # 亏损超过1%
                return "risk"
                
        except Exception as e:
            self.logger.error(f"生命周期判断失败: {e}")
            return "newborn"
    
    def _calculate_volatility(self, market_data: Dict[str, Any]) -> float:
        """计算市场波动率"""
        try:
            # 简化的波动率计算 - 基于24小时变化
            change_24h = abs(market_data.get('change_24h', 0))
            
            # 根据24小时变化估算波动率
            if change_24h > 10:
                return round(change_24h * 1.5, 2)  # 高波动
            elif change_24h > 5:
                return round(change_24h * 1.2, 2)  # 中等波动
            else:
                return round(change_24h * 1.0, 2)  # 低波动
                
        except Exception as e:
            self.logger.error(f"波动率计算失败: {e}")
            return 2.0  # 默认波动率
    
    def _calculate_holding_time(self, position_info: Dict[str, Any]) -> str:
        """计算持仓时间（模拟）"""
        # 在实际实现中，需要从数据库获取开仓时间
        # 这里返回模拟值
        return "2小时15分钟"
    
    def _generate_risk_reminder(self, market_environment: str, volatility: float) -> str:
        """生成基于市场环境的风险提醒"""
        risk_reminders = {
            "trending": f"⚠️ **趋势市场风险提醒**: 当前波动率{volatility}%，注意趋势反转风险，设置合理止损。",
            "ranging": f"⚠️ **震荡市场风险提醒**: 当前波动率{volatility}%，注意区间突破风险，控制仓位大小。",
            "breakout": f"🚨 **突破市场风险提醒**: 当前波动率{volatility}%，高波动环境，严格风控，避免假突破。",
            "uncertain": f"🚨 **不确定市场风险提醒**: 当前波动率{volatility}%，信号不明确，建议观望或极小仓位。"
        }
        
        return risk_reminders.get(market_environment, "⚠️ **一般风险提醒**: 严格遵循风险管理原则。")
    
    def _generate_risk_assessment(self, indicators: Dict[str, Any], position_info: Dict[str, Any]) -> str:
        """生成风险评估总结"""
        try:
            pnl_ratio = float(position_info.get('unrealized_pnl_ratio', 0))
            
            assessment = "### **智能风险评估总结**\n\n"
            
            if pnl_ratio > 0.1:  # 盈利超过10%
                assessment += "🎯 **高利润区域**: 建议分批锁定收益，保护既得利润。\n"
                assessment += "📈 **建议策略**: 设置移动止损，考虑减仓50%。"
            elif pnl_ratio > 0.05:  # 盈利5-10%
                assessment += "💰 **良好盈利区域**: 利润保护优先，谨慎管理。\n"
                assessment += "📊 **建议策略**: 设置保护性止损，准备减仓。"
            elif pnl_ratio > 0:  # 微盈利
                assessment += "✅ **盈利区域**: 保持谨慎乐观，密切监控。\n"
                assessment += "🎯 **建议策略**: 移动止损到成本附近。"
            elif pnl_ratio > -0.03:  # 小幅亏损
                assessment += "⚠️ **警戒区域**: 密切关注反转信号，准备止损。\n"
                assessment += "🛡️ **建议策略**: 严格执行止损计划。"
            else:  # 大幅亏损
                assessment += "🚨 **危险区域**: 立即评估止损必要性。\n"
                assessment += "🆘 **建议策略**: 果断止损，保护剩余资金。"
            
            return assessment
            
        except Exception as e:
            return "### **风险评估**: 数据异常，建议采用保守策略。"
    
    def _get_fallback_opening_prompt(self) -> str:
        """获取备用开仓提示词"""
        return """
简化开仓分析：基于当前数据做出BUY/SELL/HOLD决策
输出格式：{"decision": "HOLD", "confidence": 0.5, "reasoning": "数据不足"}
"""
    
    def _get_fallback_monitor_prompt(self) -> str:
        """获取备用监控提示词"""
        return """
简化持仓监控：基于当前数据做出HOLD/CLOSE决策
输出格式：{"decision": "HOLD", "confidence": 0.5, "reasoning": "数据不足"}
"""

    def validate_ai_response(self, response: str, engine_type: str) -> Dict[str, Any]:
        """验证AI响应格式的完整性"""
        try:
            # 清理响应
            cleaned_response = response.strip()
            if cleaned_response.startswith('```json'):
                cleaned_response = cleaned_response[7:]
            if cleaned_response.endswith('```'):
                cleaned_response = cleaned_response[:-3]
            
            # 解析JSON
            parsed_response = json.loads(cleaned_response)
            
            # 验证必需字段
            if engine_type == "opening":
                required_fields = ["decision", "confidence", "reasoning"]
                valid_decisions = ["BUY", "SELL", "HOLD"]
            else:  # monitor
                required_fields = ["decision", "confidence", "reasoning"]
                valid_decisions = ["HOLD", "REDUCE", "CLOSE"]
            
            # 检查必需字段
            for field in required_fields:
                if field not in parsed_response:
                    raise ValueError(f"缺少必需字段: {field}")
            
            # 验证决策有效性
            if parsed_response["decision"] not in valid_decisions:
                raise ValueError(f"无效决策: {parsed_response['decision']}")
            
            # 验证置信度范围
            confidence = float(parsed_response["confidence"])
            if not 0.0 <= confidence <= 1.0:
                raise ValueError(f"置信度超出范围: {confidence}")
            
            return parsed_response
            
        except Exception as e:
            self.logger.error(f"AI响应验证失败: {e}")
            # 返回安全的默认响应
            return {
                "decision": "HOLD",
                "confidence": 0.3,
                "reasoning": f"AI响应解析失败: {str(e)}，采用保守策略",
                "error": True
            }