#!/usr/bin/env python3
"""
检查现有数据库配置
"""

import sqlite3
import os

def check_existing_databases():
    """检查现有数据库"""
    print("=== 检查现有数据库配置 ===")
    
    # 检查所有可能的数据库文件
    db_files = ['credentials.db', 'config.db', 'trading.db', 'database.db']
    found_dbs = []
    
    for db_file in db_files:
        if os.path.exists(db_file):
            found_dbs.append(db_file)
            print(f"\n✅ 找到数据库文件: {db_file}")
            
            try:
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()
                
                # 查看所有表
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [t[0] for t in cursor.fetchall()]
                print(f"  📋 表: {tables}")
                
                # 如果有credentials表，查看其内容
                if 'credentials' in tables:
                    cursor.execute('PRAGMA table_info(credentials)')
                    columns = [col[1] for col in cursor.fetchall()]
                    print(f"  🏗️ credentials表结构: {columns}")
                    
                    cursor.execute('SELECT COUNT(*) FROM credentials')
                    count = cursor.fetchone()[0]
                    print(f"  📊 credentials表记录数: {count}")
                    
                    if count > 0:
                        # 查看记录但不显示敏感信息
                        cursor.execute('SELECT service_name FROM credentials')
                        services = [row[0] for row in cursor.fetchall()]
                        print(f"  🔑 配置的服务: {services}")
                        
                        # 检查OKX配置
                        cursor.execute('SELECT service_name, api_key, api_secret, passphrase FROM credentials WHERE service_name = ?', ('okx',))
                        okx_row = cursor.fetchone()
                        if okx_row:
                            service, api_key, api_secret, passphrase = okx_row
                            print(f"  🎯 OKX配置:")
                            print(f"    - API Key: {'已设置' if api_key else '未设置'}")
                            print(f"    - API Secret: {'已设置' if api_secret else '未设置'}")
                            print(f"    - Passphrase: {'已设置' if passphrase else '未设置'}")
                        else:
                            print("  ❌ 未找到OKX配置")
                
                conn.close()
                
            except Exception as e:
                print(f"  ❌ 错误: {e}")
    
    if not found_dbs:
        print("❌ 未找到任何数据库文件")
    
    return found_dbs

def check_config_manager_path():
    """检查ConfigManager使用的数据库路径"""
    print("\n=== 检查ConfigManager配置 ===")
    
    try:
        import sys
        sys.path.insert(0, 'src')
        from src.config_manager import ConfigManager
        
        config = ConfigManager()
        print(f"📁 ConfigManager使用的数据库路径: {config.db_path}")
        print(f"📁 数据库文件存在: {os.path.exists(config.db_path)}")
        
        # 检查配置内容
        print(f"📋 配置键: {list(config.config.keys())}")
        
        if 'credentials' in config.config:
            creds = config.config['credentials']
            print(f"🔑 凭证配置: {list(creds.keys()) if creds else '无'}")
        
        return config.db_path
        
    except Exception as e:
        print(f"❌ 检查ConfigManager失败: {e}")
        return None

def suggest_solution(found_dbs, config_db_path):
    """建议解决方案"""
    print("\n=== 解决方案建议 ===")
    
    if not found_dbs:
        print("❌ 没有找到数据库文件，需要重新配置API凭证")
        return
    
    if config_db_path not in found_dbs:
        print(f"⚠️ ConfigManager期望的数据库文件 '{config_db_path}' 不存在")
        print(f"📁 但找到了这些数据库文件: {found_dbs}")
        print("🔧 可能的解决方案:")
        print("  1. 将现有数据库重命名为 'credentials.db'")
        print("  2. 或修改ConfigManager使用正确的数据库路径")
    else:
        print("✅ 数据库文件路径正确")
        print("🔧 请检查:")
        print("  1. credentials表中是否有OKX记录")
        print("  2. 字段名是否正确 (service_name, api_key, api_secret, passphrase)")
        print("  3. 凭证值是否正确填写")

if __name__ == "__main__":
    found_dbs = check_existing_databases()
    config_db_path = check_config_manager_path()
    suggest_solution(found_dbs, config_db_path)
