#!/usr/bin/env python3
"""
杠杆计算器
基于OKX API和数学公式，提供完整的杠杆、保证金、风险计算功能
补充python-okx库缺失的计算工具
"""

import logging
import math
from typing import Dict, Optional, Tuple
from decimal import Decimal, ROUND_DOWN

class LeverageCalculator:
    """杠杆计算器"""
    
    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)
        
        # 缓存合约规格
        self.instrument_cache = {}
    
    def get_instrument_specs(self, symbol: str) -> Optional[Dict]:
        """获取合约规格"""
        try:
            if symbol in self.instrument_cache:
                return self.instrument_cache[symbol]
            
            from okx.PublicData import PublicAPI
            public_api = PublicAPI(flag='1', domain='https://www.okx.com')
            
            response = public_api.get_instruments(instType="SWAP", instId=symbol)
            
            if response['code'] == '0' and response['data']:
                instrument = response['data'][0]
                specs = {
                    'instId': instrument['instId'],
                    'ctVal': float(instrument['ctVal']),      # 合约面值
                    'ctMult': float(instrument['ctMult']),    # 合约乘数
                    'ctValCcy': instrument['ctValCcy'],       # 合约面值货币
                    'minSz': float(instrument['minSz']),      # 最小下单量
                    'lotSz': float(instrument['lotSz']),      # 下单数量精度
                    'tickSz': float(instrument['tickSz']),    # 价格精度
                    'settleCcy': instrument['settleCcy'],     # 结算货币
                    'quoteCcy': instrument['quoteCcy'],       # 计价货币
                    'baseCcy': instrument['baseCcy']          # 基础货币
                }
                
                self.instrument_cache[symbol] = specs
                return specs
            
            return None
            
        except Exception as e:
            self.logger.error(f"获取合约规格失败: {e}")
            return None
    
    def calculate_position_value(self, symbol: str, contracts: float, price: float) -> Dict:
        """
        计算持仓价值
        
        Args:
            symbol: 交易对符号
            contracts: 合约数量
            price: 价格
            
        Returns:
            持仓价值信息
        """
        try:
            specs = self.get_instrument_specs(symbol)
            if not specs:
                raise ValueError(f"无法获取合约规格: {symbol}")
            
            ct_val = specs['ctVal']
            ct_mult = specs['ctMult']
            
            # 计算名义价值
            notional_value = contracts * ct_val * ct_mult * price
            
            return {
                'success': True,
                'symbol': symbol,
                'contracts': contracts,
                'price': price,
                'contract_value': ct_val,
                'contract_multiplier': ct_mult,
                'notional_value': notional_value,
                'calculation': f"{contracts} × {ct_val} × {ct_mult} × {price} = {notional_value}"
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def calculate_margin_requirement(self, symbol: str, contracts: float, price: float, leverage: int) -> Dict:
        """
        计算保证金需求
        
        Args:
            symbol: 交易对符号
            contracts: 合约数量
            price: 价格
            leverage: 杠杆倍数
            
        Returns:
            保证金计算结果
        """
        try:
            # 先计算持仓价值
            position_result = self.calculate_position_value(symbol, contracts, price)
            if not position_result['success']:
                return position_result
            
            notional_value = position_result['notional_value']
            
            # 计算保证金需求
            margin_requirement = notional_value / leverage
            
            # 计算风险指标
            liquidation_buffer = margin_requirement * 0.1  # 10%缓冲
            safe_margin = margin_requirement * 1.2  # 建议保证金
            
            return {
                'success': True,
                'symbol': symbol,
                'contracts': contracts,
                'price': price,
                'leverage': leverage,
                'notional_value': notional_value,
                'margin_requirement': margin_requirement,
                'liquidation_buffer': liquidation_buffer,
                'safe_margin': safe_margin,
                'margin_ratio': (margin_requirement / notional_value) * 100,
                'calculation_steps': [
                    f"名义价值: {notional_value:.2f} USDT",
                    f"杠杆倍数: {leverage}x",
                    f"保证金需求: {notional_value:.2f} ÷ {leverage} = {margin_requirement:.2f} USDT",
                    f"建议保证金: {safe_margin:.2f} USDT (含20%安全缓冲)"
                ]
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def calculate_optimal_leverage(self, symbol: str, available_margin: float, target_contracts: float, price: float) -> Dict:
        """
        计算最优杠杆倍数
        
        Args:
            symbol: 交易对符号
            available_margin: 可用保证金
            target_contracts: 目标合约数量
            price: 价格
            
        Returns:
            最优杠杆计算结果
        """
        try:
            # 计算持仓价值
            position_result = self.calculate_position_value(symbol, target_contracts, price)
            if not position_result['success']:
                return position_result
            
            notional_value = position_result['notional_value']
            
            # 计算理论杠杆
            theoretical_leverage = notional_value / available_margin
            
            # 考虑安全因子
            safe_leverage = theoretical_leverage * 0.8  # 80%安全系数
            
            # 取整到合理范围
            optimal_leverage = max(1, min(100, math.floor(safe_leverage)))
            
            # 计算实际保证金需求
            actual_margin = notional_value / optimal_leverage
            margin_utilization = (actual_margin / available_margin) * 100
            
            return {
                'success': True,
                'symbol': symbol,
                'target_contracts': target_contracts,
                'price': price,
                'available_margin': available_margin,
                'notional_value': notional_value,
                'theoretical_leverage': theoretical_leverage,
                'optimal_leverage': optimal_leverage,
                'actual_margin_required': actual_margin,
                'margin_utilization': margin_utilization,
                'safety_buffer': available_margin - actual_margin,
                'recommendation': self._get_leverage_recommendation(optimal_leverage, margin_utilization)
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def calculate_liquidation_price(self, symbol: str, entry_price: float, contracts: float, 
                                  leverage: int, side: str = 'long') -> Dict:
        """
        计算强平价格
        
        Args:
            symbol: 交易对符号
            entry_price: 开仓价格
            contracts: 合约数量
            leverage: 杠杆倍数
            side: 持仓方向 ('long' 或 'short')
            
        Returns:
            强平价格计算结果
        """
        try:
            # 计算保证金
            margin_result = self.calculate_margin_requirement(symbol, contracts, entry_price, leverage)
            if not margin_result['success']:
                return margin_result
            
            margin_requirement = margin_result['margin_requirement']
            
            # 强平价格计算（简化版，实际OKX有更复杂的计算）
            # 假设维持保证金率为1%
            maintenance_margin_rate = 0.01
            
            if side.lower() == 'long':
                # 多头强平价格 = 开仓价格 × (1 - 1/杠杆 + 维持保证金率)
                liquidation_price = entry_price * (1 - 1/leverage + maintenance_margin_rate)
            else:
                # 空头强平价格 = 开仓价格 × (1 + 1/杠杆 - 维持保证金率)
                liquidation_price = entry_price * (1 + 1/leverage - maintenance_margin_rate)
            
            # 计算距离强平的价格变化
            price_change_to_liquidation = abs(liquidation_price - entry_price)
            price_change_percentage = (price_change_to_liquidation / entry_price) * 100
            
            return {
                'success': True,
                'symbol': symbol,
                'entry_price': entry_price,
                'contracts': contracts,
                'leverage': leverage,
                'side': side,
                'liquidation_price': liquidation_price,
                'price_change_to_liquidation': price_change_to_liquidation,
                'price_change_percentage': price_change_percentage,
                'margin_requirement': margin_requirement,
                'maintenance_margin_rate': maintenance_margin_rate * 100,
                'risk_level': self._get_liquidation_risk_level(price_change_percentage)
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def calculate_pnl_at_price(self, symbol: str, entry_price: float, current_price: float,
                             contracts: float, side: str = 'long') -> Dict:
        """
        计算指定价格下的盈亏
        
        Args:
            symbol: 交易对符号
            entry_price: 开仓价格
            current_price: 当前价格
            contracts: 合约数量
            side: 持仓方向
            
        Returns:
            盈亏计算结果
        """
        try:
            specs = self.get_instrument_specs(symbol)
            if not specs:
                raise ValueError(f"无法获取合约规格: {symbol}")
            
            ct_val = specs['ctVal']
            ct_mult = specs['ctMult']
            
            # 计算价格变化
            price_change = current_price - entry_price
            
            # 计算盈亏
            if side.lower() == 'long':
                pnl = contracts * ct_val * ct_mult * price_change
            else:
                pnl = contracts * ct_val * ct_mult * (-price_change)
            
            # 计算盈亏百分比（基于名义价值）
            notional_value = contracts * ct_val * ct_mult * entry_price
            pnl_percentage = (pnl / notional_value) * 100 if notional_value > 0 else 0
            
            return {
                'success': True,
                'symbol': symbol,
                'entry_price': entry_price,
                'current_price': current_price,
                'contracts': contracts,
                'side': side,
                'price_change': price_change,
                'pnl': pnl,
                'pnl_percentage': pnl_percentage,
                'notional_value': notional_value,
                'calculation': f"{contracts} × {ct_val} × {ct_mult} × {price_change} = {pnl}"
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _get_leverage_recommendation(self, leverage: int, margin_utilization: float) -> str:
        """获取杠杆建议"""
        if margin_utilization > 90:
            return "⚠️ 高风险：保证金使用率过高，建议降低杠杆"
        elif margin_utilization > 70:
            return "⚠️ 中风险：保证金使用率较高，注意风险控制"
        elif leverage > 20:
            return "⚠️ 高杠杆：建议谨慎操作，设置止损"
        elif leverage > 10:
            return "✅ 中等杠杆：风险可控，建议设置止损"
        else:
            return "✅ 低杠杆：风险较低，相对安全"
    
    def _get_liquidation_risk_level(self, price_change_percentage: float) -> str:
        """获取强平风险等级"""
        if price_change_percentage < 2:
            return "🔴 极高风险"
        elif price_change_percentage < 5:
            return "🟠 高风险"
        elif price_change_percentage < 10:
            return "🟡 中等风险"
        else:
            return "🟢 低风险"

def test_leverage_calculator():
    """测试杠杆计算器"""
    print("🧮 测试杠杆计算器")
    print("=" * 60)
    
    # 模拟配置
    class MockConfig:
        pass
    
    calculator = LeverageCalculator(MockConfig())
    
    # 测试案例：基于你当前的持仓
    symbol = "BTC-USDT-SWAP"
    contracts = 3.38
    entry_price = 118111.0
    current_price = 117740.4
    leverage = 5
    
    print(f"📊 基于当前持仓的计算测试")
    print(f"   交易对: {symbol}")
    print(f"   合约数量: {contracts}")
    print(f"   开仓价格: ${entry_price:,.2f}")
    print(f"   当前价格: ${current_price:,.2f}")
    print(f"   杠杆倍数: {leverage}x")
    
    # 1. 计算持仓价值
    print(f"\n1️⃣ 持仓价值计算:")
    position_result = calculator.calculate_position_value(symbol, contracts, current_price)
    if position_result['success']:
        print(f"   名义价值: ${position_result['notional_value']:,.2f}")
        print(f"   计算公式: {position_result['calculation']}")
    
    # 2. 计算保证金需求
    print(f"\n2️⃣ 保证金需求计算:")
    margin_result = calculator.calculate_margin_requirement(symbol, contracts, current_price, leverage)
    if margin_result['success']:
        print(f"   保证金需求: ${margin_result['margin_requirement']:,.2f}")
        print(f"   建议保证金: ${margin_result['safe_margin']:,.2f}")
        print(f"   保证金率: {margin_result['margin_ratio']:.2f}%")
    
    # 3. 计算强平价格
    print(f"\n3️⃣ 强平价格计算:")
    liquidation_result = calculator.calculate_liquidation_price(symbol, entry_price, contracts, leverage, 'long')
    if liquidation_result['success']:
        print(f"   强平价格: ${liquidation_result['liquidation_price']:,.2f}")
        print(f"   距离强平: {liquidation_result['price_change_percentage']:.2f}%")
        print(f"   风险等级: {liquidation_result['risk_level']}")
    
    # 4. 计算当前盈亏
    print(f"\n4️⃣ 当前盈亏计算:")
    pnl_result = calculator.calculate_pnl_at_price(symbol, entry_price, current_price, contracts, 'long')
    if pnl_result['success']:
        print(f"   盈亏金额: ${pnl_result['pnl']:,.2f}")
        print(f"   盈亏百分比: {pnl_result['pnl_percentage']:,.2f}%")
        print(f"   价格变化: ${pnl_result['price_change']:,.2f}")

if __name__ == "__main__":
    test_leverage_calculator()
