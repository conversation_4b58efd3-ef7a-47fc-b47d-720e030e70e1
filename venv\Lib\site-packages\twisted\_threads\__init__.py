# -*- test-case-name: twisted.test.test_paths -*-
# Copyright (c) Twisted Matrix Laboratories.
# See LICENSE for details.

"""
Twisted integration with operating system threads.
"""


from ._ithreads import AlreadyQuit, IWorker
from ._memory import createMemoryWorker
from ._pool import pool
from ._team import Team
from ._threadworker import <PERSON><PERSON><PERSON><PERSON>, ThreadWorker

__all__ = [
    "ThreadWorker",
    "LockWorker",
    "IWorker",
    "AlreadyQuit",
    "Team",
    "createMemoryWorker",
    "pool",
]
