"""
风险守护者引擎 - Risk Guardian Engine
替代deprecated的PositionMonitorEngine，使用优化的提示词系统
专门负责持仓风险管理和利润保护
"""

import json
import logging
import requests
import time
from typing import Dict, Any, Optional
from .advanced_position_monitor_prompt import AdvancedPositionMonitorPrompt
from src.utils.ai_json_parser import AIJsonParser


class RiskGuardianEngine:
    """
    风险守护者引擎
    
    使用先进的提示词工程技术，专注于：
    - 动态持仓风险评估
    - 智能止盈止损管理
    - 多时间周期风险监控
    - 利润保护和回撤控制
    """
    
    def __init__(self, config_manager: Optional[Any] = None):
        """初始化风险守护者引擎"""
        self.logger = logging.getLogger(__name__)
        self.config_manager = config_manager
        self.deepseek_api_key = None
        self.deepseek_base_url = "https://api.deepseek.com/v1/chat/completions"
        self.json_parser = AIJsonParser(self.logger)
        
        # 初始化提示词生成器
        self.prompt_generator = AdvancedPositionMonitorPrompt()
        
        # 尝试加载配置，失败时优雅降级
        self._load_config()
        
        self.logger.info("风险守护者引擎初始化完成")
    
    def _load_config(self):
        """加载配置和API密钥 - 优先使用ConfigManager"""
        if self.config_manager:
            self.deepseek_api_key = self.config_manager.get('credentials.deepseek.api_key')
        else:
            # 备用方案：从环境变量或文件加载
            self.logger.warning("风险守护者引擎 - 未提供ConfigManager，尝试从环境变量或文件加载")
            import os
            self.deepseek_api_key = os.getenv('DEEPSEEK_API_KEY')
            
            if not self.deepseek_api_key:
                try:
                    config_file = 'config/api_keys.json'
                    if os.path.exists(config_file):
                        import json
                        with open(config_file, 'r', encoding='utf-8') as f:
                            config = json.load(f)
                            self.deepseek_api_key = config.get('deepseek_api_key')
                except Exception:
                    pass
        
        if self.deepseek_api_key:
            self.logger.info("风险守护者引擎 - API密钥加载成功")
        else:
            self.logger.warning("风险守护者引擎 - 未找到API密钥，AI功能将不可用")
    
    def _call_ai_with_retry(self, prompt: str, max_retries: int = 3, timeout: int = 30) -> str:
        """带重试机制的AI调用"""
        if not self.deepseek_api_key:
            raise ValueError("DeepSeek API密钥未配置，无法调用AI")
            
        headers = {
            'Authorization': f'Bearer {self.deepseek_api_key}',
            'Content-Type': 'application/json'
        }
        
        # 使用低温度确保JSON格式准确性
        data = {
            'model': 'deepseek-chat',
            'messages': [{'role': 'user', 'content': prompt}],
            'temperature': 0.1,  # 低温度确保格式一致性
            'max_tokens': 1000
        }
        
        for attempt in range(max_retries + 1):
            try:
                self.logger.info(f"风险守护者 - AI调用尝试 {attempt + 1}/{max_retries + 1}")
                response = requests.post(self.deepseek_base_url, headers=headers, json=data, timeout=timeout)
                response.raise_for_status()
                
                result = response.json()
                ai_response = result['choices'][0]['message']['content'].strip()
                
                if ai_response:
                    return ai_response
                else:
                    raise ValueError("AI返回空响应")
                    
            except Exception as e:
                self.logger.warning(f"风险守护者 - AI调用失败 (尝试 {attempt + 1}/{max_retries + 1}): {e}")
                if attempt < max_retries:
                    wait_time = (attempt + 1) * 2  # 递增等待时间
                    self.logger.info(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                else:
                    raise Exception(f"风险守护者 - AI调用失败，已重试 {max_retries} 次: {e}")
    
    def _get_ai_settings(self) -> Dict[str, Any]:
        """获取AI调用设置"""
        try:
            if self.config_manager:
                ai_timeout = self.config_manager.get_setting('ai_settings', 'timeout') or 30
                ai_retries = self.config_manager.get_setting('ai_settings', 'retries') or 3
                
                return {
                    'ai_timeout': ai_timeout,
                    'ai_retries': ai_retries
                }
            else:
                return {
                    'ai_timeout': 30,
                    'ai_retries': 3
                }
        except Exception as e:
            self.logger.warning(f"获取AI设置失败，使用默认值: {e}")
            return {
                'ai_timeout': 30,
                'ai_retries': 3
            }
    
    def _fallback_decision(self, symbol: str, position_info: Dict[str, Any], reason: str) -> Dict[str, Any]:
        """简化备用决策机制"""
        try:
            # 获取持仓盈亏状态
            pnl_ratio = position_info.get('unrealized_pnl_ratio', 0) * 100
            
            # 基于盈亏状态的简单风险判断
            decision = "HOLD"
            confidence = 0.3
            
            # 如果亏损超过5%，建议平仓
            if pnl_ratio < -5:
                decision = "CLOSE"
                confidence = 0.6
                reasoning_detail = f"亏损超过5%（{pnl_ratio:.2f}%），建议止损"
            # 如果盈利超过10%，可能考虑部分获利
            elif pnl_ratio > 10:
                decision = "HOLD"  # 保守持有，享受利润
                confidence = 0.4
                reasoning_detail = f"盈利良好（{pnl_ratio:.2f}%），保守持有"
            else:
                reasoning_detail = f"盈亏在可控范围（{pnl_ratio:.2f}%），继续观察"
            
            return {
                "decision": decision,
                "confidence": confidence,
                "reasoning": f"{reason}。{reasoning_detail}",
                "stop_loss": 3.0,  # 默认止损
                "take_profit": 8.0  # 默认止盈
            }
            
        except Exception as e:
            self.logger.error(f"备用决策失败: {e}")
            return {
                "decision": "HOLD",
                "confidence": 0.2,
                "reasoning": f"{reason}。系统异常，采用保守策略",
                "stop_loss": None,
                "take_profit": None
            }
    
    def make_monitor_decision(self, symbol: str, indicators: Dict[str, Any], 
                            market_data: Dict[str, Any], position_info: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        做出持仓监控决策
        
        Args:
            symbol: 交易对符号
            indicators: 技术指标数据
            market_data: 市场数据
            position_info: 当前持仓信息
            
        Returns:
            包含监控决策信息的字典，或None（如果失败）
        """
        try:
            # 检查是否有API密钥
            if not self.deepseek_api_key:
                self.logger.warning("DeepSeek API密钥未配置，使用备用决策")
                return self._fallback_decision(symbol, position_info, "API密钥未配置")
            
            # 获取AI调用设置
            ai_settings = self._get_ai_settings()
            
            # 计算关键持仓数据
            current_price = float(market_data.get('last_price', 0))
            avg_price = float(position_info.get('avg_price', current_price))
            position_size = float(position_info.get('size', 0))
            position_side = position_info.get('side', 'long')
            leverage = float(position_info.get('leverage', 1))
            
            # 使用OKX提供的盈亏数据
            pnl_amount = float(position_info.get('unrealized_pnl', 0))
            pnl_ratio = float(position_info.get('unrealized_pnl_ratio', 0))
            pnl_pct = pnl_ratio * 100
            
            # 记录持仓状态
            self.logger.info(f"🛡️ 风险守护者分析持仓: {symbol}")
            self.logger.info(f"  - 当前价格: {current_price}")
            self.logger.info(f"  - 开仓均价: {avg_price}")
            self.logger.info(f"  - 持仓方向: {position_side}")
            self.logger.info(f"  - 杠杆倍数: {leverage}x")
            self.logger.info(f"  - 实际盈亏: {pnl_pct:.4f}% ({pnl_amount:.2f} USDT)")
            
            # 使用优化的提示词生成器创建风险分析提示
            base_prompt = self.prompt_generator.create_dynamic_monitor_prompt()
            
            # 确定持仓生命周期阶段
            pnl_percentage = pnl_pct  # 使用已计算的盈亏百分比
            if abs(pnl_percentage) <= 1:
                lifecycle_stage = "newborn"
            elif pnl_percentage >= 5:
                lifecycle_stage = "mature"
            elif pnl_percentage >= 1:
                lifecycle_stage = "growing"
            elif pnl_percentage <= -1:
                lifecycle_stage = "risk"
            else:
                lifecycle_stage = "newborn"
            
            # 格式化风险监控数据
            formatted_indicators = self.prompt_generator.format_risk_monitoring_data(
                indicators, market_data, position_info
            )
            
            # 获取生命周期特定策略
            lifecycle_prompt = self.prompt_generator.create_lifecycle_specific_prompt(
                lifecycle_stage, pnl_percentage
            )
            
            # 辅助函数：格式化持仓时间
            def format_holding_time(update_time):
                try:
                    if update_time and update_time > 0:
                        import datetime
                        current_time = time.time() * 1000  # 转换为毫秒
                        holding_time_ms = current_time - float(update_time)
                        holding_time_hours = holding_time_ms / (1000 * 60 * 60)
                        if holding_time_hours < 1:
                            return f"{holding_time_hours * 60:.0f}分钟"
                        elif holding_time_hours < 24:
                            return f"{holding_time_hours:.1f}小时"
                        else:
                            return f"{holding_time_hours / 24:.1f}天"
                    return "未知"
                except Exception:
                    return "未知"
            
            # 构建完整提示词
            # 步骤1: 先用replace安全地替换复杂的文本块
            prompt_with_indicators = base_prompt.replace("{indicators}", formatted_indicators)
            
            # 步骤2: 然后对剩余的简单占位符进行格式化
            prompt = prompt_with_indicators.format(
                symbol=symbol,
                current_price=current_price,
                avg_price=avg_price,
                position_side=position_side.upper(),
                position_size=abs(position_size),
                leverage=leverage,
                pnl_percentage=pnl_percentage,
                pnl_amount=pnl_amount,
                margin=abs(position_size * avg_price) / leverage if leverage > 0 else 0,
                holding_time=format_holding_time(position_info.get('update_time', 0))
            ) + "\n\n" + lifecycle_prompt
            
            # 调用AI分析
            self.logger.info(f"🛡️ 风险守护者开始分析: {symbol}")
            ai_response = self._call_ai_with_retry(
                prompt,
                max_retries=ai_settings['ai_retries'],
                timeout=ai_settings['ai_timeout']
            )
            
            # 显示交互信息
            print(f"\n🛡️ ===== {symbol} 风险守护者 → AI =====")
            print(f"📊 持仓: {symbol} | 价格: {current_price} | 杠杆: {leverage}x")
            print(f"💰 盈亏: {pnl_amount:.2f} USDT ({pnl_pct:.2f}%)")
            print(f"\n🤖 ===== {symbol} AI返回 =====")
            print(f"```json\n{ai_response}\n```")
            
            # 解析AI响应
            try:
                # 清理AI响应
                cleaned_response = ai_response.strip()
                if cleaned_response.startswith('```json'):
                    cleaned_response = cleaned_response[7:]
                if cleaned_response.endswith('```'):
                    cleaned_response = cleaned_response[:-3]
                
                decision = self.json_parser.parse(cleaned_response)
                if decision is None:
                    raise json.JSONDecodeError("JSON解析失败", ai_response, 0)
                
                # 获取置信度
                confidence_raw = decision.get('confidence', 0.5)
                try:
                    confidence = float(confidence_raw)
                except (ValueError, TypeError):
                    self.logger.warning(f"置信度转换失败: {confidence_raw}")
                    confidence = 0.5
                
                # 根据置信度计算动态参数
                if self.config_manager:
                    confidence_params = self.config_manager.calculate_confidence_based_parameters(confidence, symbol)
                else:
                    # 使用简化的置信度参数计算
                    confidence_params = {
                        'should_trade': confidence >= 0.4,
                        'stop_loss': max(1.0, min(5.0, (1.4 - confidence) * 5)),
                        'take_profit': max(2.0, min(10.0, confidence * 10))
                    }
                
                # 检查置信度是否足够
                if not confidence_params['should_trade'] and decision.get('decision') == 'CLOSE':
                    print(f"\n⚠️ ===== {symbol} 置信度过低，保守持仓 =====")
                    print(f"📊 置信度: {confidence:.2f}")
                    print(f"💡 建议: 继续观察，避免过度操作")
                    print(f"=" * 60)
                    
                    return {
                        "decision": "HOLD",
                        "confidence": confidence,
                        "reasoning": decision.get('reasoning', '') + " [系统判断: 置信度不足，保守持仓]",
                        "stop_loss": None,
                        "take_profit": None
                    }
                
                # 构建增强决策
                enhanced_decision = {
                    "decision": decision.get('decision'),
                    "confidence": confidence,
                    "reasoning": decision.get('reasoning'),
                    "stop_loss": confidence_params['stop_loss'] if decision.get('decision') != 'CLOSE' else None,
                    "take_profit": confidence_params['take_profit'] if decision.get('decision') != 'CLOSE' else None,
                    "confidence_params": confidence_params
                }
                
                # 显示决策结果
                print(f"\n🛡️ ===== {symbol} 风险守护者决策结果 =====")
                print(f"🎯 决策: {enhanced_decision.get('decision')}")
                print(f"📊 置信度: {enhanced_decision.get('confidence'):.2f}")
                print(f"🧠 推理: {enhanced_decision.get('reasoning')}")
                if enhanced_decision.get('stop_loss') is not None:
                    print(f"🛡️ 止损: {enhanced_decision.get('stop_loss')}%")
                if enhanced_decision.get('take_profit') is not None:
                    print(f"🎯 止盈: {enhanced_decision.get('take_profit')}%")
                print(f"=" * 60)
                
                self.logger.info(f"风险守护者决策完成: {symbol} -> {enhanced_decision.get('decision')} (置信度: {enhanced_decision.get('confidence'):.2f})")
                return enhanced_decision
                
            except json.JSONDecodeError as e:
                self.logger.error(f"风险守护者 - AI响应JSON解析失败: {e}")
                self.logger.error(f"原始AI响应: {ai_response}")
                return self._fallback_decision(symbol, position_info, "AI响应解析失败")
                
        except Exception as e:
            self.logger.error(f"风险守护者决策失败: {e}")
            try:
                return self._fallback_decision(symbol, position_info, f"引擎异常: {str(e)}")
            except Exception as fallback_error:
                self.logger.error(f"备用决策也失败: {fallback_error}")
                return {
                    "decision": "HOLD",
                    "confidence": 0.2,
                    "reasoning": f"系统完全异常: {str(e)}，采用保守策略",
                    "stop_loss": None,
                    "take_profit": None
                }