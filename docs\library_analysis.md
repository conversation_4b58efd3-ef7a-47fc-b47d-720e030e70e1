# 依赖库功能映射表 - DeepSeek加密货币量化交易系统
# 基于深度源代码分析的完整库功能映射

## 1. python-okx (v0.3.9) - OKX交易所API客户端

### 核心类和方法（基于源代码分析）：

#### OkxClient (okxclient.py)
- `__init__(api_key, api_secret_key, passphrase, flag='1', domain='https://www.okx.com', debug=False, proxy=None)` - 初始化客户端
- `_request(method, request_path, params, cursor=False)` - 发送API请求的核心方法
- `_get_timestamp()` - 获取时间戳
- `_sign(timestamp, method, request_path, body)` - 生成签名
- `_get_header(timestamp, sign, method, request_path, body)` - 构建请求头

#### MarketAPI (MarketData.py) - 13个核心方法
- `get_candlesticks(instId, after='', before='', bar='1m', limit='300')` - 获取K线数据
- `get_ticker(instId)` - 获取单个交易对行情
- `get_tickers(instType, uly='', instFamily='')` - 获取多个交易对行情
- `get_orderbook(instId, sz='1')` - 获取订单簿数据
- `get_trades(instId, limit='100')` - 获取最新成交记录
- `get_24hr_total_volume()` - 获取24小时总成交量
- `get_oracle()` - 获取Oracle上链价格
- `get_exchange_rate()` - 获取法币汇率
- `get_index_tickers(quoteCcy='', instId='')` - 获取指数行情
- `get_index_candlesticks(instId, after='', before='', bar='1m', limit='100')` - 获取指数K线
- `get_index_constituents(index)` - 获取指数成分
- `get_block_tickers(instType, uly='', instFamily='')` - 获取大宗交易行情
- `get_block_trades(instId)` - 获取大宗交易记录

#### AccountAPI (Account.py) - 50+个方法
- `get_account_balance(ccy='')` - 获取账户余额
- `get_positions(instType='', instId='', posId='')` - 获取持仓信息
- `get_position_risk(instType='')` - 获取持仓风险
- `get_bills(instType='', ccy='', mgnMode='', ctType='', type='', subType='', after='', before='', limit='100')` - 获取账单流水
- `get_bills_archive(instType='', ccy='', mgnMode='', ctType='', type='', subType='', after='', before='', limit='100')` - 获取账单流水（归档）
- `get_config()` - 获取账户配置
- `set_position_mode(posMode)` - 设置持仓模式
- `set_leverage(lever, mgnMode, instId='', ccy='', posSide='')` - 设置杠杆
- `get_max_order_size(instId, tdMode, ccy='', px='', leverage='')` - 获取最大下单量
- `get_max_avail_size(instId, tdMode, ccy='', reduceOnly='', unSpotOffset='', quickMgnType='')` - 获取最大可用交易量
- `change_position_margin(instId, posSide, type, amt, loanTrans='')` - 调整保证金
- `get_leverage(instId, mgnMode)` - 获取杠杆
- `get_max_loan(instId, mgnMode, mgnCcy)` - 获取最大借贷
- `get_fee_rates(instType, instId='', uly='', category='1', instFamily='')` - 获取交易手续费率
- `get_interest_accrued(instId='', ccy='', mgnMode='', after='', before='', limit='100')` - 获取计息记录
- `get_interest_rate(ccy='')` - 获取利率
- `set_greeks(greeksType)` - 设置希腊字母展示方式
- `isolated_margin_trading_settings(isoMode, type)` - 逐仓交易设置
- `get_max_withdrawal(ccy='')` - 获取最大可提
- `get_account_risk()` - 获取账户风险状态
- `borrow_repay(ccy, side, amt, ordId='')` - 借币还币
- `get_borrow_repay_history(ccy='', after='', before='', limit='100')` - 获取借币还币历史
- `get_vip_interest_accrued(ccy='', ordId='', after='', before='', limit='100')` - 获取VIP贷款计息记录
- `get_vip_interest_deducted(ordId='', ccy='', after='', before='', limit='100')` - 获取VIP贷款扣息记录
- `get_vip_loan_order_list(ordId='', state='', ccy='', after='', before='', limit='100')` - 获取VIP贷款订单列表
- `get_vip_loan_order_detail(ccy, ordId='', after='', before='', limit='100')` - 获取VIP贷款订单详情
- `get_interest_limits(type='', ccy='')` - 获取借贷利率与限额
- `position_builder(inclRealPos='', spotOffsetType='', greeksType='', simPos='')` - 组合保证金模拟仓位
- `get_greeks(ccy='')` - 获取希腊字母PA/BS切换
- `get_position_tiers(instType, tdMode, uly='', instFamily='', ccy='', tier='')` - 获取组合保证金账户仓位梯度

#### TradeAPI (Trade.py) - 30+个方法
- `place_order(instId, tdMode, side, ordType, sz, ccy='', clOrdId='', tag='', posSide='', px='', reduceOnly=False, tgtCcy='', banAmend=False, tpTriggerPx='', tpOrdPx='', slTriggerPx='', slOrdPx='', tpTriggerPxType='', slTriggerPxType='', quickMgnType='', stpId='', stpMode='', attachAlgoOrds='')` - 下单
- `place_multiple_orders(orders_data)` - 批量下单
- `cancel_order(instId, ordId='', clOrdId='')` - 撤销订单
- `cancel_multiple_orders(orders_data)` - 批量撤销订单
- `amend_order(instId, cxlOnFail='', ordId='', clOrdId='', reqId='', newSz='', newPx='', newTpTriggerPx='', newTpOrdPx='', newSlTriggerPx='', newSlOrdPx='', newTpTriggerPxType='', newSlTriggerPxType='')` - 修改订单
- `amend_multiple_orders(orders_data)` - 批量修改订单
- `close_positions(instId, mgnMode, posSide='', ccy='', autoCxl=False, clOrdId='', tag='')` - 平仓
- `get_order(instId, ordId='', clOrdId='')` - 获取订单详情
- `get_order_list(instType='', uly='', instId='', ordType='', state='', after='', before='', limit='100')` - 获取订单列表
- `get_order_history(instType, uly='', instId='', ordType='', state='', category='', after='', before='', limit='100')` - 获取历史订单记录
- `get_order_history_archive(instType, uly='', instId='', ordType='', state='', category='', after='', before='', limit='100')` - 获取历史订单记录（归档）
- `get_fills(instType='', uly='', instId='', ordId='', after='', before='', limit='100')` - 获取成交明细
- `get_fills_history(instType, uly='', instId='', ordId='', after='', before='', limit='100')` - 获取历史成交明细
- `place_algo_order(instId, tdMode, side, ordType, sz, ccy='', posSide='', reduceOnly='', tpTriggerPx='', tpOrdPx='', slTriggerPx='', slOrdPx='', triggerPx='', orderPx='', tpTriggerPxType='', slTriggerPxType='', triggerPxType='', callbackRatio='', callbackSpread='', activePx='', tag='', algoId='', quickMgnType='', algoClOrdId='', closeFraction='')` - 下算法订单
- `cancel_algo_order(algoId, instId)` - 撤销算法订单
- `cancel_advance_algo_order(algoId, instId)` - 撤销高级算法订单
- `get_algo_order_list(ordType, algoId='', instType='', instId='', after='', before='', limit='100')` - 获取算法订单列表
- `get_algo_order_history(ordType, state='', algoId='', instType='', instId='', after='', before='', limit='100')` - 获取算法订单历史
- `get_easy_convert_currency_list()` - 获取一键兑换主流币种列表
- `easy_convert(fromCcy, toCcy, amt, side)` - 一键兑换
- `get_easy_convert_history(after='', before='', limit='100')` - 获取一键兑换历史
- `get_oneclick_repay_currency_list(debtType='')` - 获取一键还债币种列表
- `oneclick_repay(debtCcy, repayCcy)` - 一键还债
- `get_oneclick_repay_history(after='', before='', limit='100')` - 获取一键还债历史

## 2. requests (v2.32.4) - HTTP请求库

### 核心模块（基于源代码分析）：

#### api.py - 主要HTTP方法
- `request(method, url, **kwargs)` - 通用请求方法
- `get(url, params=None, **kwargs)` - GET请求
- `post(url, data=None, json=None, **kwargs)` - POST请求
- `put(url, data=None, **kwargs)` - PUT请求
- `patch(url, data=None, **kwargs)` - PATCH请求
- `delete(url, **kwargs)` - DELETE请求
- `head(url, **kwargs)` - HEAD请求
- `options(url, **kwargs)` - OPTIONS请求

#### models.py - 核心类
- `Request(method=None, url=None, headers=None, files=None, data=None, params=None, auth=None, cookies=None, hooks=None, json=None)` - 请求对象
- `Response()` - 响应对象
  - `status_code` - HTTP状态码
  - `headers` - 响应头
  - `text` - 响应文本内容
  - `content` - 响应二进制内容
  - `json()` - 解析JSON响应
  - `raise_for_status()` - 检查HTTP错误
- `PreparedRequest()` - 预处理请求对象

#### sessions.py - 会话管理
- `Session()` - 会话类
  - `request(method, url, **kwargs)` - 发送请求
  - `get(url, **kwargs)` - GET请求
  - `post(url, data=None, json=None, **kwargs)` - POST请求
  - `put(url, data=None, **kwargs)` - PUT请求
  - `patch(url, data=None, **kwargs)` - PATCH请求
  - `delete(url, **kwargs)` - DELETE请求
  - `head(url, **kwargs)` - HEAD请求
  - `options(url, **kwargs)` - OPTIONS请求
  - `mount(prefix, adapter)` - 挂载适配器
  - `close()` - 关闭会话

## 3. schedule (v1.2.2) - 任务调度库

### 核心类和方法（基于源代码分析）：

#### Scheduler类
- `__init__()` - 初始化调度器
- `every(interval=1)` - 创建任务间隔
- `run_pending()` - 运行待执行任务
- `run_all(delay_seconds=0)` - 运行所有任务
- `get_jobs(tag=None)` - 获取任务列表
- `cancel_job(job)` - 取消指定任务
- `clear(tag=None)` - 清除任务
- `idle_seconds` - 获取下次任务执行的秒数

#### Job类
- `do(job_func, *args, **kwargs)` - 指定要执行的函数
- `tag(*tags)` - 为任务添加标签
- `until(until_time)` - 设置任务截止时间
- `cancel()` - 取消任务

#### 时间间隔方法
- `seconds` - 秒级间隔
- `minutes` - 分钟级间隔
- `hours` - 小时级间隔
- `days` - 天级间隔
- `weeks` - 周级间隔
- `monday`, `tuesday`, `wednesday`, `thursday`, `friday`, `saturday`, `sunday` - 星期
- `at(time_str)` - 指定执行时间

## 4. pandas (v2.3.1) - 数据处理库

### 核心类和方法（基于源代码分析）：

#### DataFrame类 (core/frame.py)
- `__init__(data=None, index=None, columns=None, dtype=None, copy=None)` - 创建数据框
- `head(n=5)` - 显示前n行
- `tail(n=5)` - 显示后n行
- `info(verbose=None, buf=None, max_cols=None, memory_usage=None, show_counts=None)` - 显示数据信息
- `describe(percentiles=None, include=None, exclude=None)` - 描述性统计
- `dropna(axis=0, how='any', thresh=None, subset=None, inplace=False)` - 删除空值
- `fillna(value=None, method=None, axis=None, inplace=False, limit=None, downcast=None)` - 填充空值
- `drop(labels=None, axis=0, index=None, columns=None, level=None, inplace=False, errors='raise')` - 删除行或列
- `sort_values(by, axis=0, ascending=True, inplace=False, kind='quicksort', na_position='last')` - 排序
- `groupby(by=None, axis=0, level=None, as_index=True, sort=True, group_keys=True, squeeze=False, observed=False, dropna=True)` - 分组
- `merge(right, how='inner', on=None, left_on=None, right_on=None, left_index=False, right_index=False, sort=False, suffixes=('_x', '_y'), copy=True, indicator=False, validate=None)` - 合并
- `rolling(window, min_periods=None, center=False, win_type=None, on=None, axis=0, closed=None, step=None, method='single')` - 滚动窗口
- `resample(rule, axis=0, closed=None, label=None, convention='start', kind=None, loffset=None, base=None, on=None, level=None, origin='start_day', offset=None, group_keys=False)` - 重采样
- `to_csv(path_or_buf=None, sep=',', na_rep='', float_format=None, columns=None, header=True, index=True, index_label=None, mode='w', encoding=None, compression='infer', quoting=None, quotechar='"', line_terminator=None, chunksize=None, date_format=None, doublequote=True, escapechar=None, decimal='.')` - 导出CSV
- `to_json(path_or_buf=None, orient=None, date_format=None, double_precision=10, force_ascii=True, date_unit='ms', default_handler=None, lines=False, compression='infer', index=True, indent=None, storage_options=None)` - 导出JSON

#### 读取函数
- `read_csv(filepath_or_buffer, sep=',', delimiter=None, header='infer', names=None, index_col=None, usecols=None, squeeze=False, prefix=None, mangle_dupe_cols=True, dtype=None, engine=None, converters=None, true_values=None, false_values=None, skipinitialspace=False, skiprows=None, skipfooter=0, nrows=None, na_values=None, keep_default_na=True, na_filter=True, verbose=False, skip_blank_lines=True, parse_dates=False, infer_datetime_format=False, keep_date_col=False, date_parser=None, dayfirst=False, cache_dates=True, iterator=False, chunksize=None, compression='infer', thousands=None, decimal='.', lineterminator=None, quotechar='"', quoting=0, doublequote=True, escapechar=None, comment=None, encoding=None, encoding_errors='strict', dialect=None, error_bad_lines=None, warn_bad_lines=None, on_bad_lines=None, delim_whitespace=False, low_memory=True, memory_map=False, float_precision=None, storage_options=None)` - 读取CSV文件
- `read_json(path_or_buf=None, orient=None, typ='frame', dtype=None, convert_axes=None, convert_dates=True, keep_default_dates=True, numpy=False, precise_float=False, date_unit=None, encoding=None, encoding_errors='strict', lines=False, chunksize=None, compression='infer', nrows=None, storage_options=None)` - 读取JSON文件
- `to_datetime(arg, errors='raise', dayfirst=False, yearfirst=False, utc=None, format=None, exact=True, unit=None, infer_datetime_format=False, origin='unix', cache=True)` - 时间转换

## 5. numpy (v2.3.1) - 数值计算库

### 核心函数（基于源代码分析）：

#### 数组创建
- `array(object, dtype=None, copy=True, order='K', subok=False, ndmin=0, like=None)` - 创建数组
- `zeros(shape, dtype=float, order='C', like=None)` - 创建零数组
- `ones(shape, dtype=None, order='C', like=None)` - 创建全1数组
- `empty(shape, dtype=float, order='C', like=None)` - 创建空数组
- `full(shape, fill_value, dtype=None, order='C', like=None)` - 创建填充数组
- `arange(start, stop=None, step=1, dtype=None, like=None)` - 创建等差数组
- `linspace(start, stop, num=50, endpoint=True, retstep=False, dtype=None, axis=0)` - 创建等间距数组

#### 数学运算
- `add(x1, x2, out=None, where=True, casting='same_kind', order='K', dtype=None, subok=True)` - 加法
- `subtract(x1, x2, out=None, where=True, casting='same_kind', order='K', dtype=None, subok=True)` - 减法
- `multiply(x1, x2, out=None, where=True, casting='same_kind', order='K', dtype=None, subok=True)` - 乘法
- `divide(x1, x2, out=None, where=True, casting='same_kind', order='K', dtype=None, subok=True)` - 除法
- `power(x1, x2, out=None, where=True, casting='same_kind', order='K', dtype=None, subok=True)` - 幂运算
- `sqrt(x, out=None, where=True, casting='same_kind', order='K', dtype=None, subok=True)` - 平方根
- `exp(x, out=None, where=True, casting='same_kind', order='K', dtype=None, subok=True)` - 指数函数
- `log(x, out=None, where=True, casting='same_kind', order='K', dtype=None, subok=True)` - 自然对数

#### 统计函数
- `mean(a, axis=None, dtype=None, out=None, keepdims=False, where=True)` - 计算均值
- `std(a, axis=None, dtype=None, out=None, ddof=0, keepdims=False, where=True)` - 计算标准差
- `var(a, axis=None, dtype=None, out=None, ddof=0, keepdims=False, where=True)` - 计算方差
- `max(a, axis=None, out=None, keepdims=False, initial=None, where=True)` - 最大值
- `min(a, axis=None, out=None, keepdims=False, initial=None, where=True)` - 最小值
- `sum(a, axis=None, dtype=None, out=None, keepdims=False, initial=None, where=True)` - 求和
- `median(a, axis=None, out=None, overwrite_input=False, keepdims=False)` - 中位数

#### 条件和逻辑
- `where(condition, x=None, y=None)` - 条件选择
- `logical_and(x1, x2, out=None, where=True, casting='same_kind', order='K', dtype=None, subok=True)` - 逻辑与
- `logical_or(x1, x2, out=None, where=True, casting='same_kind', order='K', dtype=None, subok=True)` - 逻辑或
- `logical_not(x, out=None, where=True, casting='same_kind', order='K', dtype=None, subok=True)` - 逻辑非

## 6. flask (v3.1.1) - Web框架

### 核心类和方法（基于源代码分析）：

#### Flask类 (app.py)
- `__init__(import_name, static_url_path=None, static_folder='static', static_host=None, host_matching=False, subdomain_matching=False, template_folder='templates', instance_path=None, instance_relative_config=False, root_path=None)` - 创建Flask应用
- `route(rule, **options)` - 路由装饰器
- `add_url_rule(rule, endpoint=None, view_func=None, provide_automatic_options=None, **options)` - 添加URL规则
- `before_request(f)` - 请求前处理装饰器
- `after_request(f)` - 请求后处理装饰器
- `errorhandler(code_or_exception)` - 错误处理装饰器
- `run(host=None, port=None, debug=None, load_dotenv=True, **options)` - 运行应用
- `test_client(use_cookies=True, **kwargs)` - 创建测试客户端

#### 请求处理
- `render_template(template_name_or_list, **context)` - 渲染模板
- `render_template_string(source, **context)` - 渲染模板字符串
- `redirect(location, code=302, Response=None)` - 重定向
- `url_for(endpoint, **values)` - 生成URL
- `abort(code, description=None, response=None)` - 中止请求
- `make_response(*args)` - 创建响应对象
- `jsonify(*args, **kwargs)` - 返回JSON响应
- `send_file(path_or_file, mimetype=None, as_attachment=False, download_name=None, conditional=True, etag=True, last_modified=None, max_age=None)` - 发送文件
- `send_from_directory(directory, path, **kwargs)` - 从目录发送文件

#### 全局对象
- `request` - 当前请求对象
  - `args` - URL参数
  - `form` - 表单数据
  - `files` - 上传文件
  - `json` - JSON数据
  - `method` - HTTP方法
  - `headers` - 请求头
- `session` - 会话对象
- `g` - 应用上下文全局对象
- `current_app` - 当前应用实例

## 7. TA-Lib (v0.6.3) - 技术分析指标库

### 核心指标函数（基于深度分析，共358个函数）：

#### 重叠研究指标 (Overlap Studies) - 16个函数
- `SMA(close, timeperiod=30)` - 简单移动平均线
- `EMA(close, timeperiod=30)` - 指数移动平均线
- `WMA(close, timeperiod=30)` - 加权移动平均线
- `DEMA(close, timeperiod=30)` - 双指数移动平均线
- `TEMA(close, timeperiod=30)` - 三重指数移动平均线
- `TRIMA(close, timeperiod=30)` - 三角移动平均线
- `KAMA(close, timeperiod=30)` - 考夫曼自适应移动平均线
- `MAMA(close, fastlimit=0.5, slowlimit=0.05)` - MESA自适应移动平均线
- `T3(close, timeperiod=5, vfactor=0.7)` - T3移动平均线
- `MA(close, timeperiod=30, matype=0)` - 移动平均线（可选类型）
- `MAVP(close, periods, minperiod=2, maxperiod=30, matype=0)` - 可变周期移动平均线
- `MIDPOINT(close, timeperiod=14)` - 中点价格
- `MIDPRICE(high, low, timeperiod=14)` - 中间价格
- `SAR(high, low, acceleration=0.02, maximum=0.2)` - 抛物线SAR
- `SAREXT(high, low, startvalue=0, offsetonreverse=0, accelerationinitlong=0.02, accelerationlong=0.02, accelerationmaxlong=0.2, accelerationinitshort=0.02, accelerationshort=0.02, accelerationmaxshort=0.2)` - 抛物线SAR扩展版
- `HT_TRENDLINE(close)` - 希尔伯特变换趋势线

#### 动量指标 (Momentum Indicators) - 30个函数
- `ADX(high, low, close, timeperiod=14)` - 平均趋向指数
- `ADXR(high, low, close, timeperiod=14)` - 平均趋向指数评级
- `APO(close, fastperiod=12, slowperiod=26, matype=0)` - 绝对价格振荡器
- `AROON(high, low, timeperiod=14)` - 阿隆指标
- `AROONOSC(high, low, timeperiod=14)` - 阿隆振荡器
- `BOP(open, high, low, close)` - 均势指标
- `CCI(high, low, close, timeperiod=14)` - 顺势指标
- `CMO(close, timeperiod=14)` - 钱德动量摆动指标
- `DX(high, low, close, timeperiod=14)` - 趋向指数
- `MACD(close, fastperiod=12, slowperiod=26, signalperiod=9)` - MACD指标
- `MACDEXT(close, fastperiod=12, fastmatype=0, slowperiod=26, slowmatype=0, signalperiod=9, signalmatype=0)` - MACD扩展版
- `MACDFIX(close, signalperiod=9)` - MACD固定12/26
- `MFI(high, low, close, volume, timeperiod=14)` - 资金流量指数
- `MINUS_DI(high, low, close, timeperiod=14)` - 负向指标
- `MINUS_DM(high, low, timeperiod=14)` - 负向运动
- `MOM(close, timeperiod=10)` - 动量指标
- `PLUS_DI(high, low, close, timeperiod=14)` - 正向指标
- `PLUS_DM(high, low, timeperiod=14)` - 正向运动
- `PPO(close, fastperiod=12, slowperiod=26, matype=0)` - 价格振荡器
- `ROC(close, timeperiod=10)` - 变动率
- `ROCP(close, timeperiod=10)` - 变动率百分比
- `ROCR(close, timeperiod=10)` - 变动率比率
- `ROCR100(close, timeperiod=10)` - 变动率比率100倍
- `RSI(close, timeperiod=14)` - 相对强弱指数
- `STOCH(high, low, close, fastk_period=5, slowk_period=3, slowk_matype=0, slowd_period=3, slowd_matype=0)` - 随机指标
- `STOCHF(high, low, close, fastk_period=5, fastd_period=3, fastd_matype=0)` - 快速随机指标
- `STOCHRSI(close, timeperiod=14, fastk_period=5, fastd_period=3, fastd_matype=0)` - 随机RSI
- `TRIX(close, timeperiod=30)` - 三重指数平滑平均
- `ULTOSC(high, low, close, timeperiod1=7, timeperiod2=14, timeperiod3=28)` - 终极振荡器
- `WILLR(high, low, close, timeperiod=14)` - 威廉指标

#### 成交量指标 (Volume Indicators) - 3个函数
- `AD(high, low, close, volume)` - 累积/派发线
- `ADOSC(high, low, close, volume, fastperiod=3, slowperiod=10)` - 累积/派发震荡器
- `OBV(close, volume)` - 能量潮指标

#### 波动率指标 (Volatility Indicators) - 4个函数
- `ATR(high, low, close, timeperiod=14)` - 真实波动幅度
- `NATR(high, low, close, timeperiod=14)` - 标准化真实波动幅度
- `TRANGE(high, low, close)` - 真实范围
- `BBANDS(close, timeperiod=5, nbdevup=2, nbdevdn=2, matype=0)` - 布林带

#### 价格变换 (Price Transform) - 4个函数
- `AVGPRICE(open, high, low, close)` - 平均价格
- `MEDPRICE(high, low)` - 中位价格
- `TYPPRICE(high, low, close)` - 典型价格
- `WCLPRICE(high, low, close)` - 加权收盘价

#### 周期指标 (Cycle Indicators) - 5个函数
- `HT_DCPERIOD(close)` - 希尔伯特变换主导周期
- `HT_DCPHASE(close)` - 希尔伯特变换主导周期相位
- `HT_PHASOR(close)` - 希尔伯特变换相量组件
- `HT_SINE(close)` - 希尔伯特变换正弦波
- `HT_TRENDMODE(close)` - 希尔伯特变换趋势模式

#### 模式识别 (Pattern Recognition) - 61个函数
- `CDL2CROWS(open, high, low, close)` - 两只乌鸦
- `CDL3BLACKCROWS(open, high, low, close)` - 三只黑乌鸦
- `CDL3INSIDE(open, high, low, close)` - 三内部上升和下降
- `CDL3LINESTRIKE(open, high, low, close)` - 三线打击
- `CDL3OUTSIDE(open, high, low, close)` - 三外部上升和下降
- `CDL3STARSINSOUTH(open, high, low, close)` - 南方三星
- `CDL3WHITESOLDIERS(open, high, low, close)` - 三个白兵
- `CDLABANDONEDBABY(open, high, low, close, penetration=0.3)` - 弃婴
- `CDLADVANCEBLOCK(open, high, low, close)` - 大敌当前
- `CDLBELTHOLD(open, high, low, close)` - 捉腰带线
- `CDLBREAKAWAY(open, high, low, close)` - 脱离
- `CDLCLOSINGMARUBOZU(open, high, low, close)` - 收盘缺影线
- `CDLCONCEALBABYSWALL(open, high, low, close)` - 藏婴吞没
- `CDLCOUNTERATTACK(open, high, low, close)` - 反击线
- `CDLDARKCLOUDCOVER(open, high, low, close, penetration=0.5)` - 乌云压顶
- `CDLDOJI(open, high, low, close)` - 十字星
- `CDLDOJISTAR(open, high, low, close)` - 十字星
- `CDLDRAGONFLYDOJI(open, high, low, close)` - 蜻蜓十字星
- `CDLENGULFING(open, high, low, close)` - 吞没模式
- `CDLEVENINGDOJISTAR(open, high, low, close, penetration=0.3)` - 黄昏十字星
- `CDLEVENINGSTAR(open, high, low, close, penetration=0.3)` - 黄昏星
- `CDLGAPSIDESIDEWHITE(open, high, low, close)` - 向上/下跳空并列阳线
- `CDLGRAVESTONEDOJI(open, high, low, close)` - 墓碑十字星
- `CDLHAMMER(open, high, low, close)` - 锤头
- `CDLHANGINGMAN(open, high, low, close)` - 上吊线
- `CDLHARAMI(open, high, low, close)` - 母子线
- `CDLHARAMICROSS(open, high, low, close)` - 十字孕线
- `CDLHIGHWAVE(open, high, low, close)` - 风高浪大线
- `CDLHIKKAKE(open, high, low, close)` - 陷阱
- `CDLHIKKAKEMOD(open, high, low, close)` - 修正陷阱
- `CDLHOMINGPIGEON(open, high, low, close)` - 家鸽
- `CDLIDENTICAL3CROWS(open, high, low, close)` - 相同三乌鸦
- `CDLINNECK(open, high, low, close)` - 颈内线
- `CDLINVERTEDHAMMER(open, high, low, close)` - 倒锤头
- `CDLKICKING(open, high, low, close)` - 反冲形态
- `CDLKICKINGBYLENGTH(open, high, low, close)` - 由较长缺影线决定的反冲形态
- `CDLLADDERBOTTOM(open, high, low, close)` - 梯底
- `CDLLONGLEGGEDDOJI(open, high, low, close)` - 长脚十字星
- `CDLLONGLINE(open, high, low, close)` - 长蜡烛线
- `CDLMARUBOZU(open, high, low, close)` - 光头光脚/缺影线
- `CDLMATCHINGLOW(open, high, low, close)` - 相同低价
- `CDLMATHOLD(open, high, low, close, penetration=0.5)` - 铺垫
- `CDLMORNINGDOJISTAR(open, high, low, close, penetration=0.3)` - 早晨十字星
- `CDLMORNINGSTAR(open, high, low, close, penetration=0.3)` - 早晨星
- `CDLONNECK(open, high, low, close)` - 颈上线
- `CDLPIERCING(open, high, low, close)` - 刺透形态
- `CDLRICKSHAWMAN(open, high, low, close)` - 黄包车夫
- `CDLRISEFALL3METHODS(open, high, low, close)` - 上升/下降三法
- `CDLSEPARATINGLINES(open, high, low, close)` - 分离线
- `CDLSHOOTINGSTAR(open, high, low, close)` - 射击星
- `CDLSHORTLINE(open, high, low, close)` - 短蜡烛线
- `CDLSPINNINGTOP(open, high, low, close)` - 纺锤
- `CDLSTALLEDPATTERN(open, high, low, close)` - 停顿形态
- `CDLSTICKSANDWICH(open, high, low, close)` - 条形三明治
- `CDLTAKURI(open, high, low, close)` - 探水竿
- `CDLTASUKIGAP(open, high, low, close)` - 跳空并列阴阳线
- `CDLTHRUSTING(open, high, low, close)` - 插入
- `CDLTRISTAR(open, high, low, close)` - 三星
- `CDLUNIQUE3RIVER(open, high, low, close)` - 奇特三河床
- `CDLUPSIDEGAP2CROWS(open, high, low, close)` - 向上跳空的两只乌鸦
- `CDLXSIDEGAP3METHODS(open, high, low, close)` - 上升/下降跳空三法

#### 统计函数 (Statistic Functions) - 11个函数
- `BETA(high, low, timeperiod=5)` - 贝塔系数
- `CORREL(high, low, timeperiod=30)` - 皮尔逊相关系数
- `LINEARREG(close, timeperiod=14)` - 线性回归
- `LINEARREG_ANGLE(close, timeperiod=14)` - 线性回归角度
- `LINEARREG_INTERCEPT(close, timeperiod=14)` - 线性回归截距
- `LINEARREG_SLOPE(close, timeperiod=14)` - 线性回归斜率
- `STDDEV(close, timeperiod=5, nbdev=1)` - 标准偏差
- `TSF(close, timeperiod=14)` - 时间序列预测
- `VAR(close, timeperiod=5, nbdev=1)` - 方差

#### 数学变换 (Math Transform) - 15个函数
- `ACOS(close)` - 反余弦
- `ASIN(close)` - 反正弦
- `ATAN(close)` - 反正切
- `CEIL(close)` - 向上舍入
- `COS(close)` - 余弦
- `COSH(close)` - 双曲余弦
- `EXP(close)` - 指数
- `FLOOR(close)` - 向下舍入
- `LN(close)` - 自然对数
- `LOG10(close)` - 常用对数
- `SIN(close)` - 正弦
- `SINH(close)` - 双曲正弦
- `SQRT(close)` - 平方根
- `TAN(close)` - 正切
- `TANH(close)` - 双曲正切

#### 数学运算符 (Math Operators) - 8个函数
- `ADD(high, low)` - 加法
- `DIV(high, low)` - 除法
- `MAX(close, timeperiod=30)` - 最高值
- `MAXINDEX(close, timeperiod=30)` - 最高值索引
- `MIN(close, timeperiod=30)` - 最低值
- `MININDEX(close, timeperiod=30)` - 最低值索引
- `MINMAX(close, timeperiod=30)` - 最低和最高值
- `MINMAXINDEX(close, timeperiod=30)` - 最低和最高值索引
- `MULT(high, low)` - 乘法
- `SUB(high, low)` - 减法
- `SUM(close, timeperiod=30)` - 求和

## 3. pandas (v2.3.1) - 数据处理

### 核心类和方法：
- `DataFrame(data, index, columns)` - 创建数据框
- `read_csv(filepath)` - 读取CSV文件
- `to_datetime(arg)` - 时间转换
- `DataFrame.dropna()` - 删除空值
- `DataFrame.fillna(value)` - 填充空值
- `DataFrame.rolling(window)` - 滚动窗口计算
- `DataFrame.resample(rule)` - 时间序列重采样

## 4. numpy (v2.3.1) - 数值计算

### 核心函数：
- `array(object)` - 创建数组
- `zeros(shape)` - 创建零数组
- `ones(shape)` - 创建全1数组
- `mean(a, axis)` - 计算均值
- `std(a, axis)` - 计算标准差
- `max(a, axis)` - 最大值
- `min(a, axis)` - 最小值
- `where(condition, x, y)` - 条件选择

## 5. requests (v2.32.4) - HTTP请求

### 核心方法：
- `get(url, params, headers, timeout)` - GET请求
- `post(url, data, json, headers, timeout)` - POST请求
- `Response.json()` - 解析JSON响应
- `Response.status_code` - 响应状态码
- `Response.text` - 响应文本内容

## 6. flask (v3.1.1) - Web框架

### 核心类和装饰器：
- `Flask(__name__)` - 创建Flask应用
- `@app.route(rule, methods)` - 路由装饰器
- `render_template(template_name, **context)` - 渲染模板
- `request.form` - 获取表单数据
- `request.args` - 获取URL参数
- `jsonify(data)` - 返回JSON响应
- `redirect(location)` - 重定向

## 7. schedule (v1.2.2) - 任务调度

### 核心方法：
- `every(interval).seconds.do(job)` - 每N秒执行
- `every(interval).minutes.do(job)` - 每N分钟执行
- `every().hour.do(job)` - 每小时执行
- `every().day.at("10:30").do(job)` - 每天定时执行
- `run_pending()` - 运行待执行任务
- `cancel_job(job)` - 取消任务

## 8. sqlite3 (Python内置) - 数据库

### 核心方法：
- `connect(database)` - 连接数据库
- `cursor()` - 创建游标
- `execute(sql, parameters)` - 执行SQL
- `fetchall()` - 获取所有结果
- `fetchone()` - 获取单个结果
- `commit()` - 提交事务
- `close()` - 关闭连接

## 9. logging (Python内置) - 日志记录

### 核心方法：
- `basicConfig(level, format, filename)` - 基础配置
- `getLogger(name)` - 获取日志器
- `debug(msg)` - 调试日志
- `info(msg)` - 信息日志
- `warning(msg)` - 警告日志
- `error(msg)` - 错误日志
- `critical(msg)` - 严重错误日志

## 10. threading (Python内置) - 多线程

### 核心类和方法：
- `Thread(target, args, kwargs)` - 创建线程
- `start()` - 启动线程
- `join(timeout)` - 等待线程结束
- `Lock()` - 创建锁
- `acquire()` - 获取锁
- `release()` - 释放锁
