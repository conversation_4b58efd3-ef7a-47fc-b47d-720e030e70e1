"""
智能猎手引擎 - Smart Hunter Engine
替代deprecated的OpenPositionEngine，使用优化的提示词系统
专门负责开仓决策和机会识别
"""

import json
import logging
import requests
import time
from typing import Dict, Any, Optional
from .advanced_opening_engine_prompt import AdvancedOpeningEnginePrompt
from src.utils.ai_json_parser import AIJsonParser


class SmartHunterEngine:
    """
    智能猎手引擎
    
    使用先进的提示词工程技术，专注于：
    - 多时间周期趋势分析
    - 动态置信度参数计算
    - 智能风险适应性管理
    - 机会识别和开仓决策
    """
    
    def __init__(self, config_manager: Optional[Any] = None):
        """初始化智能猎手引擎"""
        self.logger = logging.getLogger(__name__)
        self.config_manager = config_manager
        self.deepseek_api_key = None
        self.deepseek_base_url = "https://api.deepseek.com/v1/chat/completions"
        self.json_parser = AIJsonParser(self.logger)
        
        # 初始化提示词生成器
        self.prompt_generator = AdvancedOpeningEnginePrompt()
        
        # 尝试加载配置，失败时优雅降级
        self._load_config()
        
        self.logger.info("智能猎手引擎初始化完成")
    
    def _load_config(self):
        """加载配置和API密钥 - 优先使用ConfigManager"""
        if self.config_manager:
            self.deepseek_api_key = self.config_manager.get('credentials.deepseek.api_key')
        else:
            # 备用方案：从环境变量或文件加载
            self.logger.warning("智能猎手引擎 - 未提供ConfigManager，尝试从环境变量或文件加载")
            import os
            self.deepseek_api_key = os.getenv('DEEPSEEK_API_KEY')
            
            if not self.deepseek_api_key:
                try:
                    config_file = 'config/api_keys.json'
                    if os.path.exists(config_file):
                        import json
                        with open(config_file, 'r', encoding='utf-8') as f:
                            config = json.load(f)
                            self.deepseek_api_key = config.get('deepseek_api_key')
                except Exception:
                    pass
        
        if self.deepseek_api_key:
            self.logger.info("智能猎手引擎 - API密钥加载成功")
        else:
            self.logger.warning("智能猎手引擎 - 未找到API密钥，AI功能将不可用")
    
    def _call_ai_with_retry(self, prompt: str, max_retries: int = 3, timeout: int = 30) -> str:
        """带重试机制的AI调用"""
        if not self.deepseek_api_key:
            raise ValueError("DeepSeek API密钥未配置，无法调用AI")
            
        headers = {
            'Authorization': f'Bearer {self.deepseek_api_key}',
            'Content-Type': 'application/json'
        }
        
        # 使用低温度确保JSON格式准确性
        data = {
            'model': 'deepseek-chat',
            'messages': [{'role': 'user', 'content': prompt}],
            'temperature': 0.1,  # 低温度确保格式一致性
            'max_tokens': 1000
        }
        
        for attempt in range(max_retries + 1):
            try:
                self.logger.info(f"智能猎手 - AI调用尝试 {attempt + 1}/{max_retries + 1}")
                response = requests.post(self.deepseek_base_url, headers=headers, json=data, timeout=timeout)
                response.raise_for_status()
                
                result = response.json()
                ai_response = result['choices'][0]['message']['content'].strip()
                
                if ai_response:
                    return ai_response
                else:
                    raise ValueError("AI返回空响应")
                    
            except Exception as e:
                self.logger.warning(f"智能猎手 - AI调用失败 (尝试 {attempt + 1}/{max_retries + 1}): {e}")
                if attempt < max_retries:
                    wait_time = (attempt + 1) * 2  # 递增等待时间
                    self.logger.info(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                else:
                    raise Exception(f"智能猎手 - AI调用失败，已重试 {max_retries} 次: {e}")
    
    def _get_ai_settings(self) -> Dict[str, Any]:
        """获取AI调用设置"""
        try:
            if self.config_manager:
                ai_timeout = self.config_manager.get_setting('ai_settings', 'timeout') or 30
                ai_retries = self.config_manager.get_setting('ai_settings', 'retries') or 3
                
                return {
                    'ai_timeout': ai_timeout,
                    'ai_retries': ai_retries
                }
            else:
                return {
                    'ai_timeout': 30,
                    'ai_retries': 3
                }
        except Exception as e:
            self.logger.warning(f"获取AI设置失败，使用默认值: {e}")
            return {
                'ai_timeout': 30,
                'ai_retries': 3
            }
    
    def _fallback_decision(self, symbol: str, reason: str) -> Dict[str, Any]:
        """简化备用决策机制"""
        try:
            if self.config_manager:
                # 获取配置的默认参数
                pair_config = self.config_manager.get(f'trading_pairs.{symbol}', {})
                default_leverage = pair_config.get('max_leverage', 3)
                default_stop_loss = pair_config.get('stop_loss', 2.0)
                default_take_profit = pair_config.get('take_profit', 4.0)
            else:
                # 使用硬编码默认值
                default_leverage = 3
                default_stop_loss = 2.0
                default_take_profit = 4.0
            
            return {
                "decision": "HOLD",
                "confidence": 0.3,
                "reasoning": f"{reason}。采用保守策略，暂不开仓",
                "leverage": default_leverage,
                "stop_loss": default_stop_loss,
                "take_profit": default_take_profit,
                "investment_amount": 50,  # 默认投资金额
                "position_size": 0.05  # 保守仓位
            }
            
        except Exception as e:
            self.logger.error(f"备用决策失败: {e}")
            return {
                "decision": "HOLD",
                "confidence": 0.2,
                "reasoning": f"{reason}。系统异常，采用保守策略",
                "leverage": 1,
                "stop_loss": 2.0,
                "take_profit": 4.0,
                "investment_amount": 50,
                "position_size": 0.05
            }
    
    def make_open_decision(self, symbol: str, indicators: Dict[str, Any], 
                          market_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        做出开仓决策
        
        Args:
            symbol: 交易对符号
            indicators: 技术指标数据
            market_data: 市场数据
            
        Returns:
            包含开仓决策信息的字典，或None（如果失败）
        """
        try:
            # 检查是否有API密钥
            if not self.deepseek_api_key:
                self.logger.warning("DeepSeek API密钥未配置，使用备用决策")
                return self._fallback_decision(symbol, "API密钥未配置")
            
            # 获取AI调用设置
            ai_settings = self._get_ai_settings()
            
            # 记录分析开始
            current_price = float(market_data.get('last_price', 0))
            change_24h = float(market_data.get('change_24h', 0))
            
            self.logger.info(f"🎯 智能猎手开始分析: {symbol}")
            self.logger.info(f"  - 当前价格: {current_price}")
            self.logger.info(f"  - 24h涨跌: {change_24h:.2f}%")
            
            # 使用优化的提示词生成器创建开仓分析提示
            base_prompt = self.prompt_generator.create_dynamic_opening_prompt()
            
            # 格式化指标数据
            formatted_indicators = self.prompt_generator.format_market_data_for_analysis(indicators, market_data)
            
            # 步骤1: 先用replace安全地替换复杂的文本块
            prompt_with_indicators = base_prompt.replace("{indicators}", formatted_indicators)
            
            # 步骤2: 然后对剩余的简单占位符进行格式化
            prompt = prompt_with_indicators.format(
                symbol=symbol,
                current_price=current_price,
                change_24h=change_24h,
                volatility=abs(change_24h)
            )
            
            # 调用AI分析
            self.logger.info(f"🎯 智能猎手开始AI分析: {symbol}")
            ai_response = self._call_ai_with_retry(
                prompt,
                max_retries=ai_settings['ai_retries'],
                timeout=ai_settings['ai_timeout']
            )
            
            # 显示交互信息
            print(f"\n🎯 ===== {symbol} 智能猎手 → AI =====")
            print(f"📊 价格: {current_price} | 24h: {change_24h:.2f}%")
            print(f"📈 趋势: {indicators.get('multi_timeframe_analysis', {}).get('trend_alignment', 'UNKNOWN')}")
            print(f"⚡ 动量: {indicators.get('multi_timeframe_analysis', {}).get('momentum_strength', 'UNKNOWN')}")
            print(f"\n🤖 ===== {symbol} AI返回 =====")
            print(f"```json\n{ai_response}\n```")
            
            # 解析AI响应
            try:
                # 清理AI响应
                cleaned_response = ai_response.strip()
                if cleaned_response.startswith('```json'):
                    cleaned_response = cleaned_response[7:]
                if cleaned_response.endswith('```'):
                    cleaned_response = cleaned_response[:-3]
                
                decision = self.json_parser.parse(cleaned_response)
                if decision is None:
                    raise json.JSONDecodeError("JSON解析失败", ai_response, 0)
                
                # 获取置信度
                confidence_raw = decision.get('confidence', 0.5)
                try:
                    confidence = float(confidence_raw)
                except (ValueError, TypeError):
                    self.logger.warning(f"置信度转换失败: {confidence_raw}")
                    confidence = 0.5
                
                # 根据置信度计算动态参数
                if self.config_manager:
                    confidence_params = self.config_manager.calculate_confidence_based_parameters(confidence, symbol)
                else:
                    # 使用简化的置信度参数计算
                    confidence_params = {
                        'should_trade': confidence >= 0.4,
                        'leverage': max(1, min(5, confidence * 5)),
                        'stop_loss': max(1.0, min(5.0, (1.4 - confidence) * 5)),
                        'take_profit': max(2.0, min(10.0, confidence * 10)),
                        'position_ratio': max(0.01, min(0.1, confidence * 0.1)) * 100,
                        'position_size': max(0.01, min(0.1, confidence * 0.1))
                    }
                
                # 检查置信度是否足够开仓
                if not confidence_params['should_trade']:
                    print(f"\n⚠️ ===== {symbol} 置信度过低，不开仓 =====")
                    print(f"📊 置信度: {confidence:.2f}")
                    print(f"💡 建议: 继续观察，等待更好机会")
                    print(f"==============================================")
                    
                    return {
                        "decision": "HOLD",
                        "confidence": confidence,
                        "reasoning": decision.get('reasoning', '') + " [系统判断: 置信度不足，暂不开仓]",
                        "leverage": 1,
                        "stop_loss": 0,
                        "take_profit": 0,
                        "investment_amount": 0,
                        "position_size": 0
                    }
                
                # 构建增强决策
                enhanced_decision = {
                    "decision": decision.get('decision'),
                    "confidence": confidence,
                    "reasoning": decision.get('reasoning'),
                    "leverage": confidence_params['leverage'],
                    "stop_loss": confidence_params['stop_loss'],
                    "take_profit": confidence_params['take_profit'],
                    "investment_amount": confidence_params.get('investment_amount', 50), # 使用 get 提供默认值
                    "position_size": confidence_params.get('position_size', 0.05), # 使用 get 提供默认值
                    "confidence_params": confidence_params
                }
                
                # 显示决策结果
                print(f"\n🎯 ===== {symbol} 智能猎手决策结果 =====")
                print(f"🎯 决策: {enhanced_decision.get('decision')}")
                print(f"📊 置信度: {enhanced_decision.get('confidence'):.2f}")
                print(f"🧠 推理: {enhanced_decision.get('reasoning')}")
                print(f"💰 投资额: ${enhanced_decision.get('investment_amount', 0):.2f}")
                print(f"📏 仓位比例: {confidence_params.get('position_ratio', 0):.2f}%")
                print(f"⚖️ 杠杆: {enhanced_decision.get('leverage')}x")
                print(f"🛡️ 止损: {enhanced_decision.get('stop_loss')}%")
                print(f"🎯 止盈: {enhanced_decision.get('take_profit')}%")
                print(f"=" * 50)
                
                self.logger.info(f"智能猎手决策完成: {symbol} -> {enhanced_decision.get('decision')} (置信度: {enhanced_decision.get('confidence'):.2f})")
                return enhanced_decision
                
            except json.JSONDecodeError as e:
                self.logger.error(f"智能猎手 - AI响应JSON解析失败: {e}")
                self.logger.error(f"原始AI响应: {ai_response}")
                return self._fallback_decision(symbol, "AI响应解析失败")
                
        except Exception as e:
            self.logger.error(f"智能猎手决策失败: {e}")
            try:
                return self._fallback_decision(symbol, f"引擎异常: {str(e)}")
            except Exception as fallback_error:
                self.logger.error(f"备用决策也失败: {fallback_error}")
                return {
                    "decision": "HOLD",
                    "confidence": 0.2,
                    "reasoning": f"系统完全异常: {str(e)}，采用保守策略",
                    "leverage": 1,
                    "stop_loss": 0,
                    "take_profit": 0,
                    "investment_amount": 0,
                    "position_size": 0
                }