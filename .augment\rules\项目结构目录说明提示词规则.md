---
type: "agent_requested"
description: "Example description"
---
# 📁 DeepSeek驱动的全自动加密货币合约交易系统 - 项目结构目录说明提示词规则

## 🎯 规则目的
为AI助手提供精确的项目结构理解框架，确保在代码分析、修改、扩展时能够准确定位文件位置、理解模块关系、遵循架构设计原则。

## 📋 核心规则

### 1. 🏗️ 项目根目录结构理解规则

```
deepseek-crypto-trading-system/          # 项目根目录
├── 📂 src/                              # 核心源代码目录 - 所有业务逻辑模块
├── 📂 docs/                             # 文档目录 - 设计文档、API文档、说明文件
├── 📂 templates/                        # Web模板目录 - Flask Jinja2模板文件
├── 📂 logs/                             # 日志目录 - 系统运行日志、监控报告
├── 📂 tests/                            # 测试目录 - 单元测试、集成测试文件
├── 📂 venv/                             # 虚拟环境目录 - Python依赖隔离环境
├── 📄 main.py                           # 主启动文件 - 系统入口点
├── 📄 requirements.txt                  # 依赖清单 - Python包依赖列表
├── 📄 requirements-freeze.txt           # 锁定版本 - 实际安装的包版本
├── 📄 credentials.db                    # 配置数据库 - SQLite3存储API密钥和配置
└── 📄 ta_lib-0.6.3-cp311-cp311-win_amd64.whl  # TA-Lib库文件
```

### 2. 🧩 src/ 核心模块架构规则

#### 2.1 模块分层架构
```
src/
├── 🔧 config_manager.py               # 配置层 - 数据库配置管理
├── 📊 data_fetcher.py                 # 数据层 - OKX API数据获取
├── 📈 indicator_calculator.py         # 计算层 - TA-Lib技术指标计算
├── 🧠 decision_engine.py              # 决策层 - DeepSeek AI决策引擎
├── 🧠 decision_engine_optimized.py    # 决策层 - 优化版决策引擎
├── ⚡ trade_executor.py               # 执行层 - 交易订单执行
├── 🛡️ risk_manager.py                # 风控层 - 风险管理和监控
├── 🎯 multi_contract_manager.py       # 调度层 - 多合约并行管理
├── 📝 logger_monitor.py               # 监控层 - 日志和系统监控
├── 🌐 web_dashboard.py                # 展示层 - Web仪表盘
├── 🎮 main_controller.py              # 控制层 - 主控制器
├── 🔍 open_position_engine.py         # 引擎层 - 开仓决策引擎
└── 👁️ position_monitor_engine.py      # 引擎层 - 持仓监控引擎
```

#### 2.2 模块依赖关系规则
```
main_controller.py (主控制器)
    ↓
multi_contract_manager.py (多合约管理)
    ↓
[data_fetcher.py] → [indicator_calculator.py] → [decision_engine.py] → [trade_executor.py]
    ↓                        ↓                         ↓                      ↓
config_manager.py ←→ risk_manager.py ←→ logger_monitor.py ←→ web_dashboard.py
```

### 3. 📁 目录功能定位规则

#### 3.1 docs/ 文档目录规则
- `README.md` - 项目总体说明，安装部署指南
- `design.md` - 系统架构设计文档
- `library_analysis.md` - 依赖库分析文档
- `AI方案架构师/` - AI相关设计方案目录

#### 3.2 templates/ 模板目录规则
- `index.html` - 主仪表盘页面模板
- `test.html` - 测试页面模板
- 所有模板使用Jinja2语法，与Flask框架集成

#### 3.3 logs/ 日志目录规则
- `trading_system.log` - 主系统日志文件
- `alerts.json` - 报警信息JSON格式存储
- `monitoring_report.json` - 监控报告JSON格式存储

#### 3.4 tests/ 测试目录规则
- 按功能模块命名：`test_[模块名].py`
- 按场景命名：`test_[场景描述].py`
- 集成测试：`test_[complete|full|all]_*.py`

### 4. 🔍 文件定位规则

#### 4.1 配置相关
- API密钥配置：`credentials.db` (SQLite3数据库)
- 系统参数配置：`src/config_manager.py`
- 依赖配置：`requirements.txt`, `requirements-freeze.txt`

#### 4.2 核心业务逻辑
- 数据获取：`src/data_fetcher.py`
- 技术分析：`src/indicator_calculator.py`
- AI决策：`src/decision_engine.py`
- 交易执行：`src/trade_executor.py`
- 风险控制：`src/risk_manager.py`

#### 4.3 系统控制
- 主入口：`main.py`
- 主控制器：`src/main_controller.py`
- 多合约管理：`src/multi_contract_manager.py`

#### 4.4 监控展示
- 日志监控：`src/logger_monitor.py`
- Web界面：`src/web_dashboard.py`
- 模板文件：`templates/`

### 5. 🎯 AI助手操作规则

#### 5.1 文件修改规则
1. **配置修改**：优先修改`src/config_manager.py`，避免直接修改数据库
2. **功能扩展**：在对应功能模块文件中扩展，保持单一职责原则
3. **新增模块**：在`src/`目录下创建，遵循命名规范
4. **测试文件**：在`tests/`目录下创建对应测试文件

#### 5.2 依赖管理规则
1. **新增依赖**：使用包管理器安装，不直接修改requirements.txt
2. **版本锁定**：安装后更新requirements-freeze.txt
3. **特殊依赖**：如TA-Lib使用wheel文件安装

#### 5.3 架构遵循规则
1. **分层原则**：严格按照数据层→计算层→决策层→执行层的顺序
2. **模块独立**：每个模块职责单一，接口清晰
3. **配置集中**：所有配置通过config_manager统一管理
4. **日志统一**：所有日志通过logger_monitor统一处理

### 6. 🚨 禁止操作规则

#### 6.1 严禁直接操作
- 直接修改`credentials.db`数据库文件
- 绕过config_manager直接读取配置
- 跨层级调用（如数据层直接调用执行层）
- 修改虚拟环境目录`venv/`

#### 6.2 严禁破坏结构
- 改变核心目录结构
- 合并功能模块文件
- 删除关键配置文件
- 破坏模块间依赖关系

### 7. 🔧 扩展开发规则

#### 7.1 新增功能模块
1. 在`src/`目录下创建新文件
2. 遵循现有命名规范
3. 实现标准接口
4. 添加对应测试文件

#### 7.2 修改现有模块
1. 先理解模块在架构中的位置
2. 确认修改不会破坏依赖关系
3. 保持接口兼容性
4. 更新相关测试

#### 7.3 配置扩展
1. 通过config_manager添加新配置项
2. 在数据库中添加对应表或字段
3. 更新配置验证逻辑
4. 添加配置文档说明

## 📝 使用示例

### 示例1：添加新的技术指标
```
位置：src/indicator_calculator.py
依赖：TA-Lib库
测试：tests/test_indicator_calculator.py
配置：通过config_manager添加指标参数
```

### 示例2：扩展Web界面功能
```
后端：src/web_dashboard.py
前端：templates/index.html
静态资源：static/目录（如需要）
测试：tests/test_web_dashboard.py
```

### 示例3：添加新的风控规则
```
位置：src/risk_manager.py
配置：config_manager中添加风控参数
日志：通过logger_monitor记录风控事件
测试：tests/test_risk_manager.py
```

## 8. 📊 数据流向规则

### 8.1 主要数据流
```
OKX API → data_fetcher.py → indicator_calculator.py → decision_engine.py → trade_executor.py → OKX API
    ↓              ↓                    ↓                     ↓                  ↓
credentials.db ← config_manager.py → risk_manager.py → logger_monitor.py → logs/
    ↑                                   ↓                     ↓              ↓
web_dashboard.py ← multi_contract_manager.py ← main_controller.py ← main.py
```

### 8.2 配置数据流
```
credentials.db → config_manager.py → [所有模块]
    ↑                    ↓
web_dashboard.py → 配置更新 → 数据库持久化
```

### 8.3 日志数据流
```
[所有模块] → logger_monitor.py → logs/trading_system.log
    ↓                ↓              ↓
web_dashboard.py ← 实时监控 ← alerts.json/monitoring_report.json
```

## 9. 🔄 模块交互规则

### 9.1 标准调用链
1. **数据获取链**：`main_controller` → `multi_contract_manager` → `data_fetcher`
2. **分析决策链**：`data_fetcher` → `indicator_calculator` → `decision_engine`
3. **执行风控链**：`decision_engine` → `trade_executor` → `risk_manager`
4. **监控日志链**：`所有模块` → `logger_monitor` → `web_dashboard`

### 9.2 配置访问规则
- 所有模块通过`config_manager`获取配置
- 禁止直接访问`credentials.db`
- 配置更新必须通过`config_manager`的标准接口

### 9.3 错误处理规则
- 所有异常必须通过`logger_monitor`记录
- 关键错误触发`risk_manager`的保护机制
- 系统级错误通过`web_dashboard`展示

## 10. 🎨 代码风格规则

### 10.1 文件命名规则
- 模块文件：`功能_类型.py` (如：`data_fetcher.py`, `risk_manager.py`)
- 测试文件：`test_模块名.py` (如：`test_data_fetcher.py`)
- 配置文件：小写+下划线 (如：`requirements.txt`)
- 文档文件：中文描述性命名 (如：`项目结构目录说明提示词规则.md`)

### 10.2 类命名规则
- 主要类名与文件名对应：`DataFetcher`, `RiskManager`, `ConfigManager`
- 使用驼峰命名法：`MultiContractManager`, `DecisionEngine`
- 引擎类后缀：`Engine` (如：`OpenPositionEngine`)

### 10.3 方法命名规则
- 使用下划线分隔：`get_kline_data()`, `calculate_indicators()`
- 动作性方法：`fetch_`, `calculate_`, `execute_`, `monitor_`
- 获取性方法：`get_`, `load_`, `read_`
- 设置性方法：`set_`, `update_`, `save_`

## 11. 🔐 安全规则

### 11.1 敏感信息处理
- API密钥存储：仅在`credentials.db`中加密存储
- 配置访问：仅通过`config_manager`的安全接口
- 日志记录：禁止在日志中记录敏感信息
- Web展示：敏感信息必须脱敏显示

### 11.2 文件权限规则
- `credentials.db`：仅所有者可读写
- 配置文件：仅所有者可读写
- 日志文件：所有者可读写，组可读
- 代码文件：标准权限

## 12. 🚀 性能优化规则

### 12.1 缓存策略
- 配置缓存：`config_manager`内存缓存，定期刷新
- 数据缓存：`data_fetcher`缓存K线数据，避免重复请求
- 指标缓存：`indicator_calculator`缓存计算结果
- Web缓存：`web_dashboard`缓存静态数据

### 12.2 并发处理
- 多合约：`multi_contract_manager`使用线程池
- 数据获取：异步请求，避免阻塞
- 指标计算：并行计算多个指标
- 日志写入：异步写入，避免影响主流程

## 13. 🧪 测试规则

### 13.1 测试文件组织
```
tests/
├── test_单元测试/          # 单个模块测试
├── test_集成测试/          # 模块间集成测试
├── test_场景测试/          # 特定场景测试
└── test_性能测试/          # 性能压力测试
```

### 13.2 测试命名规则
- 单元测试：`test_[模块名].py`
- 功能测试：`test_[功能描述].py`
- 场景测试：`test_[场景名称].py`
- 集成测试：`test_[complete|full|integration]_*.py`

### 13.3 测试覆盖规则
- 核心模块：100%覆盖率
- 工具模块：80%以上覆盖率
- 配置模块：关键路径100%覆盖
- Web模块：主要功能覆盖

## 14. 📈 监控规则

### 14.1 系统监控指标
- 性能指标：响应时间、内存使用、CPU使用率
- 业务指标：交易成功率、决策准确率、风控触发率
- 技术指标：API调用成功率、数据获取延迟、系统可用性

### 14.2 日志级别规则
- `DEBUG`：详细的调试信息，开发环境使用
- `INFO`：正常的业务流程信息
- `WARNING`：警告信息，需要关注但不影响运行
- `ERROR`：错误信息，影响功能但系统可继续运行
- `CRITICAL`：严重错误，系统无法继续运行

### 14.3 报警规则
- 立即报警：系统崩溃、API密钥失效、网络中断
- 延迟报警：性能下降、决策异常、风控触发
- 定期报告：日常运行统计、性能分析、收益报告

---

**🎯 核心原则**：理解架构 → 定位文件 → 遵循规范 → 保持一致性

**📋 使用检查清单**：
- [ ] 是否理解了目标文件在架构中的位置？
- [ ] 是否遵循了模块间的依赖关系？
- [ ] 是否通过正确的接口访问配置和数据？
- [ ] 是否添加了对应的测试文件？
- [ ] 是否更新了相关文档？
- [ ] 是否考虑了安全和性能影响？
