# DeepSeek驱动的全自动加密货币合约交易系统
# 核心模块包初始化文件

__version__ = "1.0.0"
__author__ = "AI工程师Cw"
__description__ = "DeepSeek Crypto Trading System"

# 导入核心模块
from .config_manager import ConfigManager
from .indicator_calculator import IndicatorCalculator
from .multi_contract_manager import MultiContractManager
from .logger_monitor import LoggerMonitor
from .web_dashboard import WebDashboard
from .main_controller import MainController

# 已弃用的模块移动到deprecated目录
# from .data_fetcher import DataFetcher  # 已移动到deprecated/
# from .trade_executor import TradeExecutor  # 已移动到deprecated/
# from .risk_manager import RiskManager  # 已移动到deprecated/

__all__ = [
    'ConfigManager',
    'IndicatorCalculator',
    'MultiContractManager',
    'LoggerMonitor',
    'WebDashboard',
    'MainController'
    # 已弃用的模块:
    # 'DataFetcher',  # 已移动到deprecated/
    # 'TradeExecutor',  # 已移动到deprecated/
    # 'RiskManager',  # 已移动到deprecated/
]
