# 大模型加密货币量化系统架构分析

## 1. 概述

本项目是一个由大型语言模型（DeepSeek）驱动的全自动加密货币合约交易系统。系统设计精良，采用模块化、分层化的架构，核心优势在于其先进的、基于角色的Prompt工程和动态自适应的AI决策流程。

## 2. 系统架构图

```mermaid
graph TD
    subgraph "用户接口层"
        A[main.py] --> B[WebDashboard];
        B -- API/WebSocket --> C[用户];
    end

    subgraph "核心控制层"
        D[MainController]
    end

    subgraph "服务层"
        E[ConfigManager]
        F[LoggerMonitor]
        G[DataFetcher]
        H[RiskManager]
        I[IndicatorCalculator]
        J[TradeExecutor]
    end

    subgraph "交易逻辑层"
        K[MultiContractManager]
    end

    subgraph "AI决策层"
        L[SmartHunterEngine]
        M[RiskGuardianEngine]
        N[AdvancedOpeningEnginePrompt]
        O[AdvancedPositionMonitorPrompt]
        P[DeepSeek API]
    end

    A --> D;
    D -- 初始化/协调 --> E;
    D -- 初始化/协调 --> F;
    D -- 初始化/协调 --> B;

    D -- 启动 --> K;

    K -- 使用 --> G;
    K -- 使用 --> I;
    K -- 使用 --> H;
    K -- 使用 --> J;

    K -- 无持仓时调用 --> L;
    K -- 有持仓时调用 --> M;

    L -- 生成Prompt --> N;
    M -- 生成Prompt --> O;

    L -- 调用 --> P;
    M -- 调用 --> P;
    
    E -- 读/写 --> Q[credentials.db];
    I -- 使用 --> G;
    J -- 使用 --> G;
    H -- 使用 --> E;
```

## 3. 核心模块详解

### 3.1. `MainController` (主控制器)
- **职责**: 系统的“大脑”，负责初始化、协调和管理所有其他模块的生命周期。
- **启动流程**: 由 `main.py` 启动，依次初始化 `ConfigManager`, `LoggerMonitor`, `DataFetcher`, `RiskManager`, `MultiContractManager` 和 `WebDashboard`。

### 3.2. `ConfigManager` (配置管理器)
- **职责**: 系统的配置中心，负责所有持久化配置的管理。
- **技术实现**: 使用 **SQLite** 数据库 (`credentials.db`) 存储API密钥、风险参数、交易对等信息，取代了传统的`.ini`或`.json`配置文件，更加健壮和灵活。
- **核心功能**:
    - `_load_config()`: 从数据库加载所有配置。
    - `set_setting()`: 更新配置并写入数据库。
    - `calculate_confidence_based_parameters()`: **关键函数**，根据AI返回的置信度动态计算杠杆、仓位大小、止盈止损等交易参数。

### 3.3. `MultiContractManager` (多合约管理器)
- **职责**: 交易逻辑的核心，负责并行处理多个交易对。
- **技术实现**: 使用多线程 (`ThreadPoolExecutor`) 实现并发交易。
- **核心逻辑**:
    1.  检查指定交易对的当前持仓状态。
    2.  **决策分流**:
        - 如果**无持仓**，调用 `SmartHunterEngine` (智能猎手) 寻找开仓机会。
        - 如果**有持仓**，调用 `RiskGuardianEngine` (风险守护者) 进行持仓监控。
    3.  将AI引擎返回的决策交由 `TradeExecutor` 执行。

### 3.4. AI决策层 (双引擎模型)
这是整个系统的灵魂，通过职责分离的双引擎模型实现高度智能化的决策。

#### `SmartHunterEngine` (智能猎手 - 开仓决策)
- **AI角色**: 15年经验的量化交易专家。
- **核心任务**: 识别高概率的开仓机会。
- **工作流程**:
    1.  调用 `AdvancedOpeningEnginePrompt` 生成一个复杂的、上下文丰富的提示词。
    2.  提示词中包含了对**市场环境**（趋势/震荡/突破）、**多维度评估**（技术/资金/情绪）和**动态风险**的结构化分析框架。
    3.  将格式化的市场数据和技术指标填入提示词。
    4.  调用DeepSeek API，获取JSON格式的决策（BUY/SELL/HOLD）和置信度。

#### `RiskGuardianEngine` (风险守护者 - 持仓监控)
- **AI角色**: 20年经验的风险管理专家。
- **核心任务**: 保护利润，控制风险。
- **工作流程**:
    1.  调用 `AdvancedPositionMonitorPrompt` 生成提示词。
    2.  提示词核心是**持仓生命周期管理**（新生/成长/成熟/风险期）和**多因子风险评估**（技术/市场/仓位风险）。
    3.  将实时的持仓盈亏、市场指标等数据填入提示词。
    4.  调用DeepSeek API，获取JSON格式的决策（HOLD/REDUCE/CLOSE）。

### 3.5. `RiskManager` (风险管理器)
- **职责**: 在交易执行前的最后一道防线。
- **核心功能**:
    - `check_trade_risk()`: 在下单前进行多维度检查，包括：
        - **资金充足性**: 确保账户余额足够。
        - **仓位风险**: 确保总仓位不超过设定的风险上限。
        - **并发持仓限制**: 确保同时持有的合约数量在可控范围内。

## 4. 总结

该系统是一个设计严谨、高度模块化的量化交易框架。其最大的亮点是将复杂的交易决策和风险管理逻辑，通过**角色扮演**和**结构化思考框架**的方式，“外包”给了大型语言模型。这种设计使得策略的迭代和优化变得异常灵活——只需要调整Prompt，就可以改变整个系统的交易行为，而无需修改大量的代码。