Metadata-Version: 2.1
Name: constantly
Version: 23.10.4
Summary: Symbolic constants in Python
Maintainer: Twisted Matrix Labs Developers
License: MIT
Project-URL: Homepage, https://github.com/twisted/constantly
Keywords: constants,enum,twisted
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.8
Description-Content-Type: text/x-rst
License-File: LICENSE

Constantly
==========

A library that provides symbolic constant support.  It includes collections and
constants with text, numeric, and bit flag values.  Originally
``twisted.python.constants`` from the `Twisted <https://twistedmatrix.com/>`_
project.


Installing
----------

constantly is available in `PyPI <https://pypi.org/project/constantly/>`_, and
can be installed via pip::

  $ pip install constantly


Documentation
-------------------------

Documentation is available at `<https://constantly.readthedocs.io/en/latest/>`_.


Tests
-----

To run tests::

    $ tox

This will run tests on Python 2.7, 3.3, 3.4, and PyPy, as well as doing
coverage and pyflakes checks.
