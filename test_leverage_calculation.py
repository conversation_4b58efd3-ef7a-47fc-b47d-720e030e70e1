#!/usr/bin/env python3
"""测试杠杆计算逻辑"""

import sys
import os
sys.path.append('src')

from config_manager import Config<PERSON><PERSON><PERSON>

def test_leverage_calculation():
    """测试杠杆计算过程"""
    
    # 初始化配置管理器
    config_manager = ConfigManager()
    
    # 测试参数
    test_cases = [
        {"confidence": 0.6, "symbol": "BTC-USDT-SWAP"},
        {"confidence": 0.5, "symbol": "BTC-USDT-SWAP"},
        {"confidence": 0.8, "symbol": "BTC-USDT-SWAP"},
        {"confidence": 0.9, "symbol": "BTC-USDT-SWAP"},
    ]
    
    print("=== 杠杆计算测试 ===")
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n测试案例 {i}:")
        print(f"  输入: 置信度={case['confidence']}, 交易对={case['symbol']}")
        
        result = config_manager.calculate_confidence_based_parameters(
            confidence=case['confidence'],
            symbol=case['symbol']
        )
        
        print(f"  结果: 杠杆={result.get('leverage')}x")
        print(f"        仓位比例={result.get('position_ratio')}%")
        print(f"        应交易={result.get('should_trade')}")
        
        if 'max_limits' in result:
            limits = result['max_limits']
            print(f"  限制: 最大杠杆={limits.get('max_leverage')}")
            print(f"        最大仓位={limits.get('max_position_size')}")

if __name__ == "__main__":
    test_leverage_calculation()