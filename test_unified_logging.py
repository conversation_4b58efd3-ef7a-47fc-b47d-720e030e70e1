#!/usr/bin/env python3
"""
统一日志系统测试程序
验证所有模块都使用统一的 [CryptoQuant] 日志格式
"""

import sys
import os
import time

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_unified_logging():
    """测试统一日志系统"""
    print("=" * 60)
    print("🧪 统一日志系统测试开始")
    print("=" * 60)
    
    # 1. 测试统一日志设置
    print("\n📋 步骤1: 测试统一日志设置...")
    try:
        from src.logger_monitor import setup_unified_logging, get_unified_logger
        
        # 设置统一日志
        setup_unified_logging()
        print("✅ 统一日志设置成功")
        
    except Exception as e:
        print(f"❌ 统一日志设置失败: {e}")
        return False
    
    # 2. 测试主控制器日志
    print("\n📋 步骤2: 测试主控制器日志...")
    try:
        logger = get_unified_logger('src.main_controller')
        logger.info("主控制器日志测试 - 信息级别")
        logger.warning("主控制器日志测试 - 警告级别")
        logger.error("主控制器日志测试 - 错误级别")
        print("✅ 主控制器日志测试完成")
        
    except Exception as e:
        print(f"❌ 主控制器日志测试失败: {e}")
    
    # 3. 测试多合约管理器日志
    print("\n📋 步骤3: 测试多合约管理器日志...")
    try:
        logger = get_unified_logger('src.multi_contract_manager')
        logger.info("多合约管理器日志测试 - 信息级别")
        logger.warning("多合约管理器日志测试 - 警告级别")
        logger.error("多合约管理器日志测试 - 错误级别")
        print("✅ 多合约管理器日志测试完成")
        
    except Exception as e:
        print(f"❌ 多合约管理器日志测试失败: {e}")
    
    # 4. 测试Web仪表板日志
    print("\n📋 步骤4: 测试Web仪表板日志...")
    try:
        logger = get_unified_logger('src.web_dashboard')
        logger.info("Web仪表板日志测试 - 信息级别")
        logger.warning("Web仪表板日志测试 - 警告级别")
        logger.error("Web仪表板日志测试 - 错误级别")
        print("✅ Web仪表板日志测试完成")
        
    except Exception as e:
        print(f"❌ Web仪表板日志测试失败: {e}")
    
    # 5. 测试AI JSON解析器日志
    print("\n📋 步骤5: 测试AI JSON解析器日志...")
    try:
        logger = get_unified_logger('src.utils.ai_json_parser')
        logger.info("AI JSON解析器日志测试 - 信息级别")
        logger.warning("AI JSON解析器日志测试 - 警告级别")
        logger.error("AI JSON解析器日志测试 - 错误级别")
        print("✅ AI JSON解析器日志测试完成")
        
    except Exception as e:
        print(f"❌ AI JSON解析器日志测试失败: {e}")
    
    # 6. 测试日志监控器本身
    print("\n📋 步骤6: 测试日志监控器日志...")
    try:
        logger = get_unified_logger('src.logger_monitor')
        logger.info("日志监控器日志测试 - 信息级别")
        logger.warning("日志监控器日志测试 - 警告级别")
        logger.error("日志监控器日志测试 - 错误级别")
        print("✅ 日志监控器日志测试完成")
        
    except Exception as e:
        print(f"❌ 日志监控器日志测试失败: {e}")
    
    # 7. 测试不同级别的日志输出
    print("\n📋 步骤7: 测试不同级别的日志输出...")
    try:
        test_logger = get_unified_logger('TEST_MODULE')
        
        print("\n🔍 以下应该看到统一格式的日志输出:")
        test_logger.debug("这是DEBUG级别日志 - 通常不显示")
        test_logger.info("这是INFO级别日志 - 正常信息")
        test_logger.warning("这是WARNING级别日志 - 警告信息")
        test_logger.error("这是ERROR级别日志 - 错误信息")
        test_logger.critical("这是CRITICAL级别日志 - 严重错误")
        
        print("✅ 不同级别日志测试完成")
        
    except Exception as e:
        print(f"❌ 不同级别日志测试失败: {e}")
    
    # 8. 测试多模块同时使用
    print("\n📋 步骤8: 测试多模块同时使用...")
    try:
        logger1 = get_unified_logger('MODULE_A')
        logger2 = get_unified_logger('MODULE_B')
        logger3 = get_unified_logger('MODULE_C')
        
        logger1.info("模块A的日志信息")
        logger2.warning("模块B的警告信息")
        logger3.error("模块C的错误信息")
        
        print("✅ 多模块同时使用测试完成")
        
    except Exception as e:
        print(f"❌ 多模块同时使用测试失败: {e}")
    
    # 9. 测试时间戳格式
    print("\n📋 步骤9: 测试时间戳格式...")
    try:
        test_logger = get_unified_logger('TIMESTAMP_TEST')
        
        print("\n🕐 测试时间戳格式（应包含毫秒）:")
        for i in range(3):
            test_logger.info(f"时间戳测试消息 {i+1}")
            time.sleep(0.1)  # 短暂延迟以显示不同时间戳
        
        print("✅ 时间戳格式测试完成")
        
    except Exception as e:
        print(f"❌ 时间戳格式测试失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 统一日志系统测试完成!")
    print("=" * 60)
    
    print("\n📊 测试总结:")
    print("• 所有日志都应该以 [CryptoQuant] 开头")
    print("• 时间戳格式: YYYY-MM-DD HH:MM:SS.mmm")
    print("• 日志级别应该对齐显示")
    print("• 模块名称应该显示完整路径")
    print("• 所有模块使用相同的日志格式")
    
    return True

if __name__ == "__main__":
    test_unified_logging()