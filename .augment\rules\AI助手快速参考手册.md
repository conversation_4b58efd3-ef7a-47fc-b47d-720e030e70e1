---
type: "agent_requested"
description: "Example description"
---
# 🤖 AI助手快速参考手册 - DeepSeek加密货币交易系统

## 🎯 快速定位指南

### 📁 我要修改什么功能？找哪个文件？

| 功能需求 | 主要文件 | 相关文件 | 测试文件 |
|---------|---------|---------|---------|
| **API密钥配置** | `src/config_manager.py` | `credentials.db` | `tests/test_config_manager.py` |
| **数据获取逻辑** | `src/data_fetcher.py` | `src/config_manager.py` | `tests/test_data_fetcher.py` |
| **技术指标计算** | `src/indicator_calculator.py` | - | `tests/test_indicator_calculator.py` |
| **AI决策逻辑** | `src/decision_engine.py` | `src/decision_engine_optimized.py` | `tests/test_ai_*.py` |
| **交易执行** | `src/trade_executor.py` | `src/risk_manager.py` | `tests/test_trade_*.py` |
| **风险控制** | `src/risk_manager.py` | `src/logger_monitor.py` | `tests/test_risk_*.py` |
| **多合约管理** | `src/multi_contract_manager.py` | `src/main_controller.py` | `tests/test_multi_contract_*.py` |
| **日志监控** | `src/logger_monitor.py` | `logs/` | `tests/test_logger_*.py` |
| **Web界面** | `src/web_dashboard.py` | `templates/` | `tests/test_web_*.py` |
| **系统启动** | `main.py` | `src/main_controller.py` | `tests/test_complete_*.py` |

### 🔧 常见操作快速指南

#### 1. 添加新的技术指标
```python
# 文件：src/indicator_calculator.py
# 位置：IndicatorCalculator类中添加新方法
def calculate_new_indicator(self, data, period=14):
    """添加新指标计算方法"""
    pass

# 配置：src/config_manager.py 添加指标参数
# 测试：tests/test_indicator_calculator.py 添加测试用例
```

#### 2. 修改AI决策逻辑
```python
# 文件：src/decision_engine.py
# 位置：DecisionEngine类的make_decision方法
# 注意：保持输入输出接口不变
# 测试：tests/test_ai_*.py 验证决策逻辑
```

#### 3. 添加新的风控规则
```python
# 文件：src/risk_manager.py
# 位置：RiskManager类中添加新的检查方法
# 配置：通过config_manager添加风控参数
# 日志：通过logger_monitor记录风控事件
```

#### 4. 扩展Web界面功能
```python
# 后端：src/web_dashboard.py 添加新的路由和处理逻辑
# 前端：templates/index.html 添加新的页面元素
# 静态资源：如需要，在static/目录添加CSS/JS文件
```

## 🚨 重要注意事项

### ❌ 绝对禁止的操作
1. **直接修改数据库文件** `credentials.db`
2. **绕过config_manager直接读取配置**
3. **跨层级调用**（如数据层直接调用执行层）
4. **修改虚拟环境目录** `venv/`
5. **在日志中记录敏感信息**（API密钥等）

### ✅ 必须遵循的规则
1. **所有配置通过config_manager访问**
2. **所有日志通过logger_monitor记录**
3. **所有错误处理要完整**
4. **新增功能必须添加测试**
5. **保持模块间接口稳定**

## 🔄 标准工作流程

### 1. 分析需求阶段
```
1. 确定功能属于哪个模块
2. 查看相关文件的现有代码
3. 理解模块在架构中的位置
4. 确认依赖关系和接口
```

### 2. 代码修改阶段
```
1. 先查看现有代码实现
2. 遵循现有代码风格
3. 保持接口兼容性
4. 添加必要的错误处理
5. 添加适当的日志记录
```

### 3. 测试验证阶段
```
1. 编写或更新测试用例
2. 运行相关测试确保功能正常
3. 检查是否影响其他模块
4. 验证配置和日志是否正确
```

### 4. 文档更新阶段
```
1. 更新相关注释和文档
2. 如有新配置项，更新配置说明
3. 如有新接口，更新API文档
```

## 📊 模块依赖速查表

### 核心依赖链
```
main.py
  ↓
src/main_controller.py
  ↓
src/multi_contract_manager.py
  ↓
src/data_fetcher.py → src/indicator_calculator.py → src/decision_engine.py → src/trade_executor.py
  ↓                        ↓                           ↓                        ↓
src/config_manager.py ← src/risk_manager.py ← src/logger_monitor.py ← src/web_dashboard.py
```

### 配置依赖
- **所有模块** → `src/config_manager.py` → `credentials.db`

### 日志依赖
- **所有模块** → `src/logger_monitor.py` → `logs/`

### Web依赖
- `src/web_dashboard.py` → `templates/` → 浏览器界面

## 🛠️ 调试指南

### 1. 系统启动问题
```
检查文件：main.py, src/main_controller.py
检查配置：credentials.db 是否存在且有效
检查日志：logs/trading_system.log
```

### 2. 数据获取问题
```
检查文件：src/data_fetcher.py
检查配置：OKX API密钥是否正确
检查网络：API连接是否正常
检查日志：查看API调用错误信息
```

### 3. 决策引擎问题
```
检查文件：src/decision_engine.py
检查配置：DeepSeek API密钥是否正确
检查输入：技术指标数据是否正确
检查输出：决策结果格式是否符合预期
```

### 4. 交易执行问题
```
检查文件：src/trade_executor.py
检查权限：OKX API是否有交易权限
检查余额：账户余额是否充足
检查风控：是否触发风控规则
```

### 5. Web界面问题
```
检查文件：src/web_dashboard.py, templates/
检查端口：5000端口是否被占用
检查模板：Jinja2模板语法是否正确
检查数据：后端数据是否正确传递
```

## 🔍 常用代码片段

### 1. 获取配置
```python
from src.config_manager import ConfigManager
config = ConfigManager()
api_key = config.get_okx_credentials()['api_key']
```

### 2. 记录日志
```python
from src.logger_monitor import LoggerMonitor
logger = LoggerMonitor()
logger.log_info("操作成功", {"detail": "具体信息"})
```

### 3. 风控检查
```python
from src.risk_manager import RiskManager
risk_manager = RiskManager()
if risk_manager.check_position_risk(position_data):
    # 执行交易
    pass
```

### 4. 数据获取
```python
from src.data_fetcher import DataFetcher
fetcher = DataFetcher()
kline_data = fetcher.get_kline_data("BTC-USDT-SWAP", "1m")
```

### 5. 指标计算
```python
from src.indicator_calculator import IndicatorCalculator
calculator = IndicatorCalculator()
indicators = calculator.calculate_all_indicators(kline_data)
```

## 📋 检查清单

### 代码修改前
- [ ] 是否理解了要修改的模块功能？
- [ ] 是否查看了现有代码实现？
- [ ] 是否了解了模块间的依赖关系？
- [ ] 是否确认了接口规范？

### 代码修改中
- [ ] 是否遵循了现有代码风格？
- [ ] 是否保持了接口兼容性？
- [ ] 是否添加了错误处理？
- [ ] 是否添加了日志记录？

### 代码修改后
- [ ] 是否编写了测试用例？
- [ ] 是否运行了相关测试？
- [ ] 是否检查了对其他模块的影响？
- [ ] 是否更新了相关文档？

---

**🎯 记住**：先理解，再修改，后测试，最后文档化！
